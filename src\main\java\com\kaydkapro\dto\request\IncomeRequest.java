package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Income;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Income Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class IncomeRequest {
    
    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    private String title;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;
    
    @NotBlank(message = "Category is required")
    @Size(max = 100, message = "Category must not exceed 100 characters")
    private String category;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;
    
    @NotNull(message = "Income date is required")
    private LocalDate incomeDate;
    
    @Size(max = 500, message = "Receipt image URL must not exceed 500 characters")
    private String receiptImage;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;
    
    /**
     * Convert DTO to Entity
     */
    public Income toEntity() {
        return Income.builder()
                .title(this.title)
                .amount(this.amount)
                .category(this.category)
                .description(this.description)
                .incomeDate(this.incomeDate)
                .receiptImage(this.receiptImage)
                .notes(this.notes)
                .build();
    }
}
