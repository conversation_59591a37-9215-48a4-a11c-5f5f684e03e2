package com.kaydkapro.service;

import com.kaydkapro.entity.User;
import com.kaydkapro.repository.UserRepository;
import com.kaydkapro.repository.RoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * User Service - Business logic for user management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final AuditLogService auditLogService;
    
    /**
     * Create a new user
     */
    public User createUser(User user, String createdByUserId) {
        log.info("Creating new user: {}", user.getUsername());
        
        // Validate unique constraints
        validateUserUniqueness(user);
        
        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // Set audit fields
        user.setCreatedBy(createdByUserId);
        user.setCreatedAt(LocalDateTime.now());
        user.setIsActive(true);
        
        User savedUser = userRepository.save(user);
        
        // Log audit
        auditLogService.logUserAction(
            createdByUserId, 
            "CREATE", 
            "USER", 
            savedUser.getId(), 
            savedUser.getUsername()
        );
        
        log.info("User created successfully: {}", savedUser.getId());
        return savedUser;
    }
    
    /**
     * Update existing user
     */
    public User updateUser(String userId, User userUpdates, String updatedByUserId) {
        log.info("Updating user: {}", userId);
        
        User existingUser = getUserById(userId);
        
        // Store old values for audit
        String oldEmail = existingUser.getEmail();
        String oldFirstName = existingUser.getFirstName();
        String oldLastName = existingUser.getLastName();
        
        // Validate unique constraints if email/username changed
        if (!existingUser.getEmail().equals(userUpdates.getEmail()) ||
            !existingUser.getUsername().equals(userUpdates.getUsername())) {
            validateUserUniqueness(userUpdates, userId);
        }
        
        // Update fields
        existingUser.setEmail(userUpdates.getEmail());
        existingUser.setFirstName(userUpdates.getFirstName());
        existingUser.setLastName(userUpdates.getLastName());
        existingUser.setPhone(userUpdates.getPhone());
        existingUser.setProfileImage(userUpdates.getProfileImage());
        existingUser.setUpdatedBy(updatedByUserId);
        existingUser.setUpdatedAt(LocalDateTime.now());
        
        User savedUser = userRepository.save(existingUser);
        
        // Log audit with changes
        auditLogService.logDataChange(
            updatedByUserId,
            "UPDATE",
            "USER",
            savedUser.getId(),
            savedUser.getUsername(),
            createOldValuesMap(oldEmail, oldFirstName, oldLastName),
            createNewValuesMap(savedUser.getEmail(), savedUser.getFirstName(), savedUser.getLastName())
        );
        
        log.info("User updated successfully: {}", savedUser.getId());
        return savedUser;
    }
    
    /**
     * Get user by ID
     */
    @Transactional(readOnly = true)
    public User getUserById(String userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
    }
    
    /**
     * Get user by username
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    /**
     * Get user by email
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    /**
     * Get user by username or email
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByUsernameOrEmail(String usernameOrEmail) {
        return userRepository.findByUsernameOrEmail(usernameOrEmail);
    }
    
    /**
     * Get all active users
     */
    @Transactional(readOnly = true)
    public List<User> getAllActiveUsers() {
        return userRepository.findByIsActiveTrue();
    }
    
    /**
     * Get users with pagination
     */
    @Transactional(readOnly = true)
    public Page<User> getUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }
    
    /**
     * Search users
     */
    @Transactional(readOnly = true)
    public Page<User> searchUsers(String searchTerm, Pageable pageable) {
        return userRepository.searchActiveUsers(searchTerm, pageable);
    }
    
    /**
     * Get users by role
     */
    @Transactional(readOnly = true)
    public List<User> getUsersByRole(String roleName) {
        return userRepository.findActiveUsersByRole(roleName);
    }
    
    /**
     * Activate/Deactivate user
     */
    public User toggleUserStatus(String userId, boolean isActive, String updatedByUserId) {
        log.info("Toggling user status: {} to {}", userId, isActive);
        
        User user = getUserById(userId);
        user.setIsActive(isActive);
        user.setUpdatedBy(updatedByUserId);
        user.setUpdatedAt(LocalDateTime.now());
        
        User savedUser = userRepository.save(user);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            isActive ? "ACTIVATE" : "DEACTIVATE",
            "USER",
            savedUser.getId(),
            savedUser.getUsername()
        );
        
        log.info("User status updated: {} - {}", savedUser.getId(), isActive);
        return savedUser;
    }
    
    /**
     * Add role to user
     */
    public User addRoleToUser(String userId, String roleId, String assignedByUserId) {
        log.info("Adding role {} to user {}", roleId, userId);
        
        User user = getUserById(userId);
        var role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RuntimeException("Role not found with id: " + roleId));
        
        // Check if user already has this role
        boolean hasRole = user.getRoles().stream()
                .anyMatch(userRole -> userRole.getRoleId().equals(roleId));
        
        if (hasRole) {
            throw new RuntimeException("User already has this role");
        }
        
        user.addRole(roleId, role.getName(), assignedByUserId);
        user.setUpdatedBy(assignedByUserId);
        user.setUpdatedAt(LocalDateTime.now());
        
        User savedUser = userRepository.save(user);
        
        // Log audit
        auditLogService.logUserAction(
            assignedByUserId,
            "ADD_ROLE",
            "USER",
            savedUser.getId(),
            savedUser.getUsername() + " - Role: " + role.getName()
        );
        
        log.info("Role added to user successfully: {} - {}", savedUser.getId(), role.getName());
        return savedUser;
    }
    
    /**
     * Remove role from user
     */
    public User removeRoleFromUser(String userId, String roleName, String removedByUserId) {
        log.info("Removing role {} from user {}", roleName, userId);
        
        User user = getUserById(userId);
        
        // Check if user has this role
        boolean hasRole = user.getRoles().stream()
                .anyMatch(userRole -> userRole.getRoleName().equals(roleName));
        
        if (!hasRole) {
            throw new RuntimeException("User does not have this role");
        }
        
        user.removeRole(roleName);
        user.setUpdatedBy(removedByUserId);
        user.setUpdatedAt(LocalDateTime.now());
        
        User savedUser = userRepository.save(user);
        
        // Log audit
        auditLogService.logUserAction(
            removedByUserId,
            "REMOVE_ROLE",
            "USER",
            savedUser.getId(),
            savedUser.getUsername() + " - Role: " + roleName
        );
        
        log.info("Role removed from user successfully: {} - {}", savedUser.getId(), roleName);
        return savedUser;
    }
    
    /**
     * Update last login time
     */
    public void updateLastLogin(String userId) {
        User user = getUserById(userId);
        user.setLastLogin(LocalDateTime.now());
        userRepository.save(user);
        
        // Log audit
        auditLogService.logUserAction(
            userId,
            "LOGIN",
            "USER",
            user.getId(),
            user.getUsername()
        );
    }
    
    /**
     * Change user password
     */
    public void changePassword(String userId, String oldPassword, String newPassword, String updatedByUserId) {
        log.info("Changing password for user: {}", userId);
        
        User user = getUserById(userId);
        
        // Verify old password
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("Invalid old password");
        }
        
        // Update password
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedBy(updatedByUserId);
        user.setUpdatedAt(LocalDateTime.now());
        
        userRepository.save(user);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            "CHANGE_PASSWORD",
            "USER",
            user.getId(),
            user.getUsername()
        );
        
        log.info("Password changed successfully for user: {}", userId);
    }
    
    /**
     * Delete user (soft delete)
     */
    public void deleteUser(String userId, String deletedByUserId) {
        log.info("Deleting user: {}", userId);
        
        User user = getUserById(userId);
        user.setIsActive(false);
        user.setUpdatedBy(deletedByUserId);
        user.setUpdatedAt(LocalDateTime.now());
        
        userRepository.save(user);
        
        // Log audit
        auditLogService.logUserAction(
            deletedByUserId,
            "DELETE",
            "USER",
            user.getId(),
            user.getUsername()
        );
        
        log.info("User deleted successfully: {}", userId);
    }
    
    /**
     * Get user statistics
     */
    @Transactional(readOnly = true)
    public UserStats getUserStats() {
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.countByIsActiveTrue();
        long adminUsers = userRepository.countByRole("ADMIN");
        long managerUsers = userRepository.countByRole("MANAGER");
        
        return UserStats.builder()
                .totalUsers(totalUsers)
                .activeUsers(activeUsers)
                .inactiveUsers(totalUsers - activeUsers)
                .adminUsers(adminUsers)
                .managerUsers(managerUsers)
                .build();
    }
    
    // Helper methods
    private void validateUserUniqueness(User user) {
        validateUserUniqueness(user, null);
    }
    
    private void validateUserUniqueness(User user, String excludeUserId) {
        if (excludeUserId != null) {
            if (userRepository.existsByUsernameAndIdNot(user.getUsername(), excludeUserId)) {
                throw new RuntimeException("Username already exists");
            }
            if (userRepository.existsByEmailAndIdNot(user.getEmail(), excludeUserId)) {
                throw new RuntimeException("Email already exists");
            }
        } else {
            if (userRepository.existsByUsername(user.getUsername())) {
                throw new RuntimeException("Username already exists");
            }
            if (userRepository.existsByEmail(user.getEmail())) {
                throw new RuntimeException("Email already exists");
            }
        }
    }
    
    private java.util.Map<String, Object> createOldValuesMap(String email, String firstName, String lastName) {
        return java.util.Map.of(
            "email", email,
            "firstName", firstName,
            "lastName", lastName
        );
    }
    
    private java.util.Map<String, Object> createNewValuesMap(String email, String firstName, String lastName) {
        return java.util.Map.of(
            "email", email,
            "firstName", firstName,
            "lastName", lastName
        );
    }
    
    /**
     * User statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class UserStats {
        private long totalUsers;
        private long activeUsers;
        private long inactiveUsers;
        private long adminUsers;
        private long managerUsers;
    }
}
