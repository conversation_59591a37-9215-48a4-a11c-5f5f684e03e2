package com.kaydkapro.controller;

import com.kaydkapro.dto.request.LoginRequest;
import com.kaydkapro.dto.request.RegisterRequest;
import com.kaydkapro.dto.response.LoginResponse;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.entity.User;
import com.kaydkapro.service.AuthService;
import com.kaydkapro.service.UserService;
import com.kaydkapro.security.JwtTokenProvider;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

/**
 * Authentication Controller - Handles user authentication and authorization
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {
    
    private final AuthenticationManager authenticationManager;
    private final UserService userService;
    private final AuthService authService;
    private final JwtTokenProvider tokenProvider;
    
    /**
     * User login
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@Valid @RequestBody LoginRequest loginRequest,
                                                           HttpServletRequest request) {
        log.info("Login attempt for user: {}", loginRequest.getUsernameOrEmail());
        
        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsernameOrEmail(),
                    loginRequest.getPassword()
                )
            );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // Generate JWT token
            String jwt = tokenProvider.generateToken(authentication);
            
            // Get user details
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userService.getUserById(userPrincipal.getId());
            
            // Update last login
            userService.updateLastLogin(user.getId());
            
            // Log login activity
            authService.logLoginActivity(user.getId(), getClientIpAddress(request), request.getHeader("User-Agent"));
            
            LoginResponse loginResponse = LoginResponse.builder()
                    .accessToken(jwt)
                    .tokenType("Bearer")
                    .userId(user.getId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .roles(user.getRoles().stream()
                            .map(role -> role.getRoleName())
                            .toList())
                    .profileImage(user.getProfileImage())
                    .build();
            
            log.info("User logged in successfully: {}", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("Login successful", loginResponse));
            
        } catch (Exception e) {
            log.error("Login failed for user: {} - {}", loginRequest.getUsernameOrEmail(), e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid username/email or password"));
        }
    }
    
    /**
     * User registration
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<LoginResponse>> register(@Valid @RequestBody RegisterRequest registerRequest,
                                                              HttpServletRequest request) {
        log.info("Registration attempt for user: {}", registerRequest.getUsername());
        
        try {
            // Register user
            User user = authService.registerUser(registerRequest);
            
            // Auto-login after registration
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    registerRequest.getUsername(),
                    registerRequest.getPassword()
                )
            );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // Generate JWT token
            String jwt = tokenProvider.generateToken(authentication);
            
            // Log registration activity
            authService.logLoginActivity(user.getId(), getClientIpAddress(request), request.getHeader("User-Agent"));
            
            LoginResponse loginResponse = LoginResponse.builder()
                    .accessToken(jwt)
                    .tokenType("Bearer")
                    .userId(user.getId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .roles(user.getRoles().stream()
                            .map(role -> role.getRoleName())
                            .toList())
                    .profileImage(user.getProfileImage())
                    .build();
            
            log.info("User registered and logged in successfully: {}", user.getUsername());
            return ResponseEntity.ok(ApiResponse.success("Registration successful", loginResponse));
            
        } catch (Exception e) {
            log.error("Registration failed for user: {} - {}", registerRequest.getUsername(), e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get current user information
     */
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<User>> getCurrentUser(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userService.getUserById(userPrincipal.getId());
            
            // Remove sensitive information
            user.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("User information retrieved", user));
            
        } catch (Exception e) {
            log.error("Failed to get current user: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get user information"));
        }
    }
    
    /**
     * Refresh JWT token
     */
    @PostMapping("/refresh-token")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Invalid token format"));
            }
            
            String token = authHeader.substring(7);
            
            if (!tokenProvider.validateToken(token)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Invalid or expired token"));
            }
            
            String userId = tokenProvider.getUserIdFromToken(token);
            User user = userService.getUserById(userId);
            
            // Generate new token
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                user.getUsername(), null, UserPrincipal.create(user).getAuthorities()
            );
            
            String newToken = tokenProvider.generateToken(authentication);
            
            LoginResponse loginResponse = LoginResponse.builder()
                    .accessToken(newToken)
                    .tokenType("Bearer")
                    .userId(user.getId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .roles(user.getRoles().stream()
                            .map(role -> role.getRoleName())
                            .toList())
                    .profileImage(user.getProfileImage())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Token refreshed successfully", loginResponse));
            
        } catch (Exception e) {
            log.error("Token refresh failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Token refresh failed"));
        }
    }
    
    /**
     * User logout
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<String>> logout(Authentication authentication) {
        try {
            if (authentication != null) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                authService.logLogoutActivity(userPrincipal.getId());
                SecurityContextHolder.clearContext();
            }
            
            return ResponseEntity.ok(ApiResponse.success("Logout successful", null));
            
        } catch (Exception e) {
            log.error("Logout failed: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.success("Logout completed", null));
        }
    }
    
    /**
     * Forgot password
     */
    @PostMapping("/forgot-password")
    public ResponseEntity<ApiResponse<String>> forgotPassword(@RequestParam String email) {
        try {
            authService.initiatePasswordReset(email);
            return ResponseEntity.ok(ApiResponse.success("Password reset email sent", null));
            
        } catch (Exception e) {
            log.error("Forgot password failed for email: {} - {}", email, e.getMessage());
            // Don't reveal if email exists or not for security
            return ResponseEntity.ok(ApiResponse.success("If the email exists, a password reset link has been sent", null));
        }
    }
    
    /**
     * Reset password
     */
    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<String>> resetPassword(@RequestParam String token,
                                                            @RequestParam String newPassword) {
        try {
            authService.resetPassword(token, newPassword);
            return ResponseEntity.ok(ApiResponse.success("Password reset successful", null));
            
        } catch (Exception e) {
            log.error("Password reset failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Invalid or expired reset token"));
        }
    }
    
    /**
     * Change password
     */
    @PostMapping("/change-password")
    public ResponseEntity<ApiResponse<String>> changePassword(@RequestParam String oldPassword,
                                                             @RequestParam String newPassword,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            userService.changePassword(userPrincipal.getId(), oldPassword, newPassword, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Password changed successfully", null));
            
        } catch (Exception e) {
            log.error("Password change failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Validate token
     */
    @GetMapping("/validate-token")
    public ResponseEntity<ApiResponse<Boolean>> validateToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.ok(ApiResponse.success("Token validation result", false));
            }
            
            String token = authHeader.substring(7);
            boolean isValid = tokenProvider.validateToken(token);
            
            return ResponseEntity.ok(ApiResponse.success("Token validation result", isValid));
            
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.success("Token validation result", false));
        }
    }
    
    // Helper methods
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
