package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Purchase Entity - Represents purchase orders and receipts
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "purchases")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Purchase {
    
    @Id
    private String id;
    
    @NotBlank(message = "Purchase number is required")
    @Size(max = 50, message = "Purchase number must not exceed 50 characters")
    @Indexed(unique = true)
    private String purchaseNumber;
    
    @NotNull(message = "Supplier information is required")
    private PurchaseSupplier supplier;
    
    @NotNull(message = "Warehouse information is required")
    private PurchaseWarehouse warehouse;
    
    @NotNull(message = "Purchase dates are required")
    private PurchaseDates dates;
    
    @NotBlank(message = "Status is required")
    private String status; // DRAFT, PENDING, ORDERED, RECEIVED, COMPLETED, CANCELLED
    
    @Builder.Default
    private List<PurchaseItem> items = new ArrayList<>();
    
    @NotNull(message = "Totals information is required")
    private PurchaseTotals totals;
    
    @NotNull(message = "Payment information is required")
    private PurchasePayment payment;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    private String notes;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    @NotNull(message = "Created by information is required")
    private PurchaseUser createdBy;
    
    /**
     * Nested class for supplier information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseSupplier {
        private String supplierId;
        private String supplierName;
        private String contactPerson;
        private String email;
        private String phone;
    }
    
    /**
     * Nested class for warehouse information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseWarehouse {
        private String warehouseId;
        private String warehouseName;
        private String warehouseCode;
    }
    
    /**
     * Nested class for purchase dates
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseDates {
        @NotNull(message = "Purchase date is required")
        private LocalDate purchaseDate;
        
        private LocalDate deliveryDate;
        private LocalDate expectedDate;
        private LocalDate receivedDate;
    }
    
    /**
     * Nested class for purchase items
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseItem {
        private String id;
        private PurchaseProduct product;
        
        @NotNull(message = "Quantity is required")
        @DecimalMin(value = "0.0", inclusive = false, message = "Quantity must be greater than 0")
        private BigDecimal quantity;
        
        @NotNull(message = "Unit cost is required")
        @DecimalMin(value = "0.0", inclusive = false, message = "Unit cost must be greater than 0")
        private BigDecimal unitCost;
        
        private BigDecimal totalCost;
        
        @DecimalMin(value = "0.0", message = "Received quantity must be non-negative")
        @Builder.Default
        private BigDecimal receivedQuantity = BigDecimal.ZERO;
        
        private PurchaseBatchInfo batchInfo;
        
        public BigDecimal calculateTotalCost() {
            if (quantity != null && unitCost != null) {
                this.totalCost = quantity.multiply(unitCost);
                return this.totalCost;
            }
            return BigDecimal.ZERO;
        }
        
        public boolean isFullyReceived() {
            return receivedQuantity != null && quantity != null && 
                   receivedQuantity.compareTo(quantity) >= 0;
        }
        
        public BigDecimal getPendingQuantity() {
            if (quantity != null && receivedQuantity != null) {
                return quantity.subtract(receivedQuantity);
            }
            return quantity != null ? quantity : BigDecimal.ZERO;
        }
    }
    
    /**
     * Nested class for product information in purchase item
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseProduct {
        private String productId;
        private String productName;
        private String sku;
        private String categoryName;
    }
    
    /**
     * Nested class for batch information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseBatchInfo {
        private String batchNumber;
        private LocalDate expiryDate;
        private LocalDate manufacturingDate;
    }
    
    /**
     * Nested class for purchase totals
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseTotals {
        @NotNull(message = "Subtotal is required")
        @DecimalMin(value = "0.0", message = "Subtotal must be non-negative")
        private BigDecimal subtotal;
        
        @DecimalMin(value = "0.0", message = "Tax amount must be non-negative")
        @Builder.Default
        private BigDecimal taxAmount = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Discount amount must be non-negative")
        @Builder.Default
        private BigDecimal discountAmount = BigDecimal.ZERO;
        
        @NotNull(message = "Total amount is required")
        @DecimalMin(value = "0.0", message = "Total amount must be non-negative")
        private BigDecimal totalAmount;
        
        public BigDecimal calculateTotalAmount() {
            if (subtotal != null) {
                BigDecimal total = subtotal;
                if (taxAmount != null) {
                    total = total.add(taxAmount);
                }
                if (discountAmount != null) {
                    total = total.subtract(discountAmount);
                }
                this.totalAmount = total;
                return total;
            }
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * Nested class for payment information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchasePayment {
        @DecimalMin(value = "0.0", message = "Paid amount must be non-negative")
        @Builder.Default
        private BigDecimal paidAmount = BigDecimal.ZERO;
        
        @NotBlank(message = "Payment status is required")
        private String paymentStatus; // UNPAID, PARTIAL, PAID
        
        private String paymentMethod; // CASH, BANK_TRANSFER, CHECK, CREDIT
        
        private LocalDate paymentDueDate;
    }
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseUser {
        private String userId;
        private String userName;
    }
    
    // Status constants
    public static final String STATUS_DRAFT = "DRAFT";
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_ORDERED = "ORDERED";
    public static final String STATUS_RECEIVED = "RECEIVED";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_CANCELLED = "CANCELLED";
    
    // Payment status constants
    public static final String PAYMENT_UNPAID = "UNPAID";
    public static final String PAYMENT_PARTIAL = "PARTIAL";
    public static final String PAYMENT_PAID = "PAID";
    
    // Payment method constants
    public static final String PAYMENT_CASH = "CASH";
    public static final String PAYMENT_BANK_TRANSFER = "BANK_TRANSFER";
    public static final String PAYMENT_CHECK = "CHECK";
    public static final String PAYMENT_CREDIT = "CREDIT";
    
    // Helper methods
    public int getTotalItemCount() {
        return items.size();
    }
    
    public BigDecimal getTotalQuantity() {
        return items.stream()
                .map(PurchaseItem::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    public BigDecimal getTotalReceivedQuantity() {
        return items.stream()
                .map(PurchaseItem::getReceivedQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    public boolean isFullyReceived() {
        return items.stream().allMatch(PurchaseItem::isFullyReceived);
    }
    
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }
    
    public boolean isPaid() {
        return payment != null && PAYMENT_PAID.equals(payment.getPaymentStatus());
    }
    
    public BigDecimal getOutstandingAmount() {
        if (totals != null && payment != null) {
            return totals.getTotalAmount().subtract(payment.getPaidAmount());
        }
        return BigDecimal.ZERO;
    }
}
