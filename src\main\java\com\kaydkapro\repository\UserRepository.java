package com.kaydkapro.repository;

import com.kaydkapro.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * User Repository - Data access layer for User entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface UserRepository extends MongoRepository<User, String> {
    
    /**
     * Find user by username
     */
    Optional<User> findByUsername(String username);
    
    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Find user by username or email
     */
    @Query("{'$or': [{'username': ?0}, {'email': ?0}]}")
    Optional<User> findByUsernameOrEmail(String usernameOrEmail);
    
    /**
     * Find all active users
     */
    List<User> findByIsActiveTrue();
    
    /**
     * Find all inactive users
     */
    List<User> findByIsActiveFalse();
    
    /**
     * Find users by role name
     */
    @Query("{'roles.roleName': ?0}")
    List<User> findByRole(String roleName);
    
    /**
     * Find active users by role name
     */
    @Query("{'roles.roleName': ?0, 'isActive': true}")
    List<User> findActiveUsersByRole(String roleName);
    
    /**
     * Search users by text (username, firstName, lastName, email)
     */
    @Query("{'$and': [{'isActive': true}, {'$or': [" +
           "{'username': {'$regex': ?0, '$options': 'i'}}, " +
           "{'firstName': {'$regex': ?0, '$options': 'i'}}, " +
           "{'lastName': {'$regex': ?0, '$options': 'i'}}, " +
           "{'email': {'$regex': ?0, '$options': 'i'}}" +
           "]}]}")
    Page<User> searchActiveUsers(String searchTerm, Pageable pageable);
    
    /**
     * Find users who haven't logged in since a specific date
     */
    @Query("{'$or': [{'lastLogin': {'$lt': ?0}}, {'lastLogin': null}]}")
    List<User> findUsersNotLoggedInSince(LocalDateTime date);
    
    /**
     * Find users created by a specific user
     */
    List<User> findByCreatedBy(String createdBy);
    
    /**
     * Find users created between dates
     */
    List<User> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Count active users
     */
    long countByIsActiveTrue();
    
    /**
     * Count users by role
     */
    @Query(value = "{'roles.roleName': ?0}", count = true)
    long countByRole(String roleName);
    
    /**
     * Check if username exists
     */
    boolean existsByUsername(String username);
    
    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);
    
    /**
     * Check if username exists excluding current user
     */
    @Query("{'username': ?0, '_id': {'$ne': ?1}}")
    boolean existsByUsernameAndIdNot(String username, String id);
    
    /**
     * Check if email exists excluding current user
     */
    @Query("{'email': ?0, '_id': {'$ne': ?1}}")
    boolean existsByEmailAndIdNot(String email, String id);
    
    /**
     * Find users with specific permissions
     */
    @Query("{'roles': {'$elemMatch': {'roleName': {'$in': ?0}}}}")
    List<User> findUsersWithRoles(List<String> roleNames);
    
    /**
     * Update last login time
     */
    @Query("{'_id': ?0}")
    void updateLastLogin(String userId, LocalDateTime lastLogin);
    
    /**
     * Find recently active users (logged in within last N days)
     */
    @Query("{'lastLogin': {'$gte': ?0}, 'isActive': true}")
    List<User> findRecentlyActiveUsers(LocalDateTime since);
    
    /**
     * Find users by phone number
     */
    Optional<User> findByPhone(String phone);
    
    /**
     * Find users created this month
     */
    @Query("{'createdAt': {'$gte': ?0, '$lt': ?1}}")
    List<User> findUsersCreatedInMonth(LocalDateTime monthStart, LocalDateTime monthEnd);
}
