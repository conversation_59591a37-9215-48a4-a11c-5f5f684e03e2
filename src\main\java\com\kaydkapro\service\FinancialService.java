package com.kaydkapro.service;

import com.kaydkapro.entity.Income;
import com.kaydkapro.entity.Expense;
import com.kaydkapro.repository.IncomeRepository;
import com.kaydkapro.repository.ExpenseRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Financial Service - Business logic for financial management (Income & Expenses)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class FinancialService {
    
    private final IncomeRepository incomeRepository;
    private final ExpenseRepository expenseRepository;
    private final AuditLogService auditLogService;
    
    // ==================== INCOME MANAGEMENT ====================
    
    /**
     * Record new income
     */
    public Income recordIncome(Income income, String recordedByUserId) {
        log.info("Recording new income: {} - Amount: {}", income.getTitle(), income.getAmount());
        
        // Set audit fields
        income.setCreatedBy(Income.IncomeUser.builder()
                .userId(recordedByUserId)
                .userName(getUserName(recordedByUserId))
                .build());
        income.setCreatedAt(LocalDateTime.now());
        
        Income savedIncome = incomeRepository.save(income);
        
        // Log audit
        auditLogService.logUserAction(
            recordedByUserId,
            "CREATE",
            "INCOME",
            savedIncome.getId(),
            savedIncome.getTitle() + " - " + savedIncome.getAmount()
        );
        
        log.info("Income recorded successfully: {}", savedIncome.getId());
        return savedIncome;
    }
    
    /**
     * Update income record
     */
    public Income updateIncome(String incomeId, Income incomeUpdates, String updatedByUserId) {
        log.info("Updating income: {}", incomeId);
        
        Income existingIncome = getIncomeById(incomeId);
        
        // Store old values for audit
        BigDecimal oldAmount = existingIncome.getAmount();
        String oldCategory = existingIncome.getCategory();
        
        // Update fields
        existingIncome.setTitle(incomeUpdates.getTitle());
        existingIncome.setAmount(incomeUpdates.getAmount());
        existingIncome.setCategory(incomeUpdates.getCategory());
        existingIncome.setDescription(incomeUpdates.getDescription());
        existingIncome.setIncomeDate(incomeUpdates.getIncomeDate());
        existingIncome.setReceiptImage(incomeUpdates.getReceiptImage());
        existingIncome.setNotes(incomeUpdates.getNotes());
        
        Income savedIncome = incomeRepository.save(existingIncome);
        
        // Log audit with changes
        auditLogService.logDataChange(
            updatedByUserId,
            "UPDATE",
            "INCOME",
            savedIncome.getId(),
            savedIncome.getTitle(),
            java.util.Map.of("amount", oldAmount, "category", oldCategory),
            java.util.Map.of("amount", savedIncome.getAmount(), "category", savedIncome.getCategory())
        );
        
        log.info("Income updated successfully: {}", savedIncome.getId());
        return savedIncome;
    }
    
    /**
     * Get income by ID
     */
    @Transactional(readOnly = true)
    public Income getIncomeById(String incomeId) {
        return incomeRepository.findById(incomeId)
                .orElseThrow(() -> new RuntimeException("Income not found with id: " + incomeId));
    }
    
    /**
     * Get income with pagination
     */
    @Transactional(readOnly = true)
    public Page<Income> getIncome(Pageable pageable) {
        return incomeRepository.findAll(pageable);
    }
    
    /**
     * Get income by date range
     */
    @Transactional(readOnly = true)
    public Page<Income> getIncomeByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return incomeRepository.findByDateRange(startDate, endDate, pageable);
    }
    
    /**
     * Get income by category
     */
    @Transactional(readOnly = true)
    public Page<Income> getIncomeByCategory(String category, Pageable pageable) {
        return incomeRepository.findByCategory(category, pageable);
    }
    
    /**
     * Search income
     */
    @Transactional(readOnly = true)
    public Page<Income> searchIncome(String searchTerm, Pageable pageable) {
        return incomeRepository.searchIncome(searchTerm, pageable);
    }
    
    /**
     * Delete income
     */
    public void deleteIncome(String incomeId, String deletedByUserId) {
        log.info("Deleting income: {}", incomeId);
        
        Income income = getIncomeById(incomeId);
        incomeRepository.delete(income);
        
        // Log audit
        auditLogService.logUserAction(
            deletedByUserId,
            "DELETE",
            "INCOME",
            income.getId(),
            income.getTitle() + " - " + income.getAmount()
        );
        
        log.info("Income deleted successfully: {}", incomeId);
    }
    
    // ==================== EXPENSE MANAGEMENT ====================
    
    /**
     * Record new expense
     */
    public Expense recordExpense(Expense expense, String recordedByUserId) {
        log.info("Recording new expense: {} - Amount: {}", expense.getTitle(), expense.getAmount());
        
        // Set audit fields
        expense.setCreatedBy(Expense.ExpenseUser.builder()
                .userId(recordedByUserId)
                .userName(getUserName(recordedByUserId))
                .build());
        expense.setCreatedAt(LocalDateTime.now());
        
        Expense savedExpense = expenseRepository.save(expense);
        
        // Log audit
        auditLogService.logUserAction(
            recordedByUserId,
            "CREATE",
            "EXPENSE",
            savedExpense.getId(),
            savedExpense.getTitle() + " - " + savedExpense.getAmount()
        );
        
        log.info("Expense recorded successfully: {}", savedExpense.getId());
        return savedExpense;
    }
    
    /**
     * Update expense record
     */
    public Expense updateExpense(String expenseId, Expense expenseUpdates, String updatedByUserId) {
        log.info("Updating expense: {}", expenseId);
        
        Expense existingExpense = getExpenseById(expenseId);
        
        // Store old values for audit
        BigDecimal oldAmount = existingExpense.getAmount();
        String oldCategory = existingExpense.getCategory();
        
        // Update fields
        existingExpense.setTitle(expenseUpdates.getTitle());
        existingExpense.setAmount(expenseUpdates.getAmount());
        existingExpense.setCategory(expenseUpdates.getCategory());
        existingExpense.setDescription(expenseUpdates.getDescription());
        existingExpense.setExpenseDate(expenseUpdates.getExpenseDate());
        existingExpense.setReceiptImage(expenseUpdates.getReceiptImage());
        existingExpense.setNotes(expenseUpdates.getNotes());
        existingExpense.setPaymentMethod(expenseUpdates.getPaymentMethod());
        existingExpense.setVendor(expenseUpdates.getVendor());
        existingExpense.setIsRecurring(expenseUpdates.getIsRecurring());
        
        Expense savedExpense = expenseRepository.save(existingExpense);
        
        // Log audit with changes
        auditLogService.logDataChange(
            updatedByUserId,
            "UPDATE",
            "EXPENSE",
            savedExpense.getId(),
            savedExpense.getTitle(),
            java.util.Map.of("amount", oldAmount, "category", oldCategory),
            java.util.Map.of("amount", savedExpense.getAmount(), "category", savedExpense.getCategory())
        );
        
        log.info("Expense updated successfully: {}", savedExpense.getId());
        return savedExpense;
    }
    
    /**
     * Get expense by ID
     */
    @Transactional(readOnly = true)
    public Expense getExpenseById(String expenseId) {
        return expenseRepository.findById(expenseId)
                .orElseThrow(() -> new RuntimeException("Expense not found with id: " + expenseId));
    }
    
    /**
     * Get expenses with pagination
     */
    @Transactional(readOnly = true)
    public Page<Expense> getExpenses(Pageable pageable) {
        return expenseRepository.findAll(pageable);
    }
    
    /**
     * Get expenses by date range
     */
    @Transactional(readOnly = true)
    public Page<Expense> getExpensesByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return expenseRepository.findByDateRange(startDate, endDate, pageable);
    }
    
    /**
     * Get expenses by category
     */
    @Transactional(readOnly = true)
    public Page<Expense> getExpensesByCategory(String category, Pageable pageable) {
        return expenseRepository.findByCategory(category, pageable);
    }
    
    /**
     * Search expenses
     */
    @Transactional(readOnly = true)
    public Page<Expense> searchExpenses(String searchTerm, Pageable pageable) {
        return expenseRepository.searchExpenses(searchTerm, pageable);
    }
    
    /**
     * Delete expense
     */
    public void deleteExpense(String expenseId, String deletedByUserId) {
        log.info("Deleting expense: {}", expenseId);
        
        Expense expense = getExpenseById(expenseId);
        expenseRepository.delete(expense);
        
        // Log audit
        auditLogService.logUserAction(
            deletedByUserId,
            "DELETE",
            "EXPENSE",
            expense.getId(),
            expense.getTitle() + " - " + expense.getAmount()
        );
        
        log.info("Expense deleted successfully: {}", expenseId);
    }
    
    // ==================== FINANCIAL ANALYTICS ====================
    
    /**
     * Calculate total income for period
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateTotalIncome(LocalDate startDate, LocalDate endDate) {
        return incomeRepository.calculateTotalIncome(startDate, endDate).orElse(BigDecimal.ZERO);
    }
    
    /**
     * Calculate total expenses for period
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateTotalExpenses(LocalDate startDate, LocalDate endDate) {
        return expenseRepository.calculateTotalExpenses(startDate, endDate).orElse(BigDecimal.ZERO);
    }
    
    /**
     * Calculate net profit for period
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateNetProfit(LocalDate startDate, LocalDate endDate) {
        BigDecimal totalIncome = calculateTotalIncome(startDate, endDate);
        BigDecimal totalExpenses = calculateTotalExpenses(startDate, endDate);
        return totalIncome.subtract(totalExpenses);
    }
    
    /**
     * Get income by category statistics
     */
    @Transactional(readOnly = true)
    public List<IncomeRepository.IncomeCategoryStats> getIncomeByCategoryStats(LocalDate startDate, LocalDate endDate) {
        return incomeRepository.getIncomeByCategoryStats(startDate, endDate);
    }
    
    /**
     * Get expenses by category statistics
     */
    @Transactional(readOnly = true)
    public List<ExpenseRepository.ExpenseCategoryStats> getExpensesByCategoryStats(LocalDate startDate, LocalDate endDate) {
        return expenseRepository.getExpensesByCategoryStats(startDate, endDate);
    }
    
    /**
     * Get daily income statistics
     */
    @Transactional(readOnly = true)
    public List<IncomeRepository.DailyIncomeStats> getDailyIncomeStats(LocalDate startDate, LocalDate endDate) {
        return incomeRepository.getDailyIncomeStats(startDate, endDate);
    }
    
    /**
     * Get daily expense statistics
     */
    @Transactional(readOnly = true)
    public List<ExpenseRepository.DailyExpenseStats> getDailyExpenseStats(LocalDate startDate, LocalDate endDate) {
        return expenseRepository.getDailyExpenseStats(startDate, endDate);
    }
    
    /**
     * Get financial summary for period
     */
    @Transactional(readOnly = true)
    public FinancialSummary getFinancialSummary(LocalDate startDate, LocalDate endDate) {
        BigDecimal totalIncome = calculateTotalIncome(startDate, endDate);
        BigDecimal totalExpenses = calculateTotalExpenses(startDate, endDate);
        BigDecimal netProfit = totalIncome.subtract(totalExpenses);
        
        long incomeTransactions = incomeRepository.countByDateRange(startDate, endDate);
        long expenseTransactions = expenseRepository.countByDateRange(startDate, endDate);
        
        return FinancialSummary.builder()
                .totalIncome(totalIncome)
                .totalExpenses(totalExpenses)
                .netProfit(netProfit)
                .profitMargin(totalIncome.compareTo(BigDecimal.ZERO) > 0 ? 
                    netProfit.divide(totalIncome, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)) : 
                    BigDecimal.ZERO)
                .incomeTransactions(incomeTransactions)
                .expenseTransactions(expenseTransactions)
                .startDate(startDate)
                .endDate(endDate)
                .build();
    }
    
    /**
     * Get financial statistics
     */
    @Transactional(readOnly = true)
    public FinancialStats getFinancialStats() {
        LocalDate today = LocalDate.now();
        LocalDate monthStart = today.withDayOfMonth(1);
        
        long totalIncomeRecords = incomeRepository.count();
        long totalExpenseRecords = expenseRepository.count();
        long recurringExpenses = expenseRepository.countRecurringExpenses();
        
        BigDecimal monthlyIncome = calculateTotalIncome(monthStart, today);
        BigDecimal monthlyExpenses = calculateTotalExpenses(monthStart, today);
        
        return FinancialStats.builder()
                .totalIncomeRecords(totalIncomeRecords)
                .totalExpenseRecords(totalExpenseRecords)
                .recurringExpenses(recurringExpenses)
                .monthlyIncome(monthlyIncome)
                .monthlyExpenses(monthlyExpenses)
                .monthlyProfit(monthlyIncome.subtract(monthlyExpenses))
                .build();
    }
    
    // Helper methods
    private String getUserName(String userId) {
        return "User-" + userId;
    }
    
    /**
     * Financial summary DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class FinancialSummary {
        private BigDecimal totalIncome;
        private BigDecimal totalExpenses;
        private BigDecimal netProfit;
        private BigDecimal profitMargin;
        private long incomeTransactions;
        private long expenseTransactions;
        private LocalDate startDate;
        private LocalDate endDate;
    }
    
    /**
     * Financial statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class FinancialStats {
        private long totalIncomeRecords;
        private long totalExpenseRecords;
        private long recurringExpenses;
        private BigDecimal monthlyIncome;
        private BigDecimal monthlyExpenses;
        private BigDecimal monthlyProfit;
    }
}
