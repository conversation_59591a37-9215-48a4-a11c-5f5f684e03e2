package com.kaydkapro.service;

import com.kaydkapro.entity.QRCode;
import com.kaydkapro.entity.Product;
import com.kaydkapro.repository.QRCodeRepository;
import com.kaydkapro.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * QRCode Service - Business logic for QR code management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class QRCodeService {
    
    private final QRCodeRepository qrCodeRepository;
    private final ProductRepository productRepository;
    private final AuditLogService auditLogService;
    
    /**
     * Generate QR code for product
     */
    public QRCode generateProductQRCode(String productId, String createdByUserId) {
        log.info("Generating QR code for product: {}", productId);
        
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + productId));
        
        // Check if QR code already exists for this product
        List<QRCode> existingQRCodes = qrCodeRepository.findByProductIdAndType(productId, QRCode.TYPE_PRODUCT_INFO);
        if (!existingQRCodes.isEmpty()) {
            log.info("QR code already exists for product: {}", productId);
            return existingQRCodes.get(0);
        }
        
        QRCode qrCode = QRCode.createProductQRCode(
            productId,
            product.getName(),
            product.getSku(),
            product.getCategory().getCategoryName(),
            createdByUserId,
            getUserName(createdByUserId)
        );
        
        QRCode savedQRCode = qrCodeRepository.save(qrCode);
        
        // Log audit
        auditLogService.logUserAction(
            createdByUserId,
            "CREATE",
            "QRCODE",
            savedQRCode.getId(),
            "Product QR: " + product.getName()
        );
        
        log.info("QR code generated successfully: {}", savedQRCode.getId());
        return savedQRCode;
    }
    
    /**
     * Generate inventory QR code for product in warehouse
     */
    public QRCode generateInventoryQRCode(String productId, String warehouseId, String createdByUserId) {
        log.info("Generating inventory QR code for product: {} in warehouse: {}", productId, warehouseId);
        
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + productId));
        
        QRCode qrCode = QRCode.createInventoryQRCode(
            productId,
            product.getName(),
            product.getSku(),
            warehouseId,
            createdByUserId,
            getUserName(createdByUserId)
        );
        
        QRCode savedQRCode = qrCodeRepository.save(qrCode);
        
        // Log audit
        auditLogService.logUserAction(
            createdByUserId,
            "CREATE",
            "QRCODE",
            savedQRCode.getId(),
            "Inventory QR: " + product.getName()
        );
        
        log.info("Inventory QR code generated successfully: {}", savedQRCode.getId());
        return savedQRCode;
    }
    
    /**
     * Scan QR code and record analytics
     */
    public QRCodeScanResult scanQRCode(String qrCodeValue, String scannedByUserId) {
        log.info("Scanning QR code: {}", qrCodeValue);
        
        QRCode qrCode = qrCodeRepository.findByQrCode(qrCodeValue)
                .orElseThrow(() -> new RuntimeException("QR code not found: " + qrCodeValue));
        
        if (!qrCode.getIsActive()) {
            throw new RuntimeException("QR code is inactive");
        }
        
        // Record scan analytics
        qrCode.recordScan(scannedByUserId);
        qrCodeRepository.save(qrCode);
        
        // Log audit
        auditLogService.logUserAction(
            scannedByUserId,
            "SCAN",
            "QRCODE",
            qrCode.getId(),
            "QR Scan: " + qrCode.getProduct().getProductName()
        );
        
        // Create scan result
        QRCodeScanResult result = QRCodeScanResult.builder()
                .qrCodeId(qrCode.getId())
                .qrType(qrCode.getQrType())
                .productId(qrCode.getProduct().getProductId())
                .productName(qrCode.getProduct().getProductName())
                .sku(qrCode.getProduct().getSku())
                .categoryName(qrCode.getProduct().getCategoryName())
                .metadata(qrCode.getMetadata())
                .scanCount(qrCode.getTotalScans())
                .lastScanned(qrCode.getLastScanned())
                .build();
        
        log.info("QR code scanned successfully: {} - Total scans: {}", qrCode.getId(), qrCode.getTotalScans());
        return result;
    }
    
    /**
     * Get QR code by ID
     */
    @Transactional(readOnly = true)
    public QRCode getQRCodeById(String qrCodeId) {
        return qrCodeRepository.findById(qrCodeId)
                .orElseThrow(() -> new RuntimeException("QR code not found with id: " + qrCodeId));
    }
    
    /**
     * Get QR codes by product ID
     */
    @Transactional(readOnly = true)
    public List<QRCode> getQRCodesByProductId(String productId) {
        return qrCodeRepository.findByProductId(productId);
    }
    
    /**
     * Get QR codes by type
     */
    @Transactional(readOnly = true)
    public Page<QRCode> getQRCodesByType(String qrType, Pageable pageable) {
        return qrCodeRepository.findByQrType(qrType, pageable);
    }
    
    /**
     * Get all active QR codes
     */
    @Transactional(readOnly = true)
    public Page<QRCode> getActiveQRCodes(Pageable pageable) {
        return qrCodeRepository.findActiveQRCodes(pageable);
    }
    
    /**
     * Search QR codes by product name
     */
    @Transactional(readOnly = true)
    public Page<QRCode> searchQRCodesByProductName(String productName, Pageable pageable) {
        return qrCodeRepository.searchByProductName(productName, pageable);
    }
    
    /**
     * Get QR codes with high scan activity
     */
    @Transactional(readOnly = true)
    public List<QRCode> getHighActivityQRCodes(Integer minScans) {
        return qrCodeRepository.findHighActivityQRCodes(minScans);
    }
    
    /**
     * Get recently scanned QR codes
     */
    @Transactional(readOnly = true)
    public List<QRCode> getRecentlyScannedQRCodes(int hours) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        return qrCodeRepository.findRecentlyScannedQRCodes(since);
    }
    
    /**
     * Get QR codes never scanned
     */
    @Transactional(readOnly = true)
    public List<QRCode> getNeverScannedQRCodes() {
        return qrCodeRepository.findNeverScannedQRCodes();
    }
    
    /**
     * Activate/Deactivate QR code
     */
    public QRCode toggleQRCodeStatus(String qrCodeId, boolean isActive, String updatedByUserId) {
        log.info("Toggling QR code status: {} to {}", qrCodeId, isActive);
        
        QRCode qrCode = getQRCodeById(qrCodeId);
        qrCode.setIsActive(isActive);
        
        QRCode savedQRCode = qrCodeRepository.save(qrCode);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            isActive ? "ACTIVATE" : "DEACTIVATE",
            "QRCODE",
            savedQRCode.getId(),
            "QR: " + savedQRCode.getProduct().getProductName()
        );
        
        log.info("QR code status updated: {} - {}", savedQRCode.getId(), isActive);
        return savedQRCode;
    }
    
    /**
     * Bulk generate QR codes for products
     */
    public List<QRCode> bulkGenerateProductQRCodes(List<String> productIds, String createdByUserId) {
        log.info("Bulk generating QR codes for {} products", productIds.size());
        
        List<QRCode> generatedQRCodes = productIds.stream()
                .map(productId -> {
                    try {
                        return generateProductQRCode(productId, createdByUserId);
                    } catch (Exception e) {
                        log.warn("Failed to generate QR code for product {}: {}", productId, e.getMessage());
                        return null;
                    }
                })
                .filter(qrCode -> qrCode != null)
                .toList();
        
        // Log audit
        auditLogService.logUserAction(
            createdByUserId,
            "BULK_CREATE",
            "QRCODE",
            null,
            "Bulk QR generation: " + generatedQRCodes.size() + " codes"
        );
        
        log.info("Bulk QR code generation completed: {} codes generated", generatedQRCodes.size());
        return generatedQRCodes;
    }
    
    /**
     * Get QR code statistics
     */
    @Transactional(readOnly = true)
    public QRCodeStats getQRCodeStats() {
        long totalQRCodes = qrCodeRepository.count();
        long activeQRCodes = qrCodeRepository.countActiveQRCodes();
        long productInfoQRCodes = qrCodeRepository.countByQrType(QRCode.TYPE_PRODUCT_INFO);
        long inventoryQRCodes = qrCodeRepository.countByQrType(QRCode.TYPE_INVENTORY);
        long neverScannedQRCodes = qrCodeRepository.findNeverScannedQRCodes().size();
        
        return QRCodeStats.builder()
                .totalQRCodes(totalQRCodes)
                .activeQRCodes(activeQRCodes)
                .inactiveQRCodes(totalQRCodes - activeQRCodes)
                .productInfoQRCodes(productInfoQRCodes)
                .inventoryQRCodes(inventoryQRCodes)
                .neverScannedQRCodes(neverScannedQRCodes)
                .build();
    }
    
    // Helper methods
    private String getUserName(String userId) {
        // This would typically fetch from UserService or cache
        return "User-" + userId;
    }
    
    /**
     * QR code scan result DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class QRCodeScanResult {
        private String qrCodeId;
        private String qrType;
        private String productId;
        private String productName;
        private String sku;
        private String categoryName;
        private java.util.Map<String, Object> metadata;
        private Integer scanCount;
        private LocalDateTime lastScanned;
    }
    
    /**
     * QR code statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class QRCodeStats {
        private long totalQRCodes;
        private long activeQRCodes;
        private long inactiveQRCodes;
        private long productInfoQRCodes;
        private long inventoryQRCodes;
        private long neverScannedQRCodes;
    }
}
