package com.kaydkapro.service;

import com.kaydkapro.entity.Product;
import com.kaydkapro.repository.ProductRepository;
import com.kaydkapro.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Product Service - Business logic for product management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProductService {
    
    private final ProductRepository productRepository;
    private final CategoryRepository categoryRepository;
    private final AuditLogService auditLogService;
    private final QRCodeService qrCodeService;
    
    /**
     * Create a new product
     */
    public Product createProduct(Product product, String createdByUserId) {
        log.info("Creating new product: {}", product.getName());
        
        // Validate unique constraints
        validateProductUniqueness(product);
        
        // Validate category exists
        validateCategory(product.getCategory().getCategoryId());
        
        // Set audit fields
        product.setCreatedBy(createdByUserId);
        product.setCreatedAt(LocalDateTime.now());
        product.setIsActive(true);
        
        // Calculate profit margin
        if (product.getPricing() != null) {
            product.getPricing().getProfitMargin(); // This will calculate and set the margin
        }
        
        Product savedProduct = productRepository.save(product);
        
        // Generate QR code for the product
        try {
            qrCodeService.generateProductQRCode(savedProduct.getId(), createdByUserId);
        } catch (Exception e) {
            log.warn("Failed to generate QR code for product {}: {}", savedProduct.getId(), e.getMessage());
        }
        
        // Log audit
        auditLogService.logUserAction(
            createdByUserId,
            "CREATE",
            "PRODUCT",
            savedProduct.getId(),
            savedProduct.getName()
        );
        
        log.info("Product created successfully: {}", savedProduct.getId());
        return savedProduct;
    }
    
    /**
     * Update existing product
     */
    public Product updateProduct(String productId, Product productUpdates, String updatedByUserId) {
        log.info("Updating product: {}", productId);
        
        Product existingProduct = getProductById(productId);
        
        // Store old values for audit
        String oldName = existingProduct.getName();
        BigDecimal oldSellingPrice = existingProduct.getPricing().getSellingPrice();
        
        // Validate unique constraints if SKU/barcode changed
        if (!existingProduct.getSku().equals(productUpdates.getSku()) ||
            (existingProduct.getBarcode() != null && !existingProduct.getBarcode().equals(productUpdates.getBarcode()))) {
            validateProductUniqueness(productUpdates, productId);
        }
        
        // Validate category if changed
        if (!existingProduct.getCategory().getCategoryId().equals(productUpdates.getCategory().getCategoryId())) {
            validateCategory(productUpdates.getCategory().getCategoryId());
        }
        
        // Update fields
        existingProduct.setName(productUpdates.getName());
        existingProduct.setDescription(productUpdates.getDescription());
        existingProduct.setSku(productUpdates.getSku());
        existingProduct.setBarcode(productUpdates.getBarcode());
        existingProduct.setCategory(productUpdates.getCategory());
        existingProduct.setUnit(productUpdates.getUnit());
        existingProduct.setPricing(productUpdates.getPricing());
        existingProduct.setStockLevels(productUpdates.getStockLevels());
        existingProduct.setExpiryTracking(productUpdates.getExpiryTracking());
        existingProduct.setBatchTracking(productUpdates.getBatchTracking());
        existingProduct.setTags(productUpdates.getTags());
        existingProduct.setUpdatedBy(updatedByUserId);
        existingProduct.setUpdatedAt(LocalDateTime.now());
        
        Product savedProduct = productRepository.save(existingProduct);
        
        // Log audit with changes
        auditLogService.logDataChange(
            updatedByUserId,
            "UPDATE",
            "PRODUCT",
            savedProduct.getId(),
            savedProduct.getName(),
            createOldValuesMap(oldName, oldSellingPrice),
            createNewValuesMap(savedProduct.getName(), savedProduct.getPricing().getSellingPrice())
        );
        
        log.info("Product updated successfully: {}", savedProduct.getId());
        return savedProduct;
    }
    
    /**
     * Get product by ID
     */
    @Transactional(readOnly = true)
    public Product getProductById(String productId) {
        return productRepository.findById(productId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + productId));
    }
    
    /**
     * Get product by SKU
     */
    @Transactional(readOnly = true)
    public Product getProductBySku(String sku) {
        return productRepository.findBySku(sku)
                .orElseThrow(() -> new RuntimeException("Product not found with SKU: " + sku));
    }
    
    /**
     * Get product by barcode
     */
    @Transactional(readOnly = true)
    public Product getProductByBarcode(String barcode) {
        return productRepository.findByBarcode(barcode)
                .orElseThrow(() -> new RuntimeException("Product not found with barcode: " + barcode));
    }
    
    /**
     * Get all active products
     */
    @Transactional(readOnly = true)
    public List<Product> getAllActiveProducts() {
        return productRepository.findByIsActiveTrue();
    }
    
    /**
     * Get products with pagination
     */
    @Transactional(readOnly = true)
    public Page<Product> getProducts(Pageable pageable) {
        return productRepository.findAll(pageable);
    }
    
    /**
     * Search products
     */
    @Transactional(readOnly = true)
    public Page<Product> searchProducts(String searchTerm, Pageable pageable) {
        return productRepository.searchProducts(searchTerm, pageable);
    }
    
    /**
     * Get products by category
     */
    @Transactional(readOnly = true)
    public Page<Product> getProductsByCategory(String categoryId, Pageable pageable) {
        return productRepository.findByCategoryId(categoryId, pageable);
    }
    
    /**
     * Get products by price range
     */
    @Transactional(readOnly = true)
    public List<Product> getProductsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        return productRepository.findByPriceRange(minPrice, maxPrice);
    }
    
    /**
     * Get low stock products
     */
    @Transactional(readOnly = true)
    public List<Product> getLowStockProducts() {
        return productRepository.findLowStockProducts();
    }
    
    /**
     * Get products expiring soon
     */
    @Transactional(readOnly = true)
    public List<Product> getProductsExpiringSoon() {
        return productRepository.findProductsExpiringSoon();
    }
    
    /**
     * Get top selling products
     */
    @Transactional(readOnly = true)
    public List<Product> getTopSellingProducts(int limit) {
        return productRepository.findTopSellingProducts(limit);
    }
    
    /**
     * Add image to product
     */
    public Product addProductImage(String productId, String imageUrl, boolean isPrimary, String updatedByUserId) {
        log.info("Adding image to product: {}", productId);
        
        Product product = getProductById(productId);
        
        // If this is primary image, set others to non-primary
        if (isPrimary) {
            product.getImages().forEach(img -> img.setIsPrimary(false));
        }
        
        Product.ProductImage newImage = Product.ProductImage.builder()
                .imageUrl(imageUrl)
                .isPrimary(isPrimary)
                .sortOrder(product.getImages().size() + 1)
                .build();
        
        product.getImages().add(newImage);
        product.setUpdatedBy(updatedByUserId);
        product.setUpdatedAt(LocalDateTime.now());
        
        Product savedProduct = productRepository.save(product);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            "ADD_IMAGE",
            "PRODUCT",
            savedProduct.getId(),
            savedProduct.getName()
        );
        
        log.info("Image added to product successfully: {}", savedProduct.getId());
        return savedProduct;
    }
    
    /**
     * Add tag to product
     */
    public Product addTagToProduct(String productId, String tag, String updatedByUserId) {
        log.info("Adding tag '{}' to product: {}", tag, productId);
        
        Product product = getProductById(productId);
        product.addTag(tag);
        product.setUpdatedBy(updatedByUserId);
        product.setUpdatedAt(LocalDateTime.now());
        
        Product savedProduct = productRepository.save(product);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            "ADD_TAG",
            "PRODUCT",
            savedProduct.getId(),
            savedProduct.getName() + " - Tag: " + tag
        );
        
        return savedProduct;
    }
    
    /**
     * Activate/Deactivate product
     */
    public Product toggleProductStatus(String productId, boolean isActive, String updatedByUserId) {
        log.info("Toggling product status: {} to {}", productId, isActive);
        
        Product product = getProductById(productId);
        product.setIsActive(isActive);
        product.setUpdatedBy(updatedByUserId);
        product.setUpdatedAt(LocalDateTime.now());
        
        Product savedProduct = productRepository.save(product);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            isActive ? "ACTIVATE" : "DEACTIVATE",
            "PRODUCT",
            savedProduct.getId(),
            savedProduct.getName()
        );
        
        log.info("Product status updated: {} - {}", savedProduct.getId(), isActive);
        return savedProduct;
    }
    
    /**
     * Delete product (soft delete)
     */
    public void deleteProduct(String productId, String deletedByUserId) {
        log.info("Deleting product: {}", productId);
        
        Product product = getProductById(productId);
        product.setIsActive(false);
        product.setUpdatedBy(deletedByUserId);
        product.setUpdatedAt(LocalDateTime.now());
        
        productRepository.save(product);
        
        // Log audit
        auditLogService.logUserAction(
            deletedByUserId,
            "DELETE",
            "PRODUCT",
            product.getId(),
            product.getName()
        );
        
        log.info("Product deleted successfully: {}", productId);
    }
    
    /**
     * Get product statistics
     */
    @Transactional(readOnly = true)
    public ProductStats getProductStats() {
        long totalProducts = productRepository.count();
        long activeProducts = productRepository.countByIsActiveTrue();
        long lowStockProducts = productRepository.findLowStockProducts().size();
        long productsWithVariants = productRepository.findProductsWithVariants().size();
        
        return ProductStats.builder()
                .totalProducts(totalProducts)
                .activeProducts(activeProducts)
                .inactiveProducts(totalProducts - activeProducts)
                .lowStockProducts(lowStockProducts)
                .productsWithVariants(productsWithVariants)
                .build();
    }
    
    // Helper methods
    private void validateProductUniqueness(Product product) {
        validateProductUniqueness(product, null);
    }
    
    private void validateProductUniqueness(Product product, String excludeProductId) {
        if (excludeProductId != null) {
            if (productRepository.existsBySkuAndIdNot(product.getSku(), excludeProductId)) {
                throw new RuntimeException("SKU already exists");
            }
            if (product.getBarcode() != null && 
                productRepository.existsByBarcodeAndIdNot(product.getBarcode(), excludeProductId)) {
                throw new RuntimeException("Barcode already exists");
            }
        } else {
            if (productRepository.existsBySku(product.getSku())) {
                throw new RuntimeException("SKU already exists");
            }
            if (product.getBarcode() != null && productRepository.existsByBarcode(product.getBarcode())) {
                throw new RuntimeException("Barcode already exists");
            }
        }
    }
    
    private void validateCategory(String categoryId) {
        if (!categoryRepository.existsById(categoryId)) {
            throw new RuntimeException("Category not found with id: " + categoryId);
        }
    }
    
    private java.util.Map<String, Object> createOldValuesMap(String name, BigDecimal sellingPrice) {
        return java.util.Map.of(
            "name", name,
            "sellingPrice", sellingPrice
        );
    }
    
    private java.util.Map<String, Object> createNewValuesMap(String name, BigDecimal sellingPrice) {
        return java.util.Map.of(
            "name", name,
            "sellingPrice", sellingPrice
        );
    }
    
    /**
     * Product statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class ProductStats {
        private long totalProducts;
        private long activeProducts;
        private long inactiveProducts;
        private long lowStockProducts;
        private long productsWithVariants;
    }
}
