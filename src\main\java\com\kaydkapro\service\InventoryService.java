package com.kaydkapro.service;

import com.kaydkapro.entity.Inventory;
import com.kaydkapro.entity.StockMovement;
import com.kaydkapro.entity.Product;
import com.kaydkapro.repository.InventoryRepository;
import com.kaydkapro.repository.StockMovementRepository;
import com.kaydkapro.repository.ProductRepository;
import com.kaydkapro.repository.WarehouseRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Inventory Service - Business logic for inventory management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class InventoryService {
    
    private final InventoryRepository inventoryRepository;
    private final StockMovementRepository stockMovementRepository;
    private final ProductRepository productRepository;
    private final WarehouseRepository warehouseRepository;
    private final AuditLogService auditLogService;
    private final NotificationService notificationService;
    
    /**
     * Get or create inventory record
     */
    public Inventory getOrCreateInventory(String productId, String warehouseId) {
        Optional<Inventory> existingInventory = inventoryRepository.findByProductIdAndWarehouseId(productId, warehouseId);
        
        if (existingInventory.isPresent()) {
            return existingInventory.get();
        }
        
        // Create new inventory record
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new RuntimeException("Product not found with id: " + productId));
        
        var warehouse = warehouseRepository.findById(warehouseId)
                .orElseThrow(() -> new RuntimeException("Warehouse not found with id: " + warehouseId));
        
        Inventory inventory = Inventory.builder()
                .product(Inventory.InventoryProduct.builder()
                        .productId(productId)
                        .productName(product.getName())
                        .sku(product.getSku())
                        .build())
                .warehouse(Inventory.InventoryWarehouse.builder()
                        .warehouseId(warehouseId)
                        .warehouseName(warehouse.getName())
                        .warehouseCode(warehouse.getCode())
                        .build())
                .quantities(Inventory.InventoryQuantities.builder()
                        .available(BigDecimal.ZERO)
                        .reserved(BigDecimal.ZERO)
                        .onOrder(BigDecimal.ZERO)
                        .build())
                .costInfo(Inventory.InventoryCostInfo.builder()
                        .averageCost(product.getPricing().getCostPrice())
                        .lastCost(product.getPricing().getCostPrice())
                        .build())
                .lastUpdated(LocalDateTime.now())
                .build();
        
        return inventoryRepository.save(inventory);
    }
    
    /**
     * Add stock to inventory
     */
    public Inventory addStock(String productId, String warehouseId, BigDecimal quantity, 
                             BigDecimal unitCost, String referenceType, String referenceId, 
                             String addedByUserId) {
        log.info("Adding stock: {} units of product {} to warehouse {}", quantity, productId, warehouseId);
        
        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("Quantity must be greater than zero");
        }
        
        Inventory inventory = getOrCreateInventory(productId, warehouseId);
        
        // Update quantities
        BigDecimal oldQuantity = inventory.getQuantities().getAvailable();
        inventory.getQuantities().setAvailable(oldQuantity.add(quantity));
        
        // Update cost information using weighted average
        updateAverageCost(inventory, quantity, unitCost);
        
        inventory.setLastUpdated(LocalDateTime.now());
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        // Create stock movement record
        createStockMovement(
            productId, warehouseId, StockMovement.MOVEMENT_IN, quantity, unitCost,
            referenceType, referenceId, "Stock added", addedByUserId
        );
        
        // Check for low stock alerts after adding
        checkLowStockAlert(savedInventory, addedByUserId);
        
        // Log audit
        auditLogService.logUserAction(
            addedByUserId,
            "ADD_STOCK",
            "INVENTORY",
            savedInventory.getId(),
            String.format("%s - Added: %s", inventory.getProduct().getProductName(), quantity)
        );
        
        log.info("Stock added successfully: {} units to {}", quantity, savedInventory.getId());
        return savedInventory;
    }
    
    /**
     * Remove stock from inventory
     */
    public Inventory removeStock(String productId, String warehouseId, BigDecimal quantity,
                                String referenceType, String referenceId, String removedByUserId) {
        log.info("Removing stock: {} units of product {} from warehouse {}", quantity, productId, warehouseId);
        
        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("Quantity must be greater than zero");
        }
        
        Inventory inventory = inventoryRepository.findByProductIdAndWarehouseId(productId, warehouseId)
                .orElseThrow(() -> new RuntimeException("Inventory not found for product in warehouse"));
        
        BigDecimal availableQuantity = inventory.getQuantities().getAvailable();
        
        if (availableQuantity.compareTo(quantity) < 0) {
            throw new RuntimeException("Insufficient stock available. Available: " + availableQuantity + ", Requested: " + quantity);
        }
        
        // Update quantities
        inventory.getQuantities().setAvailable(availableQuantity.subtract(quantity));
        inventory.setLastUpdated(LocalDateTime.now());
        
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        // Create stock movement record
        createStockMovement(
            productId, warehouseId, StockMovement.MOVEMENT_OUT, quantity, 
            inventory.getCostInfo().getAverageCost(),
            referenceType, referenceId, "Stock removed", removedByUserId
        );
        
        // Check for low stock alerts after removal
        checkLowStockAlert(savedInventory, removedByUserId);
        
        // Log audit
        auditLogService.logUserAction(
            removedByUserId,
            "REMOVE_STOCK",
            "INVENTORY",
            savedInventory.getId(),
            String.format("%s - Removed: %s", inventory.getProduct().getProductName(), quantity)
        );
        
        log.info("Stock removed successfully: {} units from {}", quantity, savedInventory.getId());
        return savedInventory;
    }
    
    /**
     * Transfer stock between warehouses
     */
    public TransferResult transferStock(String productId, String fromWarehouseId, String toWarehouseId,
                                       BigDecimal quantity, String transferredByUserId) {
        log.info("Transferring stock: {} units of product {} from warehouse {} to warehouse {}", 
                quantity, productId, fromWarehouseId, toWarehouseId);
        
        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("Quantity must be greater than zero");
        }
        
        if (fromWarehouseId.equals(toWarehouseId)) {
            throw new RuntimeException("Cannot transfer to the same warehouse");
        }
        
        // Remove from source warehouse
        Inventory fromInventory = removeStock(
            productId, fromWarehouseId, quantity,
            StockMovement.REF_TRANSFER, "TRANSFER-" + System.currentTimeMillis(),
            transferredByUserId
        );
        
        // Add to destination warehouse
        Inventory toInventory = addStock(
            productId, toWarehouseId, quantity, fromInventory.getCostInfo().getAverageCost(),
            StockMovement.REF_TRANSFER, "TRANSFER-" + System.currentTimeMillis(),
            transferredByUserId
        );
        
        // Log audit
        auditLogService.logUserAction(
            transferredByUserId,
            "TRANSFER_STOCK",
            "INVENTORY",
            fromInventory.getId(),
            String.format("%s - Transferred: %s from %s to %s", 
                fromInventory.getProduct().getProductName(), quantity,
                fromInventory.getWarehouse().getWarehouseName(),
                toInventory.getWarehouse().getWarehouseName())
        );
        
        log.info("Stock transferred successfully: {} units", quantity);
        return TransferResult.builder()
                .fromInventory(fromInventory)
                .toInventory(toInventory)
                .transferredQuantity(quantity)
                .build();
    }
    
    /**
     * Adjust stock (for corrections)
     */
    public Inventory adjustStock(String productId, String warehouseId, BigDecimal newQuantity,
                                String reason, String adjustedByUserId) {
        log.info("Adjusting stock: product {} in warehouse {} to {} units", productId, warehouseId, newQuantity);
        
        if (newQuantity.compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("Quantity cannot be negative");
        }
        
        Inventory inventory = inventoryRepository.findByProductIdAndWarehouseId(productId, warehouseId)
                .orElseThrow(() -> new RuntimeException("Inventory not found for product in warehouse"));
        
        BigDecimal currentQuantity = inventory.getQuantities().getAvailable();
        BigDecimal adjustmentQuantity = newQuantity.subtract(currentQuantity);
        
        if (adjustmentQuantity.compareTo(BigDecimal.ZERO) == 0) {
            log.info("No adjustment needed - quantities are the same");
            return inventory;
        }
        
        // Update quantities
        inventory.getQuantities().setAvailable(newQuantity);
        inventory.setLastUpdated(LocalDateTime.now());
        
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        // Create stock movement record
        createStockMovement(
            productId, warehouseId, StockMovement.MOVEMENT_ADJUSTMENT, adjustmentQuantity.abs(),
            inventory.getCostInfo().getAverageCost(),
            StockMovement.REF_ADJUSTMENT, "ADJ-" + System.currentTimeMillis(),
            reason, adjustedByUserId
        );
        
        // Check for low stock alerts after adjustment
        checkLowStockAlert(savedInventory, adjustedByUserId);
        
        // Log audit
        auditLogService.logUserAction(
            adjustedByUserId,
            "ADJUST_STOCK",
            "INVENTORY",
            savedInventory.getId(),
            String.format("%s - Adjusted: %s to %s (%s)", 
                inventory.getProduct().getProductName(), currentQuantity, newQuantity, reason)
        );
        
        log.info("Stock adjusted successfully: {} to {} units", currentQuantity, newQuantity);
        return savedInventory;
    }
    
    /**
     * Reserve stock for sales
     */
    public Inventory reserveStock(String productId, String warehouseId, BigDecimal quantity, String reservedByUserId) {
        log.info("Reserving stock: {} units of product {} in warehouse {}", quantity, productId, warehouseId);
        
        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("Quantity must be greater than zero");
        }
        
        Inventory inventory = inventoryRepository.findByProductIdAndWarehouseId(productId, warehouseId)
                .orElseThrow(() -> new RuntimeException("Inventory not found for product in warehouse"));
        
        BigDecimal availableForReservation = inventory.getQuantities().getAvailableForSale();
        
        if (availableForReservation.compareTo(quantity) < 0) {
            throw new RuntimeException("Insufficient stock available for reservation. Available: " + availableForReservation + ", Requested: " + quantity);
        }
        
        // Update quantities
        inventory.getQuantities().setReserved(inventory.getQuantities().getReserved().add(quantity));
        inventory.setLastUpdated(LocalDateTime.now());
        
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        // Log audit
        auditLogService.logUserAction(
            reservedByUserId,
            "RESERVE_STOCK",
            "INVENTORY",
            savedInventory.getId(),
            String.format("%s - Reserved: %s", inventory.getProduct().getProductName(), quantity)
        );
        
        log.info("Stock reserved successfully: {} units", quantity);
        return savedInventory;
    }
    
    /**
     * Release reserved stock
     */
    public Inventory releaseReservedStock(String productId, String warehouseId, BigDecimal quantity, String releasedByUserId) {
        log.info("Releasing reserved stock: {} units of product {} in warehouse {}", quantity, productId, warehouseId);
        
        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("Quantity must be greater than zero");
        }
        
        Inventory inventory = inventoryRepository.findByProductIdAndWarehouseId(productId, warehouseId)
                .orElseThrow(() -> new RuntimeException("Inventory not found for product in warehouse"));
        
        BigDecimal reservedQuantity = inventory.getQuantities().getReserved();
        
        if (reservedQuantity.compareTo(quantity) < 0) {
            throw new RuntimeException("Cannot release more than reserved. Reserved: " + reservedQuantity + ", Requested: " + quantity);
        }
        
        // Update quantities
        inventory.getQuantities().setReserved(reservedQuantity.subtract(quantity));
        inventory.setLastUpdated(LocalDateTime.now());
        
        Inventory savedInventory = inventoryRepository.save(inventory);
        
        // Log audit
        auditLogService.logUserAction(
            releasedByUserId,
            "RELEASE_STOCK",
            "INVENTORY",
            savedInventory.getId(),
            String.format("%s - Released: %s", inventory.getProduct().getProductName(), quantity)
        );
        
        log.info("Reserved stock released successfully: {} units", quantity);
        return savedInventory;
    }
    
    /**
     * Get inventory by product and warehouse
     */
    @Transactional(readOnly = true)
    public Optional<Inventory> getInventory(String productId, String warehouseId) {
        return inventoryRepository.findByProductIdAndWarehouseId(productId, warehouseId);
    }
    
    /**
     * Get all inventory for a product
     */
    @Transactional(readOnly = true)
    public List<Inventory> getProductInventory(String productId) {
        return inventoryRepository.findByProductId(productId);
    }
    
    /**
     * Get all inventory in a warehouse
     */
    @Transactional(readOnly = true)
    public List<Inventory> getWarehouseInventory(String warehouseId) {
        return inventoryRepository.findByWarehouseId(warehouseId);
    }
    
    /**
     * Get low stock inventory
     */
    @Transactional(readOnly = true)
    public List<Inventory> getLowStockInventory() {
        return inventoryRepository.findLowStockInventory();
    }
    
    /**
     * Get out of stock inventory
     */
    @Transactional(readOnly = true)
    public List<Inventory> getOutOfStockInventory() {
        return inventoryRepository.findOutOfStock();
    }
    
    /**
     * Calculate total inventory value
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateTotalInventoryValue() {
        return inventoryRepository.calculateTotalInventoryValue().orElse(BigDecimal.ZERO);
    }
    
    /**
     * Get inventory summary by warehouse
     */
    @Transactional(readOnly = true)
    public List<InventoryRepository.InventorySummary> getInventorySummaryByWarehouse() {
        return inventoryRepository.getInventorySummaryByWarehouse();
    }
    
    // Helper methods
    private void updateAverageCost(Inventory inventory, BigDecimal newQuantity, BigDecimal newCost) {
        if (newCost == null || newCost.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        
        BigDecimal currentQuantity = inventory.getQuantities().getAvailable();
        BigDecimal currentCost = inventory.getCostInfo().getAverageCost();
        
        if (currentQuantity.compareTo(BigDecimal.ZERO) == 0) {
            inventory.getCostInfo().setAverageCost(newCost);
        } else {
            BigDecimal totalValue = currentQuantity.multiply(currentCost).add(newQuantity.multiply(newCost));
            BigDecimal totalQuantity = currentQuantity.add(newQuantity);
            BigDecimal newAverageCost = totalValue.divide(totalQuantity, 2, BigDecimal.ROUND_HALF_UP);
            inventory.getCostInfo().setAverageCost(newAverageCost);
        }
        
        inventory.getCostInfo().setLastCost(newCost);
    }
    
    private void createStockMovement(String productId, String warehouseId, String movementType,
                                   BigDecimal quantity, BigDecimal unitCost, String referenceType,
                                   String referenceId, String notes, String createdByUserId) {
        Product product = productRepository.findById(productId).orElse(null);
        var warehouse = warehouseRepository.findById(warehouseId).orElse(null);
        
        if (product == null || warehouse == null) {
            log.warn("Cannot create stock movement - product or warehouse not found");
            return;
        }
        
        StockMovement stockMovement = StockMovement.builder()
                .product(StockMovement.MovementProduct.builder()
                        .productId(productId)
                        .productName(product.getName())
                        .sku(product.getSku())
                        .build())
                .warehouse(StockMovement.MovementWarehouse.builder()
                        .warehouseId(warehouseId)
                        .warehouseName(warehouse.getName())
                        .warehouseCode(warehouse.getCode())
                        .build())
                .movementType(movementType)
                .quantity(quantity)
                .unitCost(unitCost)
                .reference(StockMovement.MovementReference.builder()
                        .type(referenceType)
                        .referenceId(referenceId)
                        .build())
                .notes(notes)
                .createdBy(StockMovement.MovementUser.builder()
                        .userId(createdByUserId)
                        .userName(getUserName(createdByUserId))
                        .build())
                .build();
        
        stockMovementRepository.save(stockMovement);
    }
    
    private void checkLowStockAlert(Inventory inventory, String userId) {
        // This would typically check against product's minimum stock level
        // and send notifications if stock is low
        if (inventory.isLowStock(10)) { // Example threshold
            notificationService.sendLowStockAlert(
                userId,
                inventory.getProduct().getProductName(),
                inventory.getQuantities().getAvailable().intValue(),
                10
            );
        }
    }
    
    private String getUserName(String userId) {
        return "User-" + userId;
    }
    
    /**
     * Transfer result DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class TransferResult {
        private Inventory fromInventory;
        private Inventory toInventory;
        private BigDecimal transferredQuantity;
    }
}
