package com.kaydkapro.repository;

import com.kaydkapro.entity.Expense;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Expense Repository - Data access layer for Expense entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface ExpenseRepository extends MongoRepository<Expense, String> {
    
    /**
     * Find expenses by category
     */
    List<Expense> findByCategory(String category);
    
    /**
     * Find expenses by category with pagination
     */
    Page<Expense> findByCategory(String category, Pageable pageable);
    
    /**
     * Find expenses by date range
     */
    @Query("{'expenseDate': {'$gte': ?0, '$lte': ?1}}")
    List<Expense> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find expenses by date range with pagination
     */
    @Query("{'expenseDate': {'$gte': ?0, '$lte': ?1}}")
    Page<Expense> findByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * Find expenses by amount range
     */
    @Query("{'amount': {'$gte': ?0, '$lte': ?1}}")
    List<Expense> findByAmountRange(BigDecimal minAmount, BigDecimal maxAmount);
    
    /**
     * Find expenses by payment method
     */
    List<Expense> findByPaymentMethod(String paymentMethod);
    
    /**
     * Find expenses by vendor
     */
    List<Expense> findByVendor(String vendor);
    
    /**
     * Find recurring expenses
     */
    @Query("{'isRecurring': true}")
    List<Expense> findRecurringExpenses();
    
    /**
     * Find non-recurring expenses
     */
    @Query("{'isRecurring': false}")
    List<Expense> findNonRecurringExpenses();
    
    /**
     * Find expenses by reference type
     */
    @Query("{'reference.type': ?0}")
    List<Expense> findByReferenceType(String referenceType);
    
    /**
     * Find expenses by reference ID
     */
    @Query("{'reference.referenceId': ?0}")
    List<Expense> findByReferenceId(String referenceId);
    
    /**
     * Find expenses created by user
     */
    @Query("{'createdBy.userId': ?0}")
    List<Expense> findByCreatedByUserId(String userId);
    
    /**
     * Search expenses by title or description
     */
    @Query("{'$or': [" +
           "{'title': {'$regex': ?0, '$options': 'i'}}, " +
           "{'description': {'$regex': ?0, '$options': 'i'}}, " +
           "{'vendor': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    Page<Expense> searchExpenses(String searchTerm, Pageable pageable);
    
    /**
     * Find operational expenses
     */
    @Query("{'category': {'$in': ['RENT', 'UTILITIES', 'SALARIES', 'OFFICE_SUPPLIES']}}")
    List<Expense> findOperationalExpenses();
    
    /**
     * Find expenses with receipts
     */
    @Query("{'receiptImage': {'$ne': null, '$ne': ''}}")
    List<Expense> findExpensesWithReceipts();
    
    /**
     * Find expenses without receipts
     */
    @Query("{'$or': [{'receiptImage': null}, {'receiptImage': ''}]}")
    List<Expense> findExpensesWithoutReceipts();
    
    /**
     * Calculate total expenses for period
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'expenseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': null, 'totalExpenses': { '$sum': '$amount' } } }"
    })
    Optional<BigDecimal> calculateTotalExpenses(LocalDate startDate, LocalDate endDate);
    
    /**
     * Calculate total expenses by category for period
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'expenseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': '$category', 'totalAmount': { '$sum': '$amount' }, 'count': { '$sum': 1 } } }",
        "{ '$sort': { 'totalAmount': -1 } }"
    })
    List<ExpenseCategoryStats> getExpensesByCategoryStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Calculate total expenses by payment method for period
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'expenseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': '$paymentMethod', 'totalAmount': { '$sum': '$amount' }, 'count': { '$sum': 1 } } }",
        "{ '$sort': { 'totalAmount': -1 } }"
    })
    List<ExpensePaymentMethodStats> getExpensesByPaymentMethodStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get daily expense summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'expenseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': '$expenseDate', " +
        "  'totalExpenses': { '$sum': '$amount' }, " +
        "  'count': { '$sum': 1 }, " +
        "  'averageExpense': { '$avg': '$amount' } " +
        "} }",
        "{ '$sort': { '_id': 1 } }"
    })
    List<DailyExpenseStats> getDailyExpenseStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get monthly expense summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'expenseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': { 'year': { '$year': '$expenseDate' }, 'month': { '$month': '$expenseDate' } }, " +
        "  'totalExpenses': { '$sum': '$amount' }, " +
        "  'count': { '$sum': 1 }, " +
        "  'averageExpense': { '$avg': '$amount' } " +
        "} }",
        "{ '$sort': { '_id.year': 1, '_id.month': 1 } }"
    })
    List<MonthlyExpenseStats> getMonthlyExpenseStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get top vendors by expense amount
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'vendor': { '$ne': null, '$ne': '' } } }",
        "{ '$group': { " +
        "  '_id': '$vendor', " +
        "  'totalAmount': { '$sum': '$amount' }, " +
        "  'count': { '$sum': 1 }, " +
        "  'averageAmount': { '$avg': '$amount' }, " +
        "  'lastExpenseDate': { '$max': '$expenseDate' } " +
        "} }",
        "{ '$sort': { 'totalAmount': -1 } }",
        "{ '$limit': ?0 }"
    })
    List<TopVendorStats> getTopVendors(int limit);
    
    /**
     * Find large expenses (above threshold)
     */
    @Query("{'amount': {'$gte': ?0}}")
    List<Expense> findLargeExpenses(BigDecimal threshold);
    
    /**
     * Find recent expenses (last N days)
     */
    @Query("{'expenseDate': {'$gte': ?0}}")
    List<Expense> findRecentExpenses(LocalDate since);
    
    /**
     * Count expenses by category
     */
    long countByCategory(String category);
    
    /**
     * Count expenses by date range
     */
    @Query(value = "{'expenseDate': {'$gte': ?0, '$lte': ?1}}", count = true)
    long countByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Count recurring expenses
     */
    @Query(value = "{'isRecurring': true}", count = true)
    long countRecurringExpenses();
    
    /**
     * Find expenses by reference number
     */
    @Query("{'reference.referenceNumber': ?0}")
    List<Expense> findByReferenceNumber(String referenceNumber);
    
    /**
     * Find expenses today
     */
    @Query("{'expenseDate': ?0}")
    List<Expense> findExpensesToday(LocalDate today);
    
    /**
     * Find expenses this month
     */
    @Query("{'expenseDate': {'$gte': ?0, '$lte': ?1}}")
    List<Expense> findExpensesThisMonth(LocalDate monthStart, LocalDate monthEnd);
    
    /**
     * Interface for expense category statistics projection
     */
    interface ExpenseCategoryStats {
        String getId();
        BigDecimal getTotalAmount();
        Long getCount();
    }
    
    /**
     * Interface for expense payment method statistics projection
     */
    interface ExpensePaymentMethodStats {
        String getId();
        BigDecimal getTotalAmount();
        Long getCount();
    }
    
    /**
     * Interface for daily expense statistics projection
     */
    interface DailyExpenseStats {
        LocalDate getId();
        BigDecimal getTotalExpenses();
        Long getCount();
        BigDecimal getAverageExpense();
    }
    
    /**
     * Interface for monthly expense statistics projection
     */
    interface MonthlyExpenseStats {
        MonthYear getId();
        BigDecimal getTotalExpenses();
        Long getCount();
        BigDecimal getAverageExpense();
        
        interface MonthYear {
            Integer getYear();
            Integer getMonth();
        }
    }
    
    /**
     * Interface for top vendor statistics projection
     */
    interface TopVendorStats {
        String getId();
        BigDecimal getTotalAmount();
        Long getCount();
        BigDecimal getAverageAmount();
        LocalDate getLastExpenseDate();
    }
}
