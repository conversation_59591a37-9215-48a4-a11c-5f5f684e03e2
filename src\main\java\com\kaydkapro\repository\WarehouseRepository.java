package com.kaydkapro.repository;

import com.kaydkapro.entity.Warehouse;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Warehouse Repository - Data access layer for Warehouse entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface WarehouseRepository extends MongoRepository<Warehouse, String> {
    
    /**
     * Find warehouse by code
     */
    Optional<Warehouse> findByCode(String code);
    
    /**
     * Find warehouse by name
     */
    Optional<Warehouse> findByName(String name);
    
    /**
     * Find all active warehouses
     */
    List<Warehouse> findByIsActiveTrue();
    
    /**
     * Find warehouses by manager ID
     */
    @Query("{'manager.managerId': ?0}")
    List<Warehouse> findByManagerId(String managerId);
    
    /**
     * Find warehouses by city
     */
    @Query("{'address.city': ?0, 'isActive': true}")
    List<Warehouse> findByCity(String city);
    
    /**
     * Find warehouses by state
     */
    @Query("{'address.state': ?0, 'isActive': true}")
    List<Warehouse> findByState(String state);
    
    /**
     * Find warehouses by country
     */
    @Query("{'address.country': ?0, 'isActive': true}")
    List<Warehouse> findByCountry(String country);
    
    /**
     * Search warehouses by name, code, or address
     */
    @Query("{'$and': [{'isActive': true}, {'$or': [" +
           "{'name': {'$regex': ?0, '$options': 'i'}}, " +
           "{'code': {'$regex': ?0, '$options': 'i'}}, " +
           "{'address.city': {'$regex': ?0, '$options': 'i'}}, " +
           "{'address.state': {'$regex': ?0, '$options': 'i'}}" +
           "]}]}")
    List<Warehouse> searchWarehouses(String searchTerm);
    
    /**
     * Check if warehouse code exists
     */
    boolean existsByCode(String code);
    
    /**
     * Check if warehouse name exists
     */
    boolean existsByName(String name);
    
    /**
     * Check if warehouse code exists excluding current warehouse
     */
    @Query("{'code': ?0, '_id': {'$ne': ?1}}")
    boolean existsByCodeAndIdNot(String code, String id);
    
    /**
     * Check if warehouse name exists excluding current warehouse
     */
    @Query("{'name': ?0, '_id': {'$ne': ?1}}")
    boolean existsByNameAndIdNot(String name, String id);
    
    /**
     * Find warehouses created by user
     */
    List<Warehouse> findByCreatedBy(String createdBy);
    
    /**
     * Count active warehouses
     */
    long countByIsActiveTrue();
    
    /**
     * Find warehouses with capacity information
     */
    @Query("{'capacity': {'$ne': null}, 'isActive': true}")
    List<Warehouse> findWarehousesWithCapacity();
    
    /**
     * Find warehouses without manager
     */
    @Query("{'$or': [{'manager': null}, {'manager.managerId': null}], 'isActive': true}")
    List<Warehouse> findWarehousesWithoutManager();
    
    /**
     * Find warehouses with manager
     */
    @Query("{'manager.managerId': {'$ne': null}, 'isActive': true}")
    List<Warehouse> findWarehousesWithManager();
    
    /**
     * Find main warehouse (typically the first or primary warehouse)
     */
    @Query("{'code': {'$regex': '^MAIN', '$options': 'i'}, 'isActive': true}")
    Optional<Warehouse> findMainWarehouse();
    
    /**
     * Find warehouses by postal code
     */
    @Query("{'address.postalCode': ?0, 'isActive': true}")
    List<Warehouse> findByPostalCode(String postalCode);
}
