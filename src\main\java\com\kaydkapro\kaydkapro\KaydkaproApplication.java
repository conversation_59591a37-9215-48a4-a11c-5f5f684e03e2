package com.kaydkapro;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * KaydkaPro Application - Comprehensive Inventory and Business Management System
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024
 */
@SpringBootApplication
@EnableMongoAuditing
@EnableCaching
@EnableAsync
@EnableScheduling
public class KaydkaproApplication {

	public static void main(String[] args) {
		SpringApplication.run(KaydkaproApplication.class, args);
	}

}
