package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Warehouse Entity - Represents storage locations
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "warehouses")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Warehouse {
    
    @Id
    private String id;
    
    @NotBlank(message = "Warehouse name is required")
    @Size(max = 100, message = "Warehouse name must not exceed 100 characters")
    private String name;
    
    @NotBlank(message = "Warehouse code is required")
    @Size(max = 20, message = "Warehouse code must not exceed 20 characters")
    private String code;
    
    private WarehouseAddress address;
    
    private WarehouseManager manager;
    
    private BigDecimal capacity;
    
    @Builder.Default
    private Boolean isActive = true;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    private String createdBy;
    private String updatedBy;
    
    /**
     * Nested class for warehouse address
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WarehouseAddress {
        private String street;
        private String city;
        private String state;
        private String postalCode;
        private String country;
        
        public String getFullAddress() {
            StringBuilder sb = new StringBuilder();
            if (street != null) sb.append(street).append(", ");
            if (city != null) sb.append(city).append(", ");
            if (state != null) sb.append(state).append(" ");
            if (postalCode != null) sb.append(postalCode).append(", ");
            if (country != null) sb.append(country);
            return sb.toString().replaceAll(", $", "");
        }
    }
    
    /**
     * Nested class for warehouse manager
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WarehouseManager {
        private String managerId;
        private String managerName;
        private String email;
        private String phone;
    }
}
