# =============================================
# KAYDKAPRO APPLICATION CONFIGURATION
# =============================================

# Server Configuration
server.port=8080
server.servlet.context-path=/api
spring.application.name=kaydkapro

# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=kaydkapro_db
spring.data.mongodb.auto-index-creation=true

# Connection Pool Settings
spring.data.mongodb.connection-per-host=100
spring.data.mongodb.threads-allowed-to-block-for-connection-multiplier=5
spring.data.mongodb.connect-timeout=10000
spring.data.mongodb.socket-timeout=0
spring.data.mongodb.socket-keep-alive=true

# JWT Configuration
app.jwt.secret=kaydkapro2024SecretKeyForJWTTokenGenerationAndValidation
app.jwt.expiration=86400000
app.jwt.refresh-expiration=604800000

# Security Configuration
app.cors.allowed-origins=http://localhost:3000,http://localhost:3001
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.upload.dir=uploads/

# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME:}
spring.mail.password=${EMAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# AI Configuration (Langchain4j)
langchain4j.open-ai.api-key=${OPENAI_API_KEY:}
langchain4j.open-ai.model-name=gpt-3.5-turbo
langchain4j.open-ai.temperature=0.7
langchain4j.open-ai.max-tokens=1000

# Logging Configuration
logging.level.com.kaydkapro=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.data.mongodb=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/kaydkapro.log

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# OpenAPI Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# Cache Configuration
spring.cache.type=simple
spring.cache.cache-names=products,categories,users,inventory

# WebSocket Configuration
app.websocket.allowed-origins=http://localhost:3000,http://localhost:3001

# Business Configuration
app.business.name=KaydkaPro Business Solutions
app.business.address=Mogadishu, Somalia
app.business.phone=+252-1-234567
app.business.email=<EMAIL>
app.business.currency=USD
app.business.tax-rate=15.0

# QR Code Configuration
app.qr.base-url=http://localhost:8080/api
app.qr.image-format=PNG
app.qr.image-size=300

# Notification Configuration
app.notification.email.enabled=true
app.notification.sms.enabled=false
app.notification.push.enabled=true

# Report Configuration
app.reports.temp-dir=temp/reports/
app.reports.max-records=10000

# Development Profile
spring.profiles.active=dev
