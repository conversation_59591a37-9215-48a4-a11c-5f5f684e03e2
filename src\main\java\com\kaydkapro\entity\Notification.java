package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Notification Entity - Represents system notifications
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "notifications")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Notification {
    
    @Id
    private String id;
    
    @NotNull(message = "User information is required")
    private NotificationUser user;
    
    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    private String title;
    
    @NotBlank(message = "Message is required")
    @Size(max = 1000, message = "Message must not exceed 1000 characters")
    private String message;
    
    @NotBlank(message = "Type is required")
    private String type; // INFO, WARNING, ERROR, SUCCESS
    
    @NotBlank(message = "Category is required")
    private String category; // STOCK, SALE, PURCHASE, SYSTEM, USER
    
    @Builder.Default
    private Boolean isRead = false;
    
    private LocalDateTime readAt;
    
    @Builder.Default
    private Map<String, Object> data = new HashMap<>();
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationUser {
        private String userId;
        private String userName;
    }
    
    // Notification type constants
    public static final String TYPE_INFO = "INFO";
    public static final String TYPE_WARNING = "WARNING";
    public static final String TYPE_ERROR = "ERROR";
    public static final String TYPE_SUCCESS = "SUCCESS";
    
    // Notification category constants
    public static final String CATEGORY_STOCK = "STOCK";
    public static final String CATEGORY_SALE = "SALE";
    public static final String CATEGORY_PURCHASE = "PURCHASE";
    public static final String CATEGORY_SYSTEM = "SYSTEM";
    public static final String CATEGORY_USER = "USER";
    public static final String CATEGORY_FINANCIAL = "FINANCIAL";
    public static final String CATEGORY_INVENTORY = "INVENTORY";
    
    // Helper methods
    public void markAsRead() {
        this.isRead = true;
        this.readAt = LocalDateTime.now();
    }
    
    public boolean isUnread() {
        return !isRead;
    }
    
    public boolean isWarning() {
        return TYPE_WARNING.equals(type);
    }
    
    public boolean isError() {
        return TYPE_ERROR.equals(type);
    }
    
    public boolean isSuccess() {
        return TYPE_SUCCESS.equals(type);
    }
    
    public boolean isInfo() {
        return TYPE_INFO.equals(type);
    }
    
    public void addData(String key, Object value) {
        if (data == null) {
            data = new HashMap<>();
        }
        data.put(key, value);
    }
    
    public Object getData(String key) {
        return data != null ? data.get(key) : null;
    }
    
    public String getDataAsString(String key) {
        Object value = getData(key);
        return value != null ? value.toString() : null;
    }
    
    // Factory methods for common notifications
    public static Notification createLowStockAlert(String userId, String userName, 
                                                  String productName, int currentStock, int minStock) {
        return Notification.builder()
                .user(NotificationUser.builder()
                        .userId(userId)
                        .userName(userName)
                        .build())
                .title("Low Stock Alert")
                .message(String.format("%s is running low (Current: %d, Minimum: %d)", 
                        productName, currentStock, minStock))
                .type(TYPE_WARNING)
                .category(CATEGORY_STOCK)
                .data(Map.of(
                        "productName", productName,
                        "currentStock", currentStock,
                        "minimumStock", minStock,
                        "actionUrl", "/inventory/products/" + productName.toLowerCase().replace(" ", "-")
                ))
                .build();
    }
    
    public static Notification createSaleCompletedNotification(String userId, String userName, 
                                                             String saleNumber, String amount) {
        return Notification.builder()
                .user(NotificationUser.builder()
                        .userId(userId)
                        .userName(userName)
                        .build())
                .title("Sale Completed")
                .message(String.format("Sale %s completed successfully for %s", saleNumber, amount))
                .type(TYPE_SUCCESS)
                .category(CATEGORY_SALE)
                .data(Map.of(
                        "saleNumber", saleNumber,
                        "amount", amount,
                        "actionUrl", "/sales/" + saleNumber
                ))
                .build();
    }
    
    public static Notification createSystemNotification(String userId, String userName, 
                                                       String title, String message) {
        return Notification.builder()
                .user(NotificationUser.builder()
                        .userId(userId)
                        .userName(userName)
                        .build())
                .title(title)
                .message(message)
                .type(TYPE_INFO)
                .category(CATEGORY_SYSTEM)
                .build();
    }
}
