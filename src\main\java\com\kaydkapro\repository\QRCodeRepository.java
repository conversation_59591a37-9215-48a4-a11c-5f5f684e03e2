package com.kaydkapro.repository;

import com.kaydkapro.entity.QRCode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * QRCode Repository - Data access layer for QRCode entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface QRCodeRepository extends MongoRepository<QRCode, String> {
    
    /**
     * Find QR code by QR code value
     */
    Optional<QRCode> findByQrCode(String qrCode);
    
    /**
     * Find QR codes by product ID
     */
    @Query("{'product.productId': ?0}")
    List<QRCode> findByProductId(String productId);
    
    /**
     * Find QR codes by product SKU
     */
    @Query("{'product.sku': ?0}")
    List<QRCode> findByProductSku(String sku);
    
    /**
     * Find QR codes by type
     */
    List<QRCode> findByQrType(String qrType);
    
    /**
     * Find QR codes by type with pagination
     */
    Page<QRCode> findByQrType(String qrType, Pageable pageable);
    
    /**
     * Find all active QR codes
     */
    @Query("{'isActive': true}")
    List<QRCode> findActiveQRCodes();
    
    /**
     * Find all active QR codes with pagination
     */
    @Query("{'isActive': true}")
    Page<QRCode> findActiveQRCodes(Pageable pageable);
    
    /**
     * Find QR codes by product ID and type
     */
    @Query("{'product.productId': ?0, 'qrType': ?1}")
    List<QRCode> findByProductIdAndType(String productId, String qrType);
    
    /**
     * Find QR codes created by user
     */
    @Query("{'createdBy.userId': ?0}")
    List<QRCode> findByCreatedByUserId(String userId);
    
    /**
     * Find QR codes created after specific date
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<QRCode> findByCreatedAtAfter(LocalDateTime date);
    
    /**
     * Find QR codes by date range
     */
    @Query("{'createdAt': {'$gte': ?0, '$lte': ?1}}")
    List<QRCode> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Search QR codes by product name
     */
    @Query("{'product.productName': {'$regex': ?0, '$options': 'i'}, 'isActive': true}")
    Page<QRCode> searchByProductName(String productName, Pageable pageable);
    
    /**
     * Find QR codes with analytics data
     */
    @Query("{'analytics': {'$ne': null}}")
    List<QRCode> findQRCodesWithAnalytics();
    
    /**
     * Find QR codes with scan count above threshold
     */
    @Query("{'analytics.scanCount': {'$gte': ?0}}")
    List<QRCode> findQRCodesWithScansAbove(Integer scanCount);
    
    /**
     * Find QR codes scanned recently
     */
    @Query("{'analytics.lastScanned': {'$gte': ?0}}")
    List<QRCode> findRecentlyScannedQRCodes(LocalDateTime since);
    
    /**
     * Find QR codes never scanned
     */
    @Query("{'$or': [{'analytics': null}, {'analytics.scanCount': 0}]}")
    List<QRCode> findNeverScannedQRCodes();
    
    /**
     * Find most scanned QR codes
     */
    @Query("{'analytics.scanCount': {'$ne': null}}")
    List<QRCode> findMostScannedQRCodes(Pageable pageable);
    
    /**
     * Find QR codes by category
     */
    @Query("{'product.categoryName': ?0, 'isActive': true}")
    List<QRCode> findByProductCategory(String categoryName);
    
    /**
     * Check if QR code value exists
     */
    boolean existsByQrCode(String qrCode);
    
    /**
     * Check if QR code value exists excluding current QR code
     */
    @Query("{'qrCode': ?0, '_id': {'$ne': ?1}}")
    boolean existsByQrCodeAndIdNot(String qrCode, String id);
    
    /**
     * Count QR codes by type
     */
    long countByQrType(String qrType);
    
    /**
     * Count active QR codes
     */
    @Query(value = "{'isActive': true}", count = true)
    long countActiveQRCodes();
    
    /**
     * Count QR codes by product ID
     */
    @Query(value = "{'product.productId': ?0}", count = true)
    long countByProductId(String productId);
    
    /**
     * Find QR codes with specific metadata key
     */
    @Query("{'metadata.?0': {'$exists': true}}")
    List<QRCode> findByMetadataKey(String key);
    
    /**
     * Find QR codes with specific metadata value
     */
    @Query("{'metadata.?0': ?1}")
    List<QRCode> findByMetadataKeyValue(String key, Object value);
    
    /**
     * Find product info QR codes
     */
    @Query("{'qrType': 'PRODUCT_INFO', 'isActive': true}")
    List<QRCode> findProductInfoQRCodes();
    
    /**
     * Find inventory QR codes
     */
    @Query("{'qrType': 'INVENTORY', 'isActive': true}")
    List<QRCode> findInventoryQRCodes();
    
    /**
     * Find checkout QR codes
     */
    @Query("{'qrType': 'CHECKOUT', 'isActive': true}")
    List<QRCode> findCheckoutQRCodes();
    
    /**
     * Find price check QR codes
     */
    @Query("{'qrType': 'PRICE_CHECK', 'isActive': true}")
    List<QRCode> findPriceCheckQRCodes();
    
    /**
     * Find QR codes scanned today
     */
    @Query("{'analytics.lastScanned': {'$gte': ?0, '$lt': ?1}}")
    List<QRCode> findQRCodesScannedToday(LocalDateTime startOfDay, LocalDateTime endOfDay);
    
    /**
     * Find QR codes by warehouse (from metadata)
     */
    @Query("{'metadata.warehouseId': ?0, 'isActive': true}")
    List<QRCode> findByWarehouseId(String warehouseId);
    
    /**
     * Find QR codes created this month
     */
    @Query("{'createdAt': {'$gte': ?0, '$lt': ?1}}")
    List<QRCode> findQRCodesCreatedThisMonth(LocalDateTime monthStart, LocalDateTime monthEnd);
    
    /**
     * Find all distinct QR types
     */
    @Query(value = "{}", fields = "{'qrType': 1}")
    List<String> findDistinctQRTypes();
    
    /**
     * Find QR codes with high scan activity (scanned more than X times)
     */
    @Query("{'analytics.scanCount': {'$gte': ?0}}")
    List<QRCode> findHighActivityQRCodes(Integer minScans);
    
    /**
     * Find QR codes with low scan activity (scanned less than X times)
     */
    @Query("{'analytics.scanCount': {'$lt': ?0}}")
    List<QRCode> findLowActivityQRCodes(Integer maxScans);
    
    /**
     * Find QR codes by scan date range
     */
    @Query("{'analytics.lastScanned': {'$gte': ?0, '$lte': ?1}}")
    List<QRCode> findByLastScannedDateRange(LocalDateTime startDate, LocalDateTime endDate);
}
