package com.kaydkapro.controller;

import com.kaydkapro.dto.request.AIQueryRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.service.AIService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * AI Controller - REST API endpoints for AI-powered features
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/ai")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class AIController {
    
    private final AIService aiService;
    
    /**
     * Process natural language query
     */
    @PostMapping("/query")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<AIService.AIResponse>> processQuery(@Valid @RequestBody AIQueryRequest queryRequest,
                                                                         Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            AIService.AIResponse response = aiService.processQuery(
                queryRequest.getQuery(),
                queryRequest.getContext(),
                userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Query processed successfully", response));
            
        } catch (Exception e) {
            log.error("Failed to process AI query: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get inventory insights using AI
     */
    @GetMapping("/insights/inventory")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AIService.InventoryInsights>> getInventoryInsights() {
        try {
            AIService.InventoryInsights insights = aiService.generateInventoryInsights();
            return ResponseEntity.ok(ApiResponse.success("Inventory insights generated", insights));
            
        } catch (Exception e) {
            log.error("Failed to generate inventory insights: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get sales predictions using AI
     */
    @GetMapping("/predictions/sales")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AIService.SalesPredictions>> getSalesPredictions(
            @RequestParam(defaultValue = "30") int days) {
        try {
            AIService.SalesPredictions predictions = aiService.generateSalesPredictions(days);
            return ResponseEntity.ok(ApiResponse.success("Sales predictions generated", predictions));
            
        } catch (Exception e) {
            log.error("Failed to generate sales predictions: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get product recommendations using AI
     */
    @GetMapping("/recommendations/products")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<AIService.ProductRecommendation>>> getProductRecommendations(
            @RequestParam(required = false) String customerId,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<AIService.ProductRecommendation> recommendations = aiService.getProductRecommendations(customerId, limit);
            return ResponseEntity.ok(ApiResponse.success("Product recommendations generated", recommendations));
            
        } catch (Exception e) {
            log.error("Failed to generate product recommendations: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get demand forecasting using AI
     */
    @GetMapping("/forecast/demand")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AIService.DemandForecast>> getDemandForecast(
            @RequestParam String productId,
            @RequestParam(defaultValue = "30") int days) {
        try {
            AIService.DemandForecast forecast = aiService.generateDemandForecast(productId, days);
            return ResponseEntity.ok(ApiResponse.success("Demand forecast generated", forecast));
            
        } catch (Exception e) {
            log.error("Failed to generate demand forecast: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get pricing optimization suggestions using AI
     */
    @GetMapping("/optimization/pricing")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<AIService.PricingOptimization>>> getPricingOptimization(
            @RequestParam(required = false) String categoryId) {
        try {
            List<AIService.PricingOptimization> optimizations = aiService.generatePricingOptimization(categoryId);
            return ResponseEntity.ok(ApiResponse.success("Pricing optimization generated", optimizations));
            
        } catch (Exception e) {
            log.error("Failed to generate pricing optimization: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get business insights using AI
     */
    @GetMapping("/insights/business")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AIService.BusinessInsights>> getBusinessInsights() {
        try {
            AIService.BusinessInsights insights = aiService.generateBusinessInsights();
            return ResponseEntity.ok(ApiResponse.success("Business insights generated", insights));
            
        } catch (Exception e) {
            log.error("Failed to generate business insights: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get anomaly detection results using AI
     */
    @GetMapping("/anomalies")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<AIService.Anomaly>>> getAnomalies(
            @RequestParam(defaultValue = "sales,inventory,financial") String types) {
        try {
            List<String> anomalyTypes = List.of(types.split(","));
            List<AIService.Anomaly> anomalies = aiService.detectAnomalies(anomalyTypes);
            return ResponseEntity.ok(ApiResponse.success("Anomalies detected", anomalies));
            
        } catch (Exception e) {
            log.error("Failed to detect anomalies: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get AI-powered search suggestions
     */
    @GetMapping("/search/suggestions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<String>>> getSearchSuggestions(@RequestParam String query) {
        try {
            List<String> suggestions = aiService.getSearchSuggestions(query);
            return ResponseEntity.ok(ApiResponse.success("Search suggestions generated", suggestions));
            
        } catch (Exception e) {
            log.error("Failed to generate search suggestions: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get AI chat response
     */
    @PostMapping("/chat")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<AIService.ChatResponse>> chat(@RequestParam String message,
                                                                   @RequestParam(required = false) String conversationId,
                                                                   Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            AIService.ChatResponse response = aiService.processChat(
                message,
                conversationId,
                userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Chat response generated", response));
            
        } catch (Exception e) {
            log.error("Failed to process chat: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get AI capabilities and features
     */
    @GetMapping("/capabilities")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAICapabilities() {
        try {
            Map<String, Object> capabilities = aiService.getAICapabilities();
            return ResponseEntity.ok(ApiResponse.success("AI capabilities retrieved", capabilities));
            
        } catch (Exception e) {
            log.error("Failed to get AI capabilities: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
