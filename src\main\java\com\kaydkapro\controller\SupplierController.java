package com.kaydkapro.controller;

import com.kaydkapro.dto.request.SupplierRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.Supplier;
import com.kaydkapro.service.SupplierService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * Supplier Controller - REST API endpoints for supplier management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/suppliers")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class SupplierController {
    
    private final SupplierService supplierService;
    
    /**
     * Create a new supplier
     */
    @PostMapping
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Supplier>> createSupplier(@Valid @RequestBody SupplierRequest supplierRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Supplier supplier = supplierRequest.toEntity();
            Supplier createdSupplier = supplierService.createSupplier(supplier, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Supplier created successfully", createdSupplier));
            
        } catch (Exception e) {
            log.error("Failed to create supplier: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update an existing supplier
     */
    @PutMapping("/{supplierId}")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Supplier>> updateSupplier(@PathVariable String supplierId,
                                                               @Valid @RequestBody SupplierRequest supplierRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Supplier supplierUpdates = supplierRequest.toEntity();
            Supplier updatedSupplier = supplierService.updateSupplier(supplierId, supplierUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Supplier updated successfully", updatedSupplier));
            
        } catch (Exception e) {
            log.error("Failed to update supplier {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get supplier by ID
     */
    @GetMapping("/{supplierId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Supplier>> getSupplier(@PathVariable String supplierId) {
        try {
            Supplier supplier = supplierService.getSupplierById(supplierId);
            return ResponseEntity.ok(ApiResponse.success("Supplier retrieved successfully", supplier));
            
        } catch (Exception e) {
            log.error("Failed to get supplier {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Supplier not found"));
        }
    }
    
    /**
     * Get supplier by code
     */
    @GetMapping("/code/{supplierCode}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Supplier>> getSupplierByCode(@PathVariable String supplierCode) {
        try {
            Supplier supplier = supplierService.getSupplierByCode(supplierCode);
            return ResponseEntity.ok(ApiResponse.success("Supplier retrieved successfully", supplier));
            
        } catch (Exception e) {
            log.error("Failed to get supplier by code {}: {}", supplierCode, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Supplier not found"));
        }
    }
    
    /**
     * Get all suppliers with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Supplier>>> getSuppliers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "companyName") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Supplier> supplierPage = supplierService.getSuppliers(pageable);
            PageResponse<Supplier> pageResponse = PageResponse.of(supplierPage);
            
            return ResponseEntity.ok(ApiResponse.success("Suppliers retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get suppliers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search suppliers
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Supplier>>> searchSuppliers(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Supplier> supplierPage = supplierService.searchSuppliers(query, pageable);
            PageResponse<Supplier> pageResponse = PageResponse.of(supplierPage);
            
            return ResponseEntity.ok(ApiResponse.success("Supplier search completed", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to search suppliers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get suppliers by category
     */
    @GetMapping("/category/{category}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Supplier>>> getSuppliersByCategory(@PathVariable String category) {
        try {
            List<Supplier> suppliers = supplierService.getSuppliersByCategory(category);
            return ResponseEntity.ok(ApiResponse.success("Suppliers retrieved successfully", suppliers));
            
        } catch (Exception e) {
            log.error("Failed to get suppliers by category {}: {}", category, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get active suppliers
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Supplier>>> getActiveSuppliers() {
        try {
            List<Supplier> suppliers = supplierService.getActiveSuppliers();
            return ResponseEntity.ok(ApiResponse.success("Active suppliers retrieved successfully", suppliers));
            
        } catch (Exception e) {
            log.error("Failed to get active suppliers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get preferred suppliers
     */
    @GetMapping("/preferred")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Supplier>>> getPreferredSuppliers() {
        try {
            List<Supplier> suppliers = supplierService.getPreferredSuppliers();
            return ResponseEntity.ok(ApiResponse.success("Preferred suppliers retrieved successfully", suppliers));
            
        } catch (Exception e) {
            log.error("Failed to get preferred suppliers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get suppliers by location
     */
    @GetMapping("/location")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Supplier>>> getSuppliersByLocation(@RequestParam String city,
                                                                             @RequestParam(required = false) String state,
                                                                             @RequestParam(required = false) String country) {
        try {
            List<Supplier> suppliers = supplierService.getSuppliersByLocation(city, state, country);
            return ResponseEntity.ok(ApiResponse.success("Suppliers retrieved successfully", suppliers));
            
        } catch (Exception e) {
            log.error("Failed to get suppliers by location: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update supplier rating
     */
    @PatchMapping("/{supplierId}/rating")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Supplier>> updateSupplierRating(@PathVariable String supplierId,
                                                                     @RequestParam BigDecimal rating,
                                                                     Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Supplier supplier = supplierService.updateSupplierRating(supplierId, rating, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Supplier rating updated successfully", supplier));
            
        } catch (Exception e) {
            log.error("Failed to update supplier rating {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Set supplier as preferred
     */
    @PatchMapping("/{supplierId}/preferred")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Supplier>> setSupplierPreferred(@PathVariable String supplierId,
                                                                     @RequestParam boolean isPreferred,
                                                                     Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Supplier supplier = supplierService.setSupplierPreferred(supplierId, isPreferred, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Supplier preferred status updated", supplier));
            
        } catch (Exception e) {
            log.error("Failed to set supplier preferred status {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Toggle supplier status (activate/deactivate)
     */
    @PatchMapping("/{supplierId}/status")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Supplier>> toggleSupplierStatus(@PathVariable String supplierId,
                                                                     @RequestParam boolean isActive,
                                                                     Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Supplier supplier = supplierService.toggleSupplierStatus(supplierId, isActive, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Supplier status updated", supplier));
            
        } catch (Exception e) {
            log.error("Failed to toggle supplier status {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get supplier performance metrics
     */
    @GetMapping("/{supplierId}/performance")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SupplierService.SupplierPerformance>> getSupplierPerformance(@PathVariable String supplierId) {
        try {
            SupplierService.SupplierPerformance performance = supplierService.getSupplierPerformance(supplierId);
            return ResponseEntity.ok(ApiResponse.success("Supplier performance retrieved", performance));
            
        } catch (Exception e) {
            log.error("Failed to get supplier performance {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get top suppliers by performance
     */
    @GetMapping("/top-performers")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<Supplier>>> getTopPerformingSuppliers(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Supplier> suppliers = supplierService.getTopPerformingSuppliers(limit);
            return ResponseEntity.ok(ApiResponse.success("Top performing suppliers retrieved", suppliers));
            
        } catch (Exception e) {
            log.error("Failed to get top performing suppliers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get supplier statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SupplierService.SupplierStats>> getSupplierStatistics() {
        try {
            SupplierService.SupplierStats stats = supplierService.getSupplierStats();
            return ResponseEntity.ok(ApiResponse.success("Supplier statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get supplier statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete supplier (soft delete)
     */
    @DeleteMapping("/{supplierId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteSupplier(@PathVariable String supplierId,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            supplierService.deleteSupplier(supplierId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Supplier deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete supplier {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
