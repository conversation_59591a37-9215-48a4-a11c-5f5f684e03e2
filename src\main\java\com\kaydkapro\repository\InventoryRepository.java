package com.kaydkapro.repository;

import com.kaydkapro.entity.Inventory;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Inventory Repository - Data access layer for Inventory entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface InventoryRepository extends MongoRepository<Inventory, String> {
    
    /**
     * Find inventory by product and warehouse
     */
    @Query("{'product.productId': ?0, 'warehouse.warehouseId': ?1}")
    Optional<Inventory> findByProductIdAndWarehouseId(String productId, String warehouseId);
    
    /**
     * Find all inventory for a product across all warehouses
     */
    @Query("{'product.productId': ?0}")
    List<Inventory> findByProductId(String productId);
    
    /**
     * Find all inventory in a warehouse
     */
    @Query("{'warehouse.warehouseId': ?0}")
    List<Inventory> findByWarehouseId(String warehouseId);
    
    /**
     * Find inventory with available stock
     */
    @Query("{'quantities.available': {'$gt': 0}}")
    List<Inventory> findWithAvailableStock();
    
    /**
     * Find inventory with available stock in specific warehouse
     */
    @Query("{'warehouse.warehouseId': ?0, 'quantities.available': {'$gt': 0}}")
    List<Inventory> findWithAvailableStockInWarehouse(String warehouseId);
    
    /**
     * Find out of stock inventory
     */
    @Query("{'quantities.available': {'$lte': 0}}")
    List<Inventory> findOutOfStock();
    
    /**
     * Find out of stock inventory in specific warehouse
     */
    @Query("{'warehouse.warehouseId': ?0, 'quantities.available': {'$lte': 0}}")
    List<Inventory> findOutOfStockInWarehouse(String warehouseId);
    
    /**
     * Find inventory with reserved stock
     */
    @Query("{'quantities.reserved': {'$gt': 0}}")
    List<Inventory> findWithReservedStock();
    
    /**
     * Find inventory with stock on order
     */
    @Query("{'quantities.onOrder': {'$gt': 0}}")
    List<Inventory> findWithStockOnOrder();
    
    /**
     * Find low stock inventory (requires product lookup for min stock level)
     */
    @Aggregation(pipeline = {
        "{ '$lookup': { 'from': 'products', 'localField': 'product.productId', 'foreignField': '_id', 'as': 'productDetails' } }",
        "{ '$unwind': '$productDetails' }",
        "{ '$match': { '$expr': { '$and': [ " +
        "  { '$ne': ['$productDetails.stockLevels.minStockLevel', null] }, " +
        "  { '$lte': ['$quantities.available', '$productDetails.stockLevels.minStockLevel'] } " +
        "] } } }",
        "{ '$project': { 'productDetails': 0 } }"
    })
    List<Inventory> findLowStockInventory();
    
    /**
     * Find low stock inventory in specific warehouse
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'warehouse.warehouseId': ?0 } }",
        "{ '$lookup': { 'from': 'products', 'localField': 'product.productId', 'foreignField': '_id', 'as': 'productDetails' } }",
        "{ '$unwind': '$productDetails' }",
        "{ '$match': { '$expr': { '$and': [ " +
        "  { '$ne': ['$productDetails.stockLevels.minStockLevel', null] }, " +
        "  { '$lte': ['$quantities.available', '$productDetails.stockLevels.minStockLevel'] } " +
        "] } } }",
        "{ '$project': { 'productDetails': 0 } }"
    })
    List<Inventory> findLowStockInventoryInWarehouse(String warehouseId);
    
    /**
     * Calculate total inventory value
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'costInfo.averageCost': { '$ne': null } } }",
        "{ '$group': { '_id': null, 'totalValue': { '$sum': { '$multiply': ['$quantities.available', '$costInfo.averageCost'] } } } }"
    })
    Optional<BigDecimal> calculateTotalInventoryValue();
    
    /**
     * Calculate inventory value by warehouse
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'warehouse.warehouseId': ?0, 'costInfo.averageCost': { '$ne': null } } }",
        "{ '$group': { '_id': null, 'totalValue': { '$sum': { '$multiply': ['$quantities.available', '$costInfo.averageCost'] } } } }"
    })
    Optional<BigDecimal> calculateInventoryValueByWarehouse(String warehouseId);
    
    /**
     * Get inventory summary by warehouse
     */
    @Aggregation(pipeline = {
        "{ '$group': { " +
        "  '_id': '$warehouse.warehouseId', " +
        "  'warehouseName': { '$first': '$warehouse.warehouseName' }, " +
        "  'totalProducts': { '$sum': 1 }, " +
        "  'totalStock': { '$sum': '$quantities.available' }, " +
        "  'totalReserved': { '$sum': '$quantities.reserved' }, " +
        "  'totalOnOrder': { '$sum': '$quantities.onOrder' }, " +
        "  'totalValue': { '$sum': { '$multiply': ['$quantities.available', '$costInfo.averageCost'] } } " +
        "} }"
    })
    List<InventorySummary> getInventorySummaryByWarehouse();
    
    /**
     * Get inventory summary by product
     */
    @Aggregation(pipeline = {
        "{ '$group': { " +
        "  '_id': '$product.productId', " +
        "  'productName': { '$first': '$product.productName' }, " +
        "  'sku': { '$first': '$product.sku' }, " +
        "  'totalStock': { '$sum': '$quantities.available' }, " +
        "  'totalReserved': { '$sum': '$quantities.reserved' }, " +
        "  'totalOnOrder': { '$sum': '$quantities.onOrder' }, " +
        "  'warehouseCount': { '$sum': 1 }, " +
        "  'averageCost': { '$avg': '$costInfo.averageCost' }, " +
        "  'totalValue': { '$sum': { '$multiply': ['$quantities.available', '$costInfo.averageCost'] } } " +
        "} }"
    })
    List<ProductInventorySummary> getInventorySummaryByProduct();
    
    /**
     * Find inventory by product SKU
     */
    @Query("{'product.sku': ?0}")
    List<Inventory> findByProductSku(String sku);
    
    /**
     * Find inventory by product name (case insensitive)
     */
    @Query("{'product.productName': {'$regex': ?0, '$options': 'i'}}")
    List<Inventory> findByProductName(String productName);
    
    /**
     * Find inventory with high value (above threshold)
     */
    @Query("{'$expr': { '$gt': [{ '$multiply': ['$quantities.available', '$costInfo.averageCost'] }, ?0] }}")
    List<Inventory> findHighValueInventory(BigDecimal threshold);
    
    /**
     * Count total products in inventory
     */
    long count();
    
    /**
     * Count products with available stock
     */
    @Query(value = "{'quantities.available': {'$gt': 0}}", count = true)
    long countWithAvailableStock();
    
    /**
     * Count out of stock products
     */
    @Query(value = "{'quantities.available': {'$lte': 0}}", count = true)
    long countOutOfStock();
    
    /**
     * Interface for inventory summary projection
     */
    interface InventorySummary {
        String getId();
        String getWarehouseName();
        Long getTotalProducts();
        BigDecimal getTotalStock();
        BigDecimal getTotalReserved();
        BigDecimal getTotalOnOrder();
        BigDecimal getTotalValue();
    }
    
    /**
     * Interface for product inventory summary projection
     */
    interface ProductInventorySummary {
        String getId();
        String getProductName();
        String getSku();
        BigDecimal getTotalStock();
        BigDecimal getTotalReserved();
        BigDecimal getTotalOnOrder();
        Long getWarehouseCount();
        BigDecimal getAverageCost();
        BigDecimal getTotalValue();
    }
}
