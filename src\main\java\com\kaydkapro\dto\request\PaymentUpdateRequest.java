package com.kaydkapro.dto.request;

import lombok.Data;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Payment Update Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class PaymentUpdateRequest {
    
    @NotBlank(message = "Payment status is required")
    private String paymentStatus;
    
    @NotNull(message = "Paid amount is required")
    @DecimalMin(value = "0.0", message = "Paid amount must be non-negative")
    private BigDecimal paidAmount;
    
    @NotBlank(message = "Payment method is required")
    private String paymentMethod;
    
    private String paymentReference;
    
    private String notes;
}
