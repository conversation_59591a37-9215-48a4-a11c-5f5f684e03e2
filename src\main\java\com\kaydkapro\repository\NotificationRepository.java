package com.kaydkapro.repository;

import com.kaydkapro.entity.Notification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Notification Repository - Data access layer for Notification entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface NotificationRepository extends MongoRepository<Notification, String> {
    
    /**
     * Find notifications by user ID
     */
    @Query("{'user.userId': ?0}")
    List<Notification> findByUserId(String userId);
    
    /**
     * Find notifications by user ID with pagination
     */
    @Query("{'user.userId': ?0}")
    Page<Notification> findByUserId(String userId, Pageable pageable);
    
    /**
     * Find unread notifications by user ID
     */
    @Query("{'user.userId': ?0, 'isRead': false}")
    List<Notification> findUnreadByUserId(String userId);
    
    /**
     * Find unread notifications by user ID with pagination
     */
    @Query("{'user.userId': ?0, 'isRead': false}")
    Page<Notification> findUnreadByUserId(String userId, Pageable pageable);
    
    /**
     * Find read notifications by user ID
     */
    @Query("{'user.userId': ?0, 'isRead': true}")
    List<Notification> findReadByUserId(String userId);
    
    /**
     * Find notifications by type
     */
    List<Notification> findByType(String type);
    
    /**
     * Find notifications by category
     */
    List<Notification> findByCategory(String category);
    
    /**
     * Find notifications by user ID and type
     */
    @Query("{'user.userId': ?0, 'type': ?1}")
    List<Notification> findByUserIdAndType(String userId, String type);
    
    /**
     * Find notifications by user ID and category
     */
    @Query("{'user.userId': ?0, 'category': ?1}")
    List<Notification> findByUserIdAndCategory(String userId, String category);
    
    /**
     * Find notifications created after specific date
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<Notification> findByCreatedAtAfter(LocalDateTime date);
    
    /**
     * Find notifications by user ID created after specific date
     */
    @Query("{'user.userId': ?0, 'createdAt': {'$gte': ?1}}")
    List<Notification> findByUserIdAndCreatedAtAfter(String userId, LocalDateTime date);
    
    /**
     * Find notifications by date range
     */
    @Query("{'createdAt': {'$gte': ?0, '$lte': ?1}}")
    List<Notification> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find notifications by user ID and date range
     */
    @Query("{'user.userId': ?0, 'createdAt': {'$gte': ?1, '$lte': ?2}}")
    List<Notification> findByUserIdAndDateRange(String userId, LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Count unread notifications by user ID
     */
    @Query(value = "{'user.userId': ?0, 'isRead': false}", count = true)
    long countUnreadByUserId(String userId);
    
    /**
     * Count notifications by user ID and type
     */
    @Query(value = "{'user.userId': ?0, 'type': ?1}", count = true)
    long countByUserIdAndType(String userId, String type);
    
    /**
     * Count notifications by user ID and category
     */
    @Query(value = "{'user.userId': ?0, 'category': ?1}", count = true)
    long countByUserIdAndCategory(String userId, String category);
    
    /**
     * Find warning notifications by user ID
     */
    @Query("{'user.userId': ?0, 'type': 'WARNING'}")
    List<Notification> findWarningsByUserId(String userId);
    
    /**
     * Find error notifications by user ID
     */
    @Query("{'user.userId': ?0, 'type': 'ERROR'}")
    List<Notification> findErrorsByUserId(String userId);
    
    /**
     * Find success notifications by user ID
     */
    @Query("{'user.userId': ?0, 'type': 'SUCCESS'}")
    List<Notification> findSuccessByUserId(String userId);
    
    /**
     * Find stock notifications by user ID
     */
    @Query("{'user.userId': ?0, 'category': 'STOCK'}")
    List<Notification> findStockNotificationsByUserId(String userId);
    
    /**
     * Find system notifications by user ID
     */
    @Query("{'user.userId': ?0, 'category': 'SYSTEM'}")
    List<Notification> findSystemNotificationsByUserId(String userId);
    
    /**
     * Find recent notifications (last 24 hours) by user ID
     */
    @Query("{'user.userId': ?0, 'createdAt': {'$gte': ?1}}")
    List<Notification> findRecentByUserId(String userId, LocalDateTime since);
    
    /**
     * Find old notifications (older than specified date)
     */
    @Query("{'createdAt': {'$lt': ?0}}")
    List<Notification> findOldNotifications(LocalDateTime before);
    
    /**
     * Find notifications with specific data key
     */
    @Query("{'data.?0': {'$exists': true}}")
    List<Notification> findByDataKey(String key);
    
    /**
     * Find notifications with specific data value
     */
    @Query("{'data.?0': ?1}")
    List<Notification> findByDataKeyValue(String key, Object value);
    
    /**
     * Delete old notifications (cleanup)
     */
    void deleteByCreatedAtBefore(LocalDateTime before);
    
    /**
     * Delete read notifications older than specified date
     */
    @Query(value = "{'isRead': true, 'createdAt': {'$lt': ?0}}", delete = true)
    void deleteReadNotificationsBefore(LocalDateTime before);
    
    /**
     * Mark all notifications as read for a user
     */
    @Query("{'user.userId': ?0, 'isRead': false}")
    List<Notification> findUnreadForMarkingAsRead(String userId);
    
    /**
     * Find notifications by title (case insensitive)
     */
    @Query("{'title': {'$regex': ?0, '$options': 'i'}}")
    List<Notification> findByTitleContaining(String title);
    
    /**
     * Find notifications by message content (case insensitive)
     */
    @Query("{'message': {'$regex': ?0, '$options': 'i'}}")
    List<Notification> findByMessageContaining(String message);
    
    /**
     * Search notifications by title or message
     */
    @Query("{'$or': [" +
           "{'title': {'$regex': ?0, '$options': 'i'}}, " +
           "{'message': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    List<Notification> searchNotifications(String searchTerm);
    
    /**
     * Find all notification types
     */
    @Query(value = "{}", fields = "{'type': 1}")
    List<String> findDistinctTypes();
    
    /**
     * Find all notification categories
     */
    @Query(value = "{}", fields = "{'category': 1}")
    List<String> findDistinctCategories();
}
