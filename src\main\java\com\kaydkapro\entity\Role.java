package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Role Entity - Represents user roles and permissions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "roles")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Role {
    
    @Id
    private String id;
    
    @NotBlank(message = "Role name is required")
    @Size(max = 50, message = "Role name must not exceed 50 characters")
    private String name;
    
    @Size(max = 200, message = "Description must not exceed 200 characters")
    private String description;
    
    @Builder.Default
    private List<String> permissions = new ArrayList<>();
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    // Helper methods
    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }
    
    public void addPermission(String permission) {
        if (!permissions.contains(permission)) {
            permissions.add(permission);
        }
    }
    
    public void removePermission(String permission) {
        permissions.remove(permission);
    }
    
    // Common role constants
    public static final String ADMIN = "ADMIN";
    public static final String MANAGER = "MANAGER";
    public static final String CASHIER = "CASHIER";
    public static final String STOREKEEPER = "STOREKEEPER";
    
    // Common permissions
    public static final String READ_ALL = "READ_ALL";
    public static final String WRITE_ALL = "WRITE_ALL";
    public static final String DELETE_ALL = "DELETE_ALL";
    public static final String MANAGE_USERS = "MANAGE_USERS";
    public static final String MANAGE_PRODUCTS = "MANAGE_PRODUCTS";
    public static final String MANAGE_INVENTORY = "MANAGE_INVENTORY";
    public static final String MANAGE_SALES = "MANAGE_SALES";
    public static final String MANAGE_PURCHASES = "MANAGE_PURCHASES";
    public static final String MANAGE_REPORTS = "MANAGE_REPORTS";
    public static final String MANAGE_SETTINGS = "MANAGE_SETTINGS";
}
