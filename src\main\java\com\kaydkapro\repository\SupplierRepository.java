package com.kaydkapro.repository;

import com.kaydkapro.entity.Supplier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Supplier Repository - Data access layer for Supplier entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface SupplierRepository extends MongoRepository<Supplier, String> {
    
    /**
     * Find supplier by name
     */
    Optional<Supplier> findByName(String name);
    
    /**
     * Find all active suppliers
     */
    List<Supplier> findByIsActiveTrue();
    
    /**
     * Find all active suppliers with pagination
     */
    Page<Supplier> findByIsActiveTrue(Pageable pageable);
    
    /**
     * Find suppliers by contact person
     */
    List<Supplier> findByContactPerson(String contactPerson);
    
    /**
     * Find suppliers by email
     */
    @Query("{'contact.email': ?0}")
    List<Supplier> findByEmail(String email);
    
    /**
     * Find suppliers by phone
     */
    @Query("{'contact.phone': ?0}")
    List<Supplier> findByPhone(String phone);
    
    /**
     * Find suppliers by city
     */
    @Query("{'address.city': ?0, 'isActive': true}")
    List<Supplier> findByCity(String city);
    
    /**
     * Find suppliers by state
     */
    @Query("{'address.state': ?0, 'isActive': true}")
    List<Supplier> findByState(String state);
    
    /**
     * Find suppliers by country
     */
    @Query("{'address.country': ?0, 'isActive': true}")
    List<Supplier> findByCountry(String country);
    
    /**
     * Find suppliers by tax number
     */
    @Query("{'businessInfo.taxNumber': ?0}")
    Optional<Supplier> findByTaxNumber(String taxNumber);
    
    /**
     * Search suppliers by name, contact person, or email
     */
    @Query("{'$and': [{'isActive': true}, {'$or': [" +
           "{'name': {'$regex': ?0, '$options': 'i'}}, " +
           "{'contactPerson': {'$regex': ?0, '$options': 'i'}}, " +
           "{'contact.email': {'$regex': ?0, '$options': 'i'}}, " +
           "{'address.city': {'$regex': ?0, '$options': 'i'}}" +
           "]}]}")
    Page<Supplier> searchSuppliers(String searchTerm, Pageable pageable);
    
    /**
     * Find suppliers with good rating (4.0 and above)
     */
    @Query("{'businessInfo.rating': {'$gte': 4.0}, 'isActive': true}")
    List<Supplier> findSuppliersWithGoodRating();
    
    /**
     * Find suppliers by rating range
     */
    @Query("{'businessInfo.rating': {'$gte': ?0, '$lte': ?1}, 'isActive': true}")
    List<Supplier> findByRatingRange(Double minRating, Double maxRating);
    
    /**
     * Find suppliers by payment terms
     */
    @Query("{'businessInfo.paymentTerms': ?0, 'isActive': true}")
    List<Supplier> findByPaymentTerms(Integer paymentTerms);
    
    /**
     * Find suppliers with credit limit
     */
    @Query("{'businessInfo.creditLimit': {'$ne': null}, 'isActive': true}")
    List<Supplier> findSuppliersWithCreditLimit();
    
    /**
     * Find suppliers without credit limit
     */
    @Query("{'businessInfo.creditLimit': null, 'isActive': true}")
    List<Supplier> findSuppliersWithoutCreditLimit();
    
    /**
     * Find suppliers created by user
     */
    List<Supplier> findByCreatedBy(String createdBy);
    
    /**
     * Check if supplier name exists
     */
    boolean existsByName(String name);
    
    /**
     * Check if supplier name exists excluding current supplier
     */
    @Query("{'name': ?0, '_id': {'$ne': ?1}}")
    boolean existsByNameAndIdNot(String name, String id);
    
    /**
     * Check if tax number exists
     */
    @Query("{'businessInfo.taxNumber': ?0}")
    boolean existsByTaxNumber(String taxNumber);
    
    /**
     * Check if tax number exists excluding current supplier
     */
    @Query("{'businessInfo.taxNumber': ?0, '_id': {'$ne': ?1}}")
    boolean existsByTaxNumberAndIdNot(String taxNumber, String id);
    
    /**
     * Count active suppliers
     */
    long countByIsActiveTrue();
    
    /**
     * Count suppliers by country
     */
    @Query(value = "{'address.country': ?0, 'isActive': true}", count = true)
    long countByCountry(String country);
    
    /**
     * Find suppliers with website
     */
    @Query("{'contact.website': {'$ne': null, '$ne': ''}, 'isActive': true}")
    List<Supplier> findSuppliersWithWebsite();
    
    /**
     * Find suppliers without contact information
     */
    @Query("{'$and': [" +
           "{'isActive': true}, " +
           "{'$or': [" +
           "{'contact.email': {'$in': [null, '']}}, " +
           "{'contact.phone': {'$in': [null, '']}}, " +
           "{'contact.mobile': {'$in': [null, '']}}" +
           "]}" +
           "]}")
    List<Supplier> findSuppliersWithIncompleteContact();
    
    /**
     * Find suppliers by currency
     */
    @Query("{'businessInfo.currency': ?0, 'isActive': true}")
    List<Supplier> findByCurrency(String currency);
    
    /**
     * Find top rated suppliers
     */
    @Query("{'businessInfo.rating': {'$ne': null}, 'isActive': true}")
    List<Supplier> findTopRatedSuppliers(Pageable pageable);
}
