package com.kaydkapro.controller;

import com.kaydkapro.dto.request.PurchaseRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.Purchase;
import com.kaydkapro.service.PurchaseService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Purchase Controller - REST API endpoints for purchase management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/purchases")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class PurchaseController {
    
    private final PurchaseService purchaseService;
    
    /**
     * Create a new purchase order
     */
    @PostMapping
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Purchase>> createPurchase(@Valid @RequestBody PurchaseRequest purchaseRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Purchase purchase = purchaseRequest.toEntity();
            Purchase createdPurchase = purchaseService.createPurchase(purchase, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Purchase order created successfully", createdPurchase));
            
        } catch (Exception e) {
            log.error("Failed to create purchase: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update an existing purchase order
     */
    @PutMapping("/{purchaseId}")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Purchase>> updatePurchase(@PathVariable String purchaseId,
                                                               @Valid @RequestBody PurchaseRequest purchaseRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Purchase purchaseUpdates = purchaseRequest.toEntity();
            Purchase updatedPurchase = purchaseService.updatePurchase(purchaseId, purchaseUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Purchase order updated successfully", updatedPurchase));
            
        } catch (Exception e) {
            log.error("Failed to update purchase {}: {}", purchaseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Approve purchase order
     */
    @PostMapping("/{purchaseId}/approve")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Purchase>> approvePurchase(@PathVariable String purchaseId,
                                                                Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Purchase approvedPurchase = purchaseService.approvePurchase(purchaseId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Purchase order approved successfully", approvedPurchase));
            
        } catch (Exception e) {
            log.error("Failed to approve purchase {}: {}", purchaseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Receive purchase order
     */
    @PostMapping("/{purchaseId}/receive")
    @PreAuthorize("hasRole('STOREKEEPER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Purchase>> receivePurchase(@PathVariable String purchaseId,
                                                                @RequestBody List<PurchaseService.ReceiveItem> receiveItems,
                                                                Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Purchase receivedPurchase = purchaseService.receivePurchase(purchaseId, receiveItems, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Purchase order received successfully", receivedPurchase));
            
        } catch (Exception e) {
            log.error("Failed to receive purchase {}: {}", purchaseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Cancel purchase order
     */
    @PostMapping("/{purchaseId}/cancel")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Purchase>> cancelPurchase(@PathVariable String purchaseId,
                                                               @RequestParam String reason,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Purchase cancelledPurchase = purchaseService.cancelPurchase(purchaseId, reason, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Purchase order cancelled successfully", cancelledPurchase));
            
        } catch (Exception e) {
            log.error("Failed to cancel purchase {}: {}", purchaseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get purchase by ID
     */
    @GetMapping("/{purchaseId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Purchase>> getPurchase(@PathVariable String purchaseId) {
        try {
            Purchase purchase = purchaseService.getPurchaseById(purchaseId);
            return ResponseEntity.ok(ApiResponse.success("Purchase retrieved successfully", purchase));
            
        } catch (Exception e) {
            log.error("Failed to get purchase {}: {}", purchaseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Purchase not found"));
        }
    }
    
    /**
     * Get purchase by order number
     */
    @GetMapping("/order/{orderNumber}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Purchase>> getPurchaseByOrderNumber(@PathVariable String orderNumber) {
        try {
            Purchase purchase = purchaseService.getPurchaseByOrderNumber(orderNumber);
            return ResponseEntity.ok(ApiResponse.success("Purchase retrieved successfully", purchase));
            
        } catch (Exception e) {
            log.error("Failed to get purchase by order number {}: {}", orderNumber, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Purchase not found"));
        }
    }
    
    /**
     * Get all purchases with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Purchase>>> getPurchases(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Purchase> purchasePage = purchaseService.getPurchases(pageable);
            PageResponse<Purchase> pageResponse = PageResponse.of(purchasePage);
            
            return ResponseEntity.ok(ApiResponse.success("Purchases retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get purchases: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get purchases by status
     */
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Purchase>>> getPurchasesByStatus(
            @PathVariable String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<Purchase> purchasePage = purchaseService.getPurchasesByStatus(status, pageable);
            PageResponse<Purchase> pageResponse = PageResponse.of(purchasePage);
            
            return ResponseEntity.ok(ApiResponse.success("Purchases retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get purchases by status {}: {}", status, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get purchases by supplier
     */
    @GetMapping("/supplier/{supplierId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Purchase>>> getPurchasesBySupplier(
            @PathVariable String supplierId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<Purchase> purchasePage = purchaseService.getPurchasesBySupplier(supplierId, pageable);
            PageResponse<Purchase> pageResponse = PageResponse.of(purchasePage);
            
            return ResponseEntity.ok(ApiResponse.success("Supplier purchases retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get purchases by supplier {}: {}", supplierId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get purchases by date range
     */
    @GetMapping("/date-range")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Purchase>>> getPurchasesByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<Purchase> purchasePage = purchaseService.getPurchasesByDateRange(startDate, endDate, pageable);
            PageResponse<Purchase> pageResponse = PageResponse.of(purchasePage);
            
            return ResponseEntity.ok(ApiResponse.success("Purchases retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get purchases by date range: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search purchases
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Purchase>>> searchPurchases(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<Purchase> purchasePage = purchaseService.searchPurchases(query, pageable);
            PageResponse<Purchase> pageResponse = PageResponse.of(purchasePage);
            
            return ResponseEntity.ok(ApiResponse.success("Purchase search completed", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to search purchases: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get pending purchases
     */
    @GetMapping("/pending")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Purchase>>> getPendingPurchases() {
        try {
            List<Purchase> purchases = purchaseService.getPendingPurchases();
            return ResponseEntity.ok(ApiResponse.success("Pending purchases retrieved successfully", purchases));
            
        } catch (Exception e) {
            log.error("Failed to get pending purchases: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get overdue purchases
     */
    @GetMapping("/overdue")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<Purchase>>> getOverduePurchases() {
        try {
            List<Purchase> purchases = purchaseService.getOverduePurchases();
            return ResponseEntity.ok(ApiResponse.success("Overdue purchases retrieved successfully", purchases));
            
        } catch (Exception e) {
            log.error("Failed to get overdue purchases: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Calculate total purchase amount for period
     */
    @GetMapping("/total-amount")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BigDecimal>> calculateTotalPurchaseAmount(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            BigDecimal totalAmount = purchaseService.calculateTotalPurchaseAmount(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Total purchase amount calculated", totalAmount));
            
        } catch (Exception e) {
            log.error("Failed to calculate total purchase amount: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get purchase statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PurchaseService.PurchaseStats>> getPurchaseStatistics() {
        try {
            PurchaseService.PurchaseStats stats = purchaseService.getPurchaseStats();
            return ResponseEntity.ok(ApiResponse.success("Purchase statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get purchase statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete purchase (soft delete)
     */
    @DeleteMapping("/{purchaseId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deletePurchase(@PathVariable String purchaseId,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            purchaseService.deletePurchase(purchaseId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Purchase deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete purchase {}: {}", purchaseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
