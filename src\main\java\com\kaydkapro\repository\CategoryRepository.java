package com.kaydkapro.repository;

import com.kaydkapro.entity.Category;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Category Repository - Data access layer for Category entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface CategoryRepository extends MongoRepository<Category, String> {
    
    /**
     * Find category by name
     */
    Optional<Category> findByName(String name);
    
    /**
     * Find all active categories
     */
    List<Category> findByIsActiveTrue();
    
    /**
     * Find all active categories sorted by sort order
     */
    List<Category> findByIsActiveTrue(Sort sort);
    
    /**
     * Find root categories (no parent)
     */
    @Query("{'$or': [{'parentId': null}, {'parentId': ''}], 'isActive': true}")
    List<Category> findRootCategories();
    
    /**
     * Find root categories sorted by sort order
     */
    @Query("{'$or': [{'parentId': null}, {'parentId': ''}], 'isActive': true}")
    List<Category> findRootCategories(Sort sort);
    
    /**
     * Find subcategories by parent ID
     */
    @Query("{'parentId': ?0, 'isActive': true}")
    List<Category> findByParentId(String parentId);
    
    /**
     * Find subcategories by parent ID sorted by sort order
     */
    @Query("{'parentId': ?0, 'isActive': true}")
    List<Category> findByParentId(String parentId, Sort sort);
    
    /**
     * Search categories by name or description
     */
    @Query("{'$and': [{'isActive': true}, {'$or': [" +
           "{'name': {'$regex': ?0, '$options': 'i'}}, " +
           "{'description': {'$regex': ?0, '$options': 'i'}}" +
           "]}]}")
    List<Category> searchCategories(String searchTerm);
    
    /**
     * Check if category name exists
     */
    boolean existsByName(String name);
    
    /**
     * Check if category name exists excluding current category
     */
    @Query("{'name': ?0, '_id': {'$ne': ?1}}")
    boolean existsByNameAndIdNot(String name, String id);
    
    /**
     * Check if category has subcategories
     */
    @Query(value = "{'parentId': ?0}", count = true)
    long countByParentId(String parentId);
    
    /**
     * Find categories created by user
     */
    List<Category> findByCreatedBy(String createdBy);
    
    /**
     * Count active categories
     */
    long countByIsActiveTrue();
    
    /**
     * Count root categories
     */
    @Query(value = "{'$or': [{'parentId': null}, {'parentId': ''}], 'isActive': true}", count = true)
    long countRootCategories();
    
    /**
     * Find categories with images
     */
    @Query("{'imageUrl': {'$ne': null, '$ne': ''}, 'isActive': true}")
    List<Category> findCategoriesWithImages();
    
    /**
     * Find categories without images
     */
    @Query("{'$or': [{'imageUrl': null}, {'imageUrl': ''}], 'isActive': true}")
    List<Category> findCategoriesWithoutImages();
    
    /**
     * Find all categories in hierarchy (parent and all its children)
     */
    @Query("{'$or': [{'_id': ?0}, {'parentId': ?0}], 'isActive': true}")
    List<Category> findCategoryHierarchy(String categoryId);
    
    /**
     * Find categories by sort order range
     */
    @Query("{'sortOrder': {'$gte': ?0, '$lte': ?1}, 'isActive': true}")
    List<Category> findBySortOrderBetween(Integer minOrder, Integer maxOrder);
    
    /**
     * Find next sort order for root categories
     */
    @Query(value = "{'$or': [{'parentId': null}, {'parentId': ''}]}", 
           fields = "{'sortOrder': 1}", 
           sort = "{'sortOrder': -1}")
    Optional<Category> findTopBySortOrderDesc();
    
    /**
     * Find next sort order for subcategories
     */
    @Query(value = "{'parentId': ?0}", 
           fields = "{'sortOrder': 1}", 
           sort = "{'sortOrder': -1}")
    Optional<Category> findTopByParentIdOrderBySortOrderDesc(String parentId);
}
