package com.kaydkapro.service;

import com.kaydkapro.entity.Notification;
import com.kaydkapro.repository.NotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Notification Service - Business logic for notification management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class NotificationService {
    
    private final NotificationRepository notificationRepository;
    
    /**
     * Send low stock alert notification
     */
    public Notification sendLowStockAlert(String userId, String productName, int currentStock, int minStock) {
        log.info("Sending low stock alert for product: {} to user: {}", productName, userId);
        
        Notification notification = Notification.createLowStockAlert(
            userId,
            getUserName(userId),
            productName,
            currentStock,
            minStock
        );
        
        Notification savedNotification = notificationRepository.save(notification);
        
        // Here you would typically trigger real-time notification (WebSocket, email, etc.)
        triggerRealTimeNotification(savedNotification);
        
        log.info("Low stock alert sent: {}", savedNotification.getId());
        return savedNotification;
    }
    
    /**
     * Send sale completed notification
     */
    public Notification sendSaleCompletedNotification(String userId, String saleNumber, String amount) {
        log.info("Sending sale completed notification for sale: {} to user: {}", saleNumber, userId);
        
        Notification notification = Notification.createSaleCompletedNotification(
            userId,
            getUserName(userId),
            saleNumber,
            amount
        );
        
        Notification savedNotification = notificationRepository.save(notification);
        triggerRealTimeNotification(savedNotification);
        
        log.info("Sale completed notification sent: {}", savedNotification.getId());
        return savedNotification;
    }
    
    /**
     * Send system notification
     */
    public Notification sendSystemNotification(String userId, String title, String message) {
        log.info("Sending system notification to user: {}", userId);
        
        Notification notification = Notification.createSystemNotification(
            userId,
            getUserName(userId),
            title,
            message
        );
        
        Notification savedNotification = notificationRepository.save(notification);
        triggerRealTimeNotification(savedNotification);
        
        log.info("System notification sent: {}", savedNotification.getId());
        return savedNotification;
    }
    
    /**
     * Send custom notification
     */
    public Notification sendNotification(String userId, String title, String message, 
                                       String type, String category) {
        log.info("Sending custom notification to user: {}", userId);
        
        Notification notification = Notification.builder()
                .user(Notification.NotificationUser.builder()
                        .userId(userId)
                        .userName(getUserName(userId))
                        .build())
                .title(title)
                .message(message)
                .type(type)
                .category(category)
                .isRead(false)
                .build();
        
        Notification savedNotification = notificationRepository.save(notification);
        triggerRealTimeNotification(savedNotification);
        
        log.info("Custom notification sent: {}", savedNotification.getId());
        return savedNotification;
    }
    
    /**
     * Broadcast notification to all users
     */
    public List<Notification> broadcastNotification(List<String> userIds, String title, 
                                                   String message, String type, String category) {
        log.info("Broadcasting notification to {} users", userIds.size());
        
        List<Notification> notifications = userIds.stream()
                .map(userId -> sendNotification(userId, title, message, type, category))
                .toList();
        
        log.info("Broadcast notification completed: {} notifications sent", notifications.size());
        return notifications;
    }
    
    /**
     * Get notifications for user
     */
    @Transactional(readOnly = true)
    public Page<Notification> getUserNotifications(String userId, Pageable pageable) {
        return notificationRepository.findByUserId(userId, pageable);
    }
    
    /**
     * Get unread notifications for user
     */
    @Transactional(readOnly = true)
    public Page<Notification> getUnreadNotifications(String userId, Pageable pageable) {
        return notificationRepository.findUnreadByUserId(userId, pageable);
    }
    
    /**
     * Get unread notification count for user
     */
    @Transactional(readOnly = true)
    public long getUnreadNotificationCount(String userId) {
        return notificationRepository.countUnreadByUserId(userId);
    }
    
    /**
     * Mark notification as read
     */
    public Notification markAsRead(String notificationId, String userId) {
        log.info("Marking notification as read: {}", notificationId);
        
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("Notification not found with id: " + notificationId));
        
        // Verify user owns this notification
        if (!notification.getUser().getUserId().equals(userId)) {
            throw new RuntimeException("User not authorized to mark this notification as read");
        }
        
        if (!notification.getIsRead()) {
            notification.markAsRead();
            notification = notificationRepository.save(notification);
            log.info("Notification marked as read: {}", notificationId);
        }
        
        return notification;
    }
    
    /**
     * Mark all notifications as read for user
     */
    public void markAllAsRead(String userId) {
        log.info("Marking all notifications as read for user: {}", userId);
        
        List<Notification> unreadNotifications = notificationRepository.findUnreadForMarkingAsRead(userId);
        
        unreadNotifications.forEach(notification -> {
            notification.markAsRead();
        });
        
        notificationRepository.saveAll(unreadNotifications);
        
        log.info("Marked {} notifications as read for user: {}", unreadNotifications.size(), userId);
    }
    
    /**
     * Get notifications by type for user
     */
    @Transactional(readOnly = true)
    public List<Notification> getNotificationsByType(String userId, String type) {
        return notificationRepository.findByUserIdAndType(userId, type);
    }
    
    /**
     * Get notifications by category for user
     */
    @Transactional(readOnly = true)
    public List<Notification> getNotificationsByCategory(String userId, String category) {
        return notificationRepository.findByUserIdAndCategory(userId, category);
    }
    
    /**
     * Get recent notifications for user
     */
    @Transactional(readOnly = true)
    public List<Notification> getRecentNotifications(String userId, int hours) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        return notificationRepository.findByUserIdAndCreatedAtAfter(userId, since);
    }
    
    /**
     * Delete notification
     */
    public void deleteNotification(String notificationId, String userId) {
        log.info("Deleting notification: {}", notificationId);
        
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("Notification not found with id: " + notificationId));
        
        // Verify user owns this notification
        if (!notification.getUser().getUserId().equals(userId)) {
            throw new RuntimeException("User not authorized to delete this notification");
        }
        
        notificationRepository.delete(notification);
        log.info("Notification deleted: {}", notificationId);
    }
    
    /**
     * Delete all read notifications for user
     */
    public void deleteReadNotifications(String userId) {
        log.info("Deleting all read notifications for user: {}", userId);
        
        List<Notification> readNotifications = notificationRepository.findReadByUserId(userId);
        notificationRepository.deleteAll(readNotifications);
        
        log.info("Deleted {} read notifications for user: {}", readNotifications.size(), userId);
    }
    
    /**
     * Clean up old notifications
     */
    public void cleanupOldNotifications(int daysToKeep) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
        notificationRepository.deleteByCreatedAtBefore(cutoffDate);
        log.info("Cleaned up notifications older than {} days", daysToKeep);
    }
    
    /**
     * Clean up old read notifications
     */
    public void cleanupOldReadNotifications(int daysToKeep) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
        notificationRepository.deleteReadNotificationsBefore(cutoffDate);
        log.info("Cleaned up read notifications older than {} days", daysToKeep);
    }
    
    /**
     * Get notification statistics for user
     */
    @Transactional(readOnly = true)
    public NotificationStats getNotificationStats(String userId) {
        long totalNotifications = notificationRepository.countByUserId(userId);
        long unreadNotifications = notificationRepository.countUnreadByUserId(userId);
        long warningNotifications = notificationRepository.countByUserIdAndType(userId, Notification.TYPE_WARNING);
        long errorNotifications = notificationRepository.countByUserIdAndType(userId, Notification.TYPE_ERROR);
        long stockNotifications = notificationRepository.countByUserIdAndCategory(userId, Notification.CATEGORY_STOCK);
        
        return NotificationStats.builder()
                .totalNotifications(totalNotifications)
                .unreadNotifications(unreadNotifications)
                .readNotifications(totalNotifications - unreadNotifications)
                .warningNotifications(warningNotifications)
                .errorNotifications(errorNotifications)
                .stockNotifications(stockNotifications)
                .build();
    }
    
    // Helper methods
    private void triggerRealTimeNotification(Notification notification) {
        // Here you would implement real-time notification delivery
        // Examples: WebSocket push, email sending, SMS, push notifications, etc.
        log.debug("Triggering real-time notification: {}", notification.getId());
        
        // WebSocket notification (would be implemented with WebSocket service)
        // webSocketService.sendNotificationToUser(notification.getUser().getUserId(), notification);
        
        // Email notification for important alerts
        if (Notification.TYPE_ERROR.equals(notification.getType()) || 
            Notification.TYPE_WARNING.equals(notification.getType())) {
            // emailService.sendNotificationEmail(notification);
        }
        
        // Push notification for mobile apps
        // pushNotificationService.sendPushNotification(notification);
    }
    
    private String getUserName(String userId) {
        // This would typically fetch from UserService or cache
        return "User-" + userId;
    }
    
    /**
     * Notification statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class NotificationStats {
        private long totalNotifications;
        private long unreadNotifications;
        private long readNotifications;
        private long warningNotifications;
        private long errorNotifications;
        private long stockNotifications;
    }
}
