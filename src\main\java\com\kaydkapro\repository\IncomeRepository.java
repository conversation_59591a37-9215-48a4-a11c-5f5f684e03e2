package com.kaydkapro.repository;

import com.kaydkapro.entity.Income;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Income Repository - Data access layer for Income entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface IncomeRepository extends MongoRepository<Income, String> {
    
    /**
     * Find income by category
     */
    List<Income> findByCategory(String category);
    
    /**
     * Find income by category with pagination
     */
    Page<Income> findByCategory(String category, Pageable pageable);
    
    /**
     * Find income by date range
     */
    @Query("{'incomeDate': {'$gte': ?0, '$lte': ?1}}")
    List<Income> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find income by date range with pagination
     */
    @Query("{'incomeDate': {'$gte': ?0, '$lte': ?1}}")
    Page<Income> findByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * Find income by amount range
     */
    @Query("{'amount': {'$gte': ?0, '$lte': ?1}}")
    List<Income> findByAmountRange(BigDecimal minAmount, BigDecimal maxAmount);
    
    /**
     * Find income by reference type
     */
    @Query("{'reference.type': ?0}")
    List<Income> findByReferenceType(String referenceType);
    
    /**
     * Find income by reference ID
     */
    @Query("{'reference.referenceId': ?0}")
    List<Income> findByReferenceId(String referenceId);
    
    /**
     * Find income created by user
     */
    @Query("{'createdBy.userId': ?0}")
    List<Income> findByCreatedByUserId(String userId);
    
    /**
     * Search income by title or description
     */
    @Query("{'$or': [" +
           "{'title': {'$regex': ?0, '$options': 'i'}}, " +
           "{'description': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    Page<Income> searchIncome(String searchTerm, Pageable pageable);
    
    /**
     * Find sales revenue income
     */
    @Query("{'category': 'SALES_REVENUE'}")
    List<Income> findSalesRevenue();
    
    /**
     * Find service revenue income
     */
    @Query("{'category': 'SERVICE_REVENUE'}")
    List<Income> findServiceRevenue();
    
    /**
     * Find income with receipts
     */
    @Query("{'receiptImage': {'$ne': null, '$ne': ''}}")
    List<Income> findIncomeWithReceipts();
    
    /**
     * Find income without receipts
     */
    @Query("{'$or': [{'receiptImage': null}, {'receiptImage': ''}]}")
    List<Income> findIncomeWithoutReceipts();
    
    /**
     * Calculate total income for period
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'incomeDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': null, 'totalIncome': { '$sum': '$amount' } } }"
    })
    Optional<BigDecimal> calculateTotalIncome(LocalDate startDate, LocalDate endDate);
    
    /**
     * Calculate total income by category for period
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'incomeDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': '$category', 'totalAmount': { '$sum': '$amount' }, 'count': { '$sum': 1 } } }",
        "{ '$sort': { 'totalAmount': -1 } }"
    })
    List<IncomeCategoryStats> getIncomeByCategoryStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get daily income summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'incomeDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': '$incomeDate', " +
        "  'totalIncome': { '$sum': '$amount' }, " +
        "  'count': { '$sum': 1 }, " +
        "  'averageIncome': { '$avg': '$amount' } " +
        "} }",
        "{ '$sort': { '_id': 1 } }"
    })
    List<DailyIncomeStats> getDailyIncomeStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get monthly income summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'incomeDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': { 'year': { '$year': '$incomeDate' }, 'month': { '$month': '$incomeDate' } }, " +
        "  'totalIncome': { '$sum': '$amount' }, " +
        "  'count': { '$sum': 1 }, " +
        "  'averageIncome': { '$avg': '$amount' } " +
        "} }",
        "{ '$sort': { '_id.year': 1, '_id.month': 1 } }"
    })
    List<MonthlyIncomeStats> getMonthlyIncomeStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find large income entries (above threshold)
     */
    @Query("{'amount': {'$gte': ?0}}")
    List<Income> findLargeIncomeEntries(BigDecimal threshold);
    
    /**
     * Find recent income (last N days)
     */
    @Query("{'incomeDate': {'$gte': ?0}}")
    List<Income> findRecentIncome(LocalDate since);
    
    /**
     * Count income entries by category
     */
    long countByCategory(String category);
    
    /**
     * Count income entries by date range
     */
    @Query(value = "{'incomeDate': {'$gte': ?0, '$lte': ?1}}", count = true)
    long countByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find income by reference number
     */
    @Query("{'reference.referenceNumber': ?0}")
    List<Income> findByReferenceNumber(String referenceNumber);
    
    /**
     * Find income today
     */
    @Query("{'incomeDate': ?0}")
    List<Income> findIncomeToday(LocalDate today);
    
    /**
     * Find income this month
     */
    @Query("{'incomeDate': {'$gte': ?0, '$lte': ?1}}")
    List<Income> findIncomeThisMonth(LocalDate monthStart, LocalDate monthEnd);
    
    /**
     * Interface for income category statistics projection
     */
    interface IncomeCategoryStats {
        String getId();
        BigDecimal getTotalAmount();
        Long getCount();
    }
    
    /**
     * Interface for daily income statistics projection
     */
    interface DailyIncomeStats {
        LocalDate getId();
        BigDecimal getTotalIncome();
        Long getCount();
        BigDecimal getAverageIncome();
    }
    
    /**
     * Interface for monthly income statistics projection
     */
    interface MonthlyIncomeStats {
        MonthYear getId();
        BigDecimal getTotalIncome();
        Long getCount();
        BigDecimal getAverageIncome();
        
        interface MonthYear {
            Integer getYear();
            Integer getMonth();
        }
    }
}
