package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Customer Entity - Represents customers
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "customers")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Customer {
    
    @Id
    private String id;
    
    @NotBlank(message = "Customer name is required")
    @Size(max = 200, message = "Customer name must not exceed 200 characters")
    private String name;
    
    private CustomerContact contact;
    
    private CustomerAddress address;
    
    @NotBlank(message = "Customer type is required")
    private String customerType; // INDIVIDUAL, BUSINESS
    
    private CustomerBusinessInfo businessInfo;
    
    private CustomerLoyaltyProgram loyaltyProgram;
    
    @Builder.Default
    private Boolean isActive = true;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    private String createdBy;
    private String updatedBy;
    
    /**
     * Nested class for customer contact information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerContact {
        @Email(message = "Email should be valid")
        private String email;
        
        @Size(max = 20, message = "Phone number must not exceed 20 characters")
        private String phone;
        
        @Size(max = 20, message = "Mobile number must not exceed 20 characters")
        private String mobile;
        
        @Size(max = 20, message = "Fax number must not exceed 20 characters")
        private String fax;
    }
    
    /**
     * Nested class for customer address
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerAddress {
        @Size(max = 200, message = "Street must not exceed 200 characters")
        private String street;
        
        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;
        
        @Size(max = 100, message = "State must not exceed 100 characters")
        private String state;
        
        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;
        
        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;
        
        public String getFullAddress() {
            StringBuilder sb = new StringBuilder();
            if (street != null) sb.append(street).append(", ");
            if (city != null) sb.append(city).append(", ");
            if (state != null) sb.append(state).append(" ");
            if (postalCode != null) sb.append(postalCode).append(", ");
            if (country != null) sb.append(country);
            return sb.toString().replaceAll(", $", "");
        }
    }
    
    /**
     * Nested class for business customer information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerBusinessInfo {
        @Size(max = 50, message = "Tax number must not exceed 50 characters")
        private String taxNumber;
        
        @DecimalMin(value = "0.0", message = "Credit limit must be non-negative")
        private BigDecimal creditLimit;
        
        @Size(max = 100, message = "Company registration must not exceed 100 characters")
        private String companyRegistration;
        
        @Size(max = 100, message = "Industry must not exceed 100 characters")
        private String industry;
        
        @Size(max = 500, message = "Notes must not exceed 500 characters")
        private String notes;
    }
    
    /**
     * Nested class for loyalty program information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomerLoyaltyProgram {
        @DecimalMin(value = "0.0", message = "Points must be non-negative")
        @Builder.Default
        private BigDecimal points = BigDecimal.ZERO;
        
        @NotBlank(message = "Tier is required")
        @Builder.Default
        private String tier = TIER_BRONZE; // BRONZE, SILVER, GOLD, PLATINUM
        
        private LocalDateTime lastPointsEarned;
        private LocalDateTime lastPointsRedeemed;
        
        @DecimalMin(value = "0.0", message = "Total earned points must be non-negative")
        @Builder.Default
        private BigDecimal totalEarnedPoints = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Total redeemed points must be non-negative")
        @Builder.Default
        private BigDecimal totalRedeemedPoints = BigDecimal.ZERO;
        
        public void earnPoints(BigDecimal points) {
            if (points != null && points.compareTo(BigDecimal.ZERO) > 0) {
                this.points = this.points.add(points);
                this.totalEarnedPoints = this.totalEarnedPoints.add(points);
                this.lastPointsEarned = LocalDateTime.now();
                updateTier();
            }
        }
        
        public boolean redeemPoints(BigDecimal points) {
            if (points != null && points.compareTo(BigDecimal.ZERO) > 0 && 
                this.points.compareTo(points) >= 0) {
                this.points = this.points.subtract(points);
                this.totalRedeemedPoints = this.totalRedeemedPoints.add(points);
                this.lastPointsRedeemed = LocalDateTime.now();
                return true;
            }
            return false;
        }
        
        private void updateTier() {
            if (totalEarnedPoints.compareTo(BigDecimal.valueOf(10000)) >= 0) {
                this.tier = TIER_PLATINUM;
            } else if (totalEarnedPoints.compareTo(BigDecimal.valueOf(5000)) >= 0) {
                this.tier = TIER_GOLD;
            } else if (totalEarnedPoints.compareTo(BigDecimal.valueOf(1000)) >= 0) {
                this.tier = TIER_SILVER;
            } else {
                this.tier = TIER_BRONZE;
            }
        }
    }
    
    // Customer type constants
    public static final String TYPE_INDIVIDUAL = "INDIVIDUAL";
    public static final String TYPE_BUSINESS = "BUSINESS";
    
    // Loyalty tier constants
    public static final String TIER_BRONZE = "BRONZE";
    public static final String TIER_SILVER = "SILVER";
    public static final String TIER_GOLD = "GOLD";
    public static final String TIER_PLATINUM = "PLATINUM";
    
    // Helper methods
    public String getPrimaryContactMethod() {
        if (contact != null) {
            if (contact.getEmail() != null && !contact.getEmail().isEmpty()) {
                return contact.getEmail();
            }
            if (contact.getPhone() != null && !contact.getPhone().isEmpty()) {
                return contact.getPhone();
            }
            if (contact.getMobile() != null && !contact.getMobile().isEmpty()) {
                return contact.getMobile();
            }
        }
        return null;
    }
    
    public boolean isBusinessCustomer() {
        return TYPE_BUSINESS.equals(customerType);
    }
    
    public boolean isIndividualCustomer() {
        return TYPE_INDIVIDUAL.equals(customerType);
    }
    
    public boolean isVipCustomer() {
        return loyaltyProgram != null && 
               (TIER_GOLD.equals(loyaltyProgram.getTier()) || 
                TIER_PLATINUM.equals(loyaltyProgram.getTier()));
    }
    
    public boolean isWithinCreditLimit(BigDecimal amount) {
        if (businessInfo == null || businessInfo.getCreditLimit() == null) {
            return true; // No limit set
        }
        return amount.compareTo(businessInfo.getCreditLimit()) <= 0;
    }
    
    public BigDecimal getAvailableLoyaltyPoints() {
        return loyaltyProgram != null ? loyaltyProgram.getPoints() : BigDecimal.ZERO;
    }
}
