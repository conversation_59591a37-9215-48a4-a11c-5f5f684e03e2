package com.kaydkapro.repository;

import com.kaydkapro.entity.StockMovement;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * StockMovement Repository - Data access layer for StockMovement entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface StockMovementRepository extends MongoRepository<StockMovement, String> {
    
    /**
     * Find stock movements by product ID
     */
    @Query("{'product.productId': ?0}")
    List<StockMovement> findByProductId(String productId);
    
    /**
     * Find stock movements by product ID with pagination
     */
    @Query("{'product.productId': ?0}")
    Page<StockMovement> findByProductId(String productId, Pageable pageable);
    
    /**
     * Find stock movements by warehouse ID
     */
    @Query("{'warehouse.warehouseId': ?0}")
    List<StockMovement> findByWarehouseId(String warehouseId);
    
    /**
     * Find stock movements by warehouse ID with pagination
     */
    @Query("{'warehouse.warehouseId': ?0}")
    Page<StockMovement> findByWarehouseId(String warehouseId, Pageable pageable);
    
    /**
     * Find stock movements by movement type
     */
    List<StockMovement> findByMovementType(String movementType);
    
    /**
     * Find stock movements by movement type with pagination
     */
    Page<StockMovement> findByMovementType(String movementType, Pageable pageable);
    
    /**
     * Find stock movements by product ID and warehouse ID
     */
    @Query("{'product.productId': ?0, 'warehouse.warehouseId': ?1}")
    List<StockMovement> findByProductIdAndWarehouseId(String productId, String warehouseId);
    
    /**
     * Find stock movements by product ID and movement type
     */
    @Query("{'product.productId': ?0, 'movementType': ?1}")
    List<StockMovement> findByProductIdAndMovementType(String productId, String movementType);
    
    /**
     * Find stock movements by reference type
     */
    @Query("{'reference.type': ?0}")
    List<StockMovement> findByReferenceType(String referenceType);
    
    /**
     * Find stock movements by reference ID
     */
    @Query("{'reference.referenceId': ?0}")
    List<StockMovement> findByReferenceId(String referenceId);
    
    /**
     * Find stock movements by reference number
     */
    @Query("{'reference.referenceNumber': ?0}")
    List<StockMovement> findByReferenceNumber(String referenceNumber);
    
    /**
     * Find stock movements created by user
     */
    @Query("{'createdBy.userId': ?0}")
    List<StockMovement> findByCreatedByUserId(String userId);
    
    /**
     * Find stock movements by date range
     */
    @Query("{'createdAt': {'$gte': ?0, '$lte': ?1}}")
    List<StockMovement> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find stock movements by date range with pagination
     */
    @Query("{'createdAt': {'$gte': ?0, '$lte': ?1}}")
    Page<StockMovement> findByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    /**
     * Find inbound movements (IN)
     */
    @Query("{'movementType': 'IN'}")
    List<StockMovement> findInboundMovements();
    
    /**
     * Find outbound movements (OUT)
     */
    @Query("{'movementType': 'OUT'}")
    List<StockMovement> findOutboundMovements();
    
    /**
     * Find transfer movements
     */
    @Query("{'movementType': 'TRANSFER'}")
    List<StockMovement> findTransferMovements();
    
    /**
     * Find adjustment movements
     */
    @Query("{'movementType': 'ADJUSTMENT'}")
    List<StockMovement> findAdjustmentMovements();
    
    /**
     * Find stock movements by batch number
     */
    @Query("{'batchInfo.batchNumber': ?0}")
    List<StockMovement> findByBatchNumber(String batchNumber);
    
    /**
     * Find stock movements with batch info
     */
    @Query("{'batchInfo': {'$ne': null}}")
    List<StockMovement> findMovementsWithBatchInfo();
    
    /**
     * Find stock movements by product SKU
     */
    @Query("{'product.sku': ?0}")
    List<StockMovement> findByProductSku(String sku);
    
    /**
     * Find recent stock movements (last N hours)
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<StockMovement> findRecentMovements(LocalDateTime since);
    
    /**
     * Find stock movements by quantity range
     */
    @Query("{'quantity': {'$gte': ?0, '$lte': ?1}}")
    List<StockMovement> findByQuantityRange(BigDecimal minQuantity, BigDecimal maxQuantity);
    
    /**
     * Find large stock movements (above threshold)
     */
    @Query("{'quantity': {'$gte': ?0}}")
    List<StockMovement> findLargeMovements(BigDecimal threshold);
    
    /**
     * Get stock movement summary by product
     */
    @Aggregation(pipeline = {
        "{ '$group': { " +
        "  '_id': '$product.productId', " +
        "  'productName': { '$first': '$product.productName' }, " +
        "  'sku': { '$first': '$product.sku' }, " +
        "  'totalInbound': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'IN'] }, '$quantity', 0] } }, " +
        "  'totalOutbound': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'OUT'] }, '$quantity', 0] } }, " +
        "  'totalTransfers': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'TRANSFER'] }, '$quantity', 0] } }, " +
        "  'totalAdjustments': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'ADJUSTMENT'] }, '$quantity', 0] } }, " +
        "  'movementCount': { '$sum': 1 }, " +
        "  'lastMovement': { '$max': '$createdAt' } " +
        "} }",
        "{ '$sort': { 'movementCount': -1 } }"
    })
    List<ProductMovementSummary> getMovementSummaryByProduct();
    
    /**
     * Get stock movement summary by warehouse
     */
    @Aggregation(pipeline = {
        "{ '$group': { " +
        "  '_id': '$warehouse.warehouseId', " +
        "  'warehouseName': { '$first': '$warehouse.warehouseName' }, " +
        "  'warehouseCode': { '$first': '$warehouse.warehouseCode' }, " +
        "  'totalInbound': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'IN'] }, '$quantity', 0] } }, " +
        "  'totalOutbound': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'OUT'] }, '$quantity', 0] } }, " +
        "  'totalTransfers': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'TRANSFER'] }, '$quantity', 0] } }, " +
        "  'totalAdjustments': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'ADJUSTMENT'] }, '$quantity', 0] } }, " +
        "  'movementCount': { '$sum': 1 }, " +
        "  'lastMovement': { '$max': '$createdAt' } " +
        "} }",
        "{ '$sort': { 'movementCount': -1 } }"
    })
    List<WarehouseMovementSummary> getMovementSummaryByWarehouse();
    
    /**
     * Get daily movement statistics
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'createdAt': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': { '$dateToString': { 'format': '%Y-%m-%d', 'date': '$createdAt' } }, " +
        "  'totalMovements': { '$sum': 1 }, " +
        "  'totalInbound': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'IN'] }, '$quantity', 0] } }, " +
        "  'totalOutbound': { '$sum': { '$cond': [{ '$eq': ['$movementType', 'OUT'] }, '$quantity', 0] } }, " +
        "  'totalValue': { '$sum': { '$multiply': ['$quantity', '$unitCost'] } } " +
        "} }",
        "{ '$sort': { '_id': 1 } }"
    })
    List<DailyMovementStats> getDailyMovementStats(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Count movements by type
     */
    long countByMovementType(String movementType);
    
    /**
     * Count movements by product ID
     */
    @Query(value = "{'product.productId': ?0}", count = true)
    long countByProductId(String productId);
    
    /**
     * Count movements by warehouse ID
     */
    @Query(value = "{'warehouse.warehouseId': ?0}", count = true)
    long countByWarehouseId(String warehouseId);
    
    /**
     * Count movements by date range
     */
    @Query(value = "{'createdAt': {'$gte': ?0, '$lte': ?1}}", count = true)
    long countByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find movements today
     */
    @Query("{'createdAt': {'$gte': ?0, '$lt': ?1}}")
    List<StockMovement> findMovementsToday(LocalDateTime startOfDay, LocalDateTime endOfDay);
    
    /**
     * Interface for product movement summary projection
     */
    interface ProductMovementSummary {
        String getId();
        String getProductName();
        String getSku();
        BigDecimal getTotalInbound();
        BigDecimal getTotalOutbound();
        BigDecimal getTotalTransfers();
        BigDecimal getTotalAdjustments();
        Long getMovementCount();
        LocalDateTime getLastMovement();
    }
    
    /**
     * Interface for warehouse movement summary projection
     */
    interface WarehouseMovementSummary {
        String getId();
        String getWarehouseName();
        String getWarehouseCode();
        BigDecimal getTotalInbound();
        BigDecimal getTotalOutbound();
        BigDecimal getTotalTransfers();
        BigDecimal getTotalAdjustments();
        Long getMovementCount();
        LocalDateTime getLastMovement();
    }
    
    /**
     * Interface for daily movement statistics projection
     */
    interface DailyMovementStats {
        String getId();
        Long getTotalMovements();
        BigDecimal getTotalInbound();
        BigDecimal getTotalOutbound();
        BigDecimal getTotalValue();
    }
}
