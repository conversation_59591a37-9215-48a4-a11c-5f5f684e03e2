package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Expense;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Expense Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class ExpenseRequest {
    
    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    private String title;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;
    
    @NotBlank(message = "Category is required")
    @Size(max = 100, message = "Category must not exceed 100 characters")
    private String category;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;
    
    @NotNull(message = "Expense date is required")
    private LocalDate expenseDate;
    
    @Size(max = 500, message = "Receipt image URL must not exceed 500 characters")
    private String receiptImage;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;
    
    @Size(max = 100, message = "Payment method must not exceed 100 characters")
    private String paymentMethod;
    
    @Size(max = 200, message = "Vendor must not exceed 200 characters")
    private String vendor;
    
    private Boolean isRecurring = false;
    
    /**
     * Convert DTO to Entity
     */
    public Expense toEntity() {
        return Expense.builder()
                .title(this.title)
                .amount(this.amount)
                .category(this.category)
                .description(this.description)
                .expenseDate(this.expenseDate)
                .receiptImage(this.receiptImage)
                .notes(this.notes)
                .paymentMethod(this.paymentMethod)
                .vendor(this.vendor)
                .isRecurring(this.isRecurring)
                .build();
    }
}
