package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Supplier Entity - Represents suppliers/vendors
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "suppliers")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Supplier {
    
    @Id
    private String id;
    
    @NotBlank(message = "Supplier name is required")
    @Size(max = 200, message = "Supplier name must not exceed 200 characters")
    private String name;
    
    @Size(max = 100, message = "Contact person name must not exceed 100 characters")
    private String contactPerson;
    
    private SupplierContact contact;
    
    private SupplierAddress address;
    
    private SupplierBusinessInfo businessInfo;
    
    @Builder.Default
    private Boolean isActive = true;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    private String createdBy;
    private String updatedBy;
    
    /**
     * Nested class for supplier contact information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupplierContact {
        @Email(message = "Email should be valid")
        private String email;
        
        @Size(max = 20, message = "Phone number must not exceed 20 characters")
        private String phone;
        
        @Size(max = 20, message = "Mobile number must not exceed 20 characters")
        private String mobile;
        
        @Size(max = 20, message = "Fax number must not exceed 20 characters")
        private String fax;
        
        @Size(max = 200, message = "Website must not exceed 200 characters")
        private String website;
    }
    
    /**
     * Nested class for supplier address
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupplierAddress {
        @Size(max = 200, message = "Street must not exceed 200 characters")
        private String street;
        
        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;
        
        @Size(max = 100, message = "State must not exceed 100 characters")
        private String state;
        
        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;
        
        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;
        
        public String getFullAddress() {
            StringBuilder sb = new StringBuilder();
            if (street != null) sb.append(street).append(", ");
            if (city != null) sb.append(city).append(", ");
            if (state != null) sb.append(state).append(" ");
            if (postalCode != null) sb.append(postalCode).append(", ");
            if (country != null) sb.append(country);
            return sb.toString().replaceAll(", $", "");
        }
    }
    
    /**
     * Nested class for supplier business information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupplierBusinessInfo {
        @Size(max = 50, message = "Tax number must not exceed 50 characters")
        private String taxNumber;
        
        @Min(value = 0, message = "Payment terms must be non-negative")
        @Max(value = 365, message = "Payment terms must not exceed 365 days")
        private Integer paymentTerms; // in days
        
        @DecimalMin(value = "0.0", message = "Credit limit must be non-negative")
        private BigDecimal creditLimit;
        
        @Min(value = 1, message = "Rating must be at least 1")
        @Max(value = 5, message = "Rating must not exceed 5")
        private Double rating;
        
        @Size(max = 100, message = "Currency must not exceed 100 characters")
        private String currency;
        
        @Size(max = 500, message = "Notes must not exceed 500 characters")
        private String notes;
    }
    
    // Helper methods
    public String getPrimaryContactMethod() {
        if (contact != null) {
            if (contact.getEmail() != null && !contact.getEmail().isEmpty()) {
                return contact.getEmail();
            }
            if (contact.getPhone() != null && !contact.getPhone().isEmpty()) {
                return contact.getPhone();
            }
            if (contact.getMobile() != null && !contact.getMobile().isEmpty()) {
                return contact.getMobile();
            }
        }
        return null;
    }
    
    public boolean hasGoodRating() {
        return businessInfo != null && businessInfo.getRating() != null && businessInfo.getRating() >= 4.0;
    }
    
    public boolean isWithinCreditLimit(BigDecimal amount) {
        if (businessInfo == null || businessInfo.getCreditLimit() == null) {
            return true; // No limit set
        }
        return amount.compareTo(businessInfo.getCreditLimit()) <= 0;
    }
}
