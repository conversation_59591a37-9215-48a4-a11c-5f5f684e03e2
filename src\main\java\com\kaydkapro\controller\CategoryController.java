package com.kaydkapro.controller;

import com.kaydkapro.dto.request.CategoryRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.entity.Category;
import com.kaydkapro.service.CategoryService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Category Controller - REST API endpoints for category management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/categories")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class CategoryController {
    
    private final CategoryService categoryService;
    
    /**
     * Create a new category
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Category>> createCategory(@Valid @RequestBody CategoryRequest categoryRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Category category = categoryRequest.toEntity();
            Category createdCategory = categoryService.createCategory(category, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Category created successfully", createdCategory));
            
        } catch (Exception e) {
            log.error("Failed to create category: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update an existing category
     */
    @PutMapping("/{categoryId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Category>> updateCategory(@PathVariable String categoryId,
                                                               @Valid @RequestBody CategoryRequest categoryRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Category categoryUpdates = categoryRequest.toEntity();
            Category updatedCategory = categoryService.updateCategory(categoryId, categoryUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Category updated successfully", updatedCategory));
            
        } catch (Exception e) {
            log.error("Failed to update category {}: {}", categoryId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get category by ID
     */
    @GetMapping("/{categoryId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Category>> getCategory(@PathVariable String categoryId) {
        try {
            Category category = categoryService.getCategoryById(categoryId);
            return ResponseEntity.ok(ApiResponse.success("Category retrieved successfully", category));
            
        } catch (Exception e) {
            log.error("Failed to get category {}: {}", categoryId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Category not found"));
        }
    }
    
    /**
     * Get all active categories
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Category>>> getAllActiveCategories() {
        try {
            List<Category> categories = categoryService.getAllActiveCategories();
            return ResponseEntity.ok(ApiResponse.success("Categories retrieved successfully", categories));
            
        } catch (Exception e) {
            log.error("Failed to get categories: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get root categories (no parent)
     */
    @GetMapping("/root")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Category>>> getRootCategories() {
        try {
            List<Category> rootCategories = categoryService.getRootCategories();
            return ResponseEntity.ok(ApiResponse.success("Root categories retrieved successfully", rootCategories));
            
        } catch (Exception e) {
            log.error("Failed to get root categories: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get subcategories by parent ID
     */
    @GetMapping("/{parentId}/subcategories")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Category>>> getSubcategories(@PathVariable String parentId) {
        try {
            List<Category> subcategories = categoryService.getSubcategories(parentId);
            return ResponseEntity.ok(ApiResponse.success("Subcategories retrieved successfully", subcategories));
            
        } catch (Exception e) {
            log.error("Failed to get subcategories for parent {}: {}", parentId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get category hierarchy (parent and all children)
     */
    @GetMapping("/{categoryId}/hierarchy")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Category>>> getCategoryHierarchy(@PathVariable String categoryId) {
        try {
            List<Category> hierarchy = categoryService.getCategoryHierarchy(categoryId);
            return ResponseEntity.ok(ApiResponse.success("Category hierarchy retrieved successfully", hierarchy));
            
        } catch (Exception e) {
            log.error("Failed to get category hierarchy for {}: {}", categoryId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search categories
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Category>>> searchCategories(@RequestParam String query) {
        try {
            List<Category> categories = categoryService.searchCategories(query);
            return ResponseEntity.ok(ApiResponse.success("Category search completed", categories));
            
        } catch (Exception e) {
            log.error("Failed to search categories: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Move category to different parent
     */
    @PutMapping("/{categoryId}/move")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Category>> moveCategory(@PathVariable String categoryId,
                                                             @RequestParam(required = false) String newParentId,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Category movedCategory = categoryService.moveCategory(categoryId, newParentId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Category moved successfully", movedCategory));
            
        } catch (Exception e) {
            log.error("Failed to move category {}: {}", categoryId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Reorder categories
     */
    @PutMapping("/reorder")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<String>> reorderCategories(@RequestBody List<CategoryService.CategoryOrder> categoryOrders,
                                                                Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            categoryService.reorderCategories(categoryOrders, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Categories reordered successfully"));
            
        } catch (Exception e) {
            log.error("Failed to reorder categories: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Toggle category status (activate/deactivate)
     */
    @PatchMapping("/{categoryId}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Category>> toggleCategoryStatus(@PathVariable String categoryId,
                                                                     @RequestParam boolean isActive,
                                                                     Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Category category = categoryService.toggleCategoryStatus(categoryId, isActive, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Category status updated", category));
            
        } catch (Exception e) {
            log.error("Failed to toggle category status {}: {}", categoryId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete category (soft delete)
     */
    @DeleteMapping("/{categoryId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteCategory(@PathVariable String categoryId,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            categoryService.deleteCategory(categoryId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Category deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete category {}: {}", categoryId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get category statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<CategoryService.CategoryStats>> getCategoryStatistics() {
        try {
            CategoryService.CategoryStats stats = categoryService.getCategoryStats();
            return ResponseEntity.ok(ApiResponse.success("Category statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get category statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
