package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * QRCode Entity - Represents QR codes for products and other entities
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "qrCodes")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QRCode {
    
    @Id
    private String id;
    
    @NotNull(message = "Product information is required")
    private QRCodeProduct product;
    
    @NotBlank(message = "QR code is required")
    @Size(max = 100, message = "QR code must not exceed 100 characters")
    @Indexed(unique = true)
    private String qrCode;
    
    @NotBlank(message = "QR type is required")
    private String qrType; // PRODUCT_INFO, INVENTORY, CHECKOUT, PRICE_CHECK
    
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();
    
    @Builder.Default
    private Boolean isActive = true;
    
    private QRCodeAnalytics analytics;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @NotNull(message = "Created by information is required")
    private QRCodeUser createdBy;
    
    /**
     * Nested class for product information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QRCodeProduct {
        private String productId;
        private String productName;
        private String sku;
        private String categoryName;
    }
    
    /**
     * Nested class for QR code analytics
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QRCodeAnalytics {
        @Min(value = 0, message = "Scan count must be non-negative")
        @Builder.Default
        private Integer scanCount = 0;
        
        private LocalDateTime lastScanned;
        private LocalDateTime firstScanned;
        
        @Builder.Default
        private Map<String, Integer> scansByDate = new HashMap<>();
        
        @Builder.Default
        private Map<String, Integer> scansByUser = new HashMap<>();
        
        public void recordScan(String userId) {
            scanCount++;
            lastScanned = LocalDateTime.now();
            
            if (firstScanned == null) {
                firstScanned = lastScanned;
            }
            
            // Record scan by date
            String dateKey = lastScanned.toLocalDate().toString();
            scansByDate.put(dateKey, scansByDate.getOrDefault(dateKey, 0) + 1);
            
            // Record scan by user
            if (userId != null) {
                scansByUser.put(userId, scansByUser.getOrDefault(userId, 0) + 1);
            }
        }
        
        public Integer getScansToday() {
            String today = LocalDateTime.now().toLocalDate().toString();
            return scansByDate.getOrDefault(today, 0);
        }
        
        public Integer getScansByUser(String userId) {
            return scansByUser.getOrDefault(userId, 0);
        }
    }
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QRCodeUser {
        private String userId;
        private String userName;
    }
    
    // QR type constants
    public static final String TYPE_PRODUCT_INFO = "PRODUCT_INFO";
    public static final String TYPE_INVENTORY = "INVENTORY";
    public static final String TYPE_CHECKOUT = "CHECKOUT";
    public static final String TYPE_PRICE_CHECK = "PRICE_CHECK";
    public static final String TYPE_STOCK_CHECK = "STOCK_CHECK";
    
    // Helper methods
    public void addMetadata(String key, Object value) {
        if (metadata == null) {
            metadata = new HashMap<>();
        }
        metadata.put(key, value);
    }
    
    public Object getMetadata(String key) {
        return metadata != null ? metadata.get(key) : null;
    }
    
    public String getMetadataAsString(String key) {
        Object value = getMetadata(key);
        return value != null ? value.toString() : null;
    }
    
    public void recordScan(String userId) {
        if (analytics == null) {
            analytics = QRCodeAnalytics.builder().build();
        }
        analytics.recordScan(userId);
    }
    
    public boolean isProductInfo() {
        return TYPE_PRODUCT_INFO.equals(qrType);
    }
    
    public boolean isInventoryCheck() {
        return TYPE_INVENTORY.equals(qrType);
    }
    
    public boolean isCheckout() {
        return TYPE_CHECKOUT.equals(qrType);
    }
    
    public boolean isPriceCheck() {
        return TYPE_PRICE_CHECK.equals(qrType);
    }
    
    public String getProductUrl() {
        return getMetadataAsString("productUrl");
    }
    
    @SuppressWarnings("unchecked")
    public List<String> getQuickActions() {
        Object actions = getMetadata("quickActions");
        return actions instanceof List ? (List<String>) actions : null;
    }
    
    public Integer getTotalScans() {
        return analytics != null ? analytics.getScanCount() : 0;
    }
    
    public LocalDateTime getLastScanned() {
        return analytics != null ? analytics.getLastScanned() : null;
    }
    
    // Factory methods for common QR codes
    public static QRCode createProductQRCode(String productId, String productName, String sku, 
                                           String categoryName, String createdByUserId, String createdByUserName) {
        String qrCodeValue = "PRODUCT-" + sku + "-" + System.currentTimeMillis();
        
        return QRCode.builder()
                .product(QRCodeProduct.builder()
                        .productId(productId)
                        .productName(productName)
                        .sku(sku)
                        .categoryName(categoryName)
                        .build())
                .qrCode(qrCodeValue)
                .qrType(TYPE_PRODUCT_INFO)
                .metadata(Map.of(
                        "productUrl", "/products/" + productId,
                        "quickActions", List.of("VIEW_DETAILS", "ADD_TO_CART", "CHECK_STOCK"),
                        "description", "Product information QR code for " + productName
                ))
                .analytics(QRCodeAnalytics.builder().build())
                .createdBy(QRCodeUser.builder()
                        .userId(createdByUserId)
                        .userName(createdByUserName)
                        .build())
                .build();
    }
    
    public static QRCode createInventoryQRCode(String productId, String productName, String sku, 
                                             String warehouseId, String createdByUserId, String createdByUserName) {
        String qrCodeValue = "INVENTORY-" + sku + "-" + warehouseId + "-" + System.currentTimeMillis();
        
        return QRCode.builder()
                .product(QRCodeProduct.builder()
                        .productId(productId)
                        .productName(productName)
                        .sku(sku)
                        .build())
                .qrCode(qrCodeValue)
                .qrType(TYPE_INVENTORY)
                .metadata(Map.of(
                        "warehouseId", warehouseId,
                        "inventoryUrl", "/inventory/" + productId + "/" + warehouseId,
                        "quickActions", List.of("CHECK_STOCK", "ADJUST_STOCK", "MOVE_STOCK"),
                        "description", "Inventory management QR code for " + productName
                ))
                .analytics(QRCodeAnalytics.builder().build())
                .createdBy(QRCodeUser.builder()
                        .userId(createdByUserId)
                        .userName(createdByUserName)
                        .build())
                .build();
    }
}
