package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.TextIndexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Product Entity - Represents products in the inventory system
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "products")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Product {
    
    @Id
    private String id;
    
    @NotBlank(message = "Product name is required")
    @Size(max = 200, message = "Product name must not exceed 200 characters")
    @TextIndexed
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @TextIndexed
    private String description;
    
    @NotBlank(message = "SKU is required")
    @Size(max = 50, message = "SKU must not exceed 50 characters")
    @Indexed(unique = true)
    private String sku;
    
    @Size(max = 50, message = "Barcode must not exceed 50 characters")
    @Indexed(unique = true, sparse = true)
    private String barcode;
    
    @NotNull(message = "Category is required")
    private ProductCategory category;
    
    private ProductUnit unit;
    
    @NotNull(message = "Pricing information is required")
    private ProductPricing pricing;
    
    private StockLevels stockLevels;
    
    @Builder.Default
    private List<ProductImage> images = new ArrayList<>();
    
    @Builder.Default
    private List<ProductVariant> variants = new ArrayList<>();
    
    @Builder.Default
    private Boolean isActive = true;
    
    @Builder.Default
    private Boolean hasVariants = false;
    
    @Builder.Default
    private Boolean expiryTracking = false;
    
    @Builder.Default
    private Boolean batchTracking = false;
    
    @Builder.Default
    private List<String> tags = new ArrayList<>();
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    private String createdBy;
    private String updatedBy;
    
    /**
     * Nested class for product category information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductCategory {
        private String categoryId;
        private String categoryName;
    }
    
    /**
     * Nested class for product unit information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductUnit {
        private String unitId;
        private String unitName;
        private String abbreviation;
    }
    
    /**
     * Nested class for product pricing information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductPricing {
        @NotNull(message = "Cost price is required")
        @DecimalMin(value = "0.0", inclusive = false, message = "Cost price must be greater than 0")
        private BigDecimal costPrice;
        
        @NotNull(message = "Selling price is required")
        @DecimalMin(value = "0.0", inclusive = false, message = "Selling price must be greater than 0")
        private BigDecimal sellingPrice;
        
        @DecimalMin(value = "0.0", message = "Tax rate must be non-negative")
        private BigDecimal taxRate;
        
        public BigDecimal getProfitMargin() {
            if (costPrice != null && sellingPrice != null && costPrice.compareTo(BigDecimal.ZERO) > 0) {
                return sellingPrice.subtract(costPrice).divide(costPrice, 4, BigDecimal.ROUND_HALF_UP);
            }
            return BigDecimal.ZERO;
        }
        
        public BigDecimal getProfitAmount() {
            if (costPrice != null && sellingPrice != null) {
                return sellingPrice.subtract(costPrice);
            }
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * Nested class for stock level configuration
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StockLevels {
        private Integer minStockLevel;
        private Integer maxStockLevel;
        private Integer reorderLevel;
    }
    
    /**
     * Nested class for product images
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductImage {
        private String imageUrl;
        private Boolean isPrimary;
        private Integer sortOrder;
    }
    
    /**
     * Nested class for product variants
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductVariant {
        private String id;
        private String variantName;
        private String sku;
        private String barcode;
        private BigDecimal costPrice;
        private BigDecimal sellingPrice;
        private Boolean isActive;
    }
    
    // Helper methods
    public String getPrimaryImageUrl() {
        return images.stream()
                .filter(ProductImage::getIsPrimary)
                .findFirst()
                .map(ProductImage::getImageUrl)
                .orElse(null);
    }
    
    public void addTag(String tag) {
        if (!tags.contains(tag)) {
            tags.add(tag);
        }
    }
    
    public void removeTag(String tag) {
        tags.remove(tag);
    }
}
