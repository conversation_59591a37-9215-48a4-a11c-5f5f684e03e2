package com.kaydkapro.repository;

import com.kaydkapro.entity.Purchase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Purchase Repository - Data access layer for Purchase entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface PurchaseRepository extends MongoRepository<Purchase, String> {
    
    /**
     * Find purchase by purchase number
     */
    Optional<Purchase> findByPurchaseNumber(String purchaseNumber);
    
    /**
     * Find purchases by status
     */
    List<Purchase> findByStatus(String status);
    
    /**
     * Find purchases by status with pagination
     */
    Page<Purchase> findByStatus(String status, Pageable pageable);
    
    /**
     * Find purchases by supplier ID
     */
    @Query("{'supplier.supplierId': ?0}")
    List<Purchase> findBySupplierId(String supplierId);
    
    /**
     * Find purchases by supplier ID with pagination
     */
    @Query("{'supplier.supplierId': ?0}")
    Page<Purchase> findBySupplierId(String supplierId, Pageable pageable);
    
    /**
     * Find purchases by warehouse ID
     */
    @Query("{'warehouse.warehouseId': ?0}")
    List<Purchase> findByWarehouseId(String warehouseId);
    
    /**
     * Find purchases by date range
     */
    @Query("{'dates.purchaseDate': {'$gte': ?0, '$lte': ?1}}")
    List<Purchase> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find purchases by date range with pagination
     */
    @Query("{'dates.purchaseDate': {'$gte': ?0, '$lte': ?1}}")
    Page<Purchase> findByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * Find completed purchases by date range
     */
    @Query("{'status': 'COMPLETED', 'dates.purchaseDate': {'$gte': ?0, '$lte': ?1}}")
    List<Purchase> findCompletedPurchasesByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find purchases by created user
     */
    @Query("{'createdBy.userId': ?0}")
    List<Purchase> findByCreatedByUserId(String userId);
    
    /**
     * Find purchases by payment status
     */
    @Query("{'payment.paymentStatus': ?0}")
    List<Purchase> findByPaymentStatus(String paymentStatus);
    
    /**
     * Find unpaid purchases
     */
    @Query("{'payment.paymentStatus': {'$in': ['UNPAID', 'PARTIAL']}}")
    List<Purchase> findUnpaidPurchases();
    
    /**
     * Find pending orders
     */
    @Query("{'status': {'$in': ['PENDING', 'ORDERED']}}")
    List<Purchase> findPendingOrders();
    
    /**
     * Find overdue purchases (expected delivery date passed)
     */
    @Query("{'status': {'$in': ['ORDERED', 'PENDING']}, 'dates.expectedDate': {'$lt': ?0}}")
    List<Purchase> findOverduePurchases(LocalDate currentDate);
    
    /**
     * Find purchases by amount range
     */
    @Query("{'totals.totalAmount': {'$gte': ?0, '$lte': ?1}}")
    List<Purchase> findByAmountRange(BigDecimal minAmount, BigDecimal maxAmount);
    
    /**
     * Search purchases by purchase number or supplier name
     */
    @Query("{'$or': [" +
           "{'purchaseNumber': {'$regex': ?0, '$options': 'i'}}, " +
           "{'supplier.supplierName': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    Page<Purchase> searchPurchases(String searchTerm, Pageable pageable);
    
    /**
     * Check if purchase number exists
     */
    boolean existsByPurchaseNumber(String purchaseNumber);
    
    /**
     * Count purchases by status
     */
    long countByStatus(String status);
    
    /**
     * Count completed purchases today
     */
    @Query(value = "{'status': 'COMPLETED', 'dates.purchaseDate': ?0}", count = true)
    long countCompletedPurchasesToday(LocalDate today);
    
    /**
     * Get daily purchase summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'dates.purchaseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': '$dates.purchaseDate', " +
        "  'totalPurchases': { '$sum': 1 }, " +
        "  'totalAmount': { '$sum': '$totals.totalAmount' }, " +
        "  'averagePurchase': { '$avg': '$totals.totalAmount' }, " +
        "  'totalItems': { '$sum': { '$size': '$items' } } " +
        "} }",
        "{ '$sort': { '_id': 1 } }"
    })
    List<DailyPurchaseStats> getDailyPurchaseStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get monthly purchase summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'dates.purchaseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': { 'year': { '$year': '$dates.purchaseDate' }, 'month': { '$month': '$dates.purchaseDate' } }, " +
        "  'totalPurchases': { '$sum': 1 }, " +
        "  'totalAmount': { '$sum': '$totals.totalAmount' }, " +
        "  'averagePurchase': { '$avg': '$totals.totalAmount' } " +
        "} }",
        "{ '$sort': { '_id.year': 1, '_id.month': 1 } }"
    })
    List<MonthlyPurchaseStats> getMonthlyPurchaseStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get top suppliers by purchase volume
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED' } }",
        "{ '$group': { " +
        "  '_id': '$supplier.supplierId', " +
        "  'supplierName': { '$first': '$supplier.supplierName' }, " +
        "  'totalPurchases': { '$sum': 1 }, " +
        "  'totalAmount': { '$sum': '$totals.totalAmount' }, " +
        "  'averageAmount': { '$avg': '$totals.totalAmount' }, " +
        "  'lastPurchaseDate': { '$max': '$dates.purchaseDate' } " +
        "} }",
        "{ '$sort': { 'totalAmount': -1 } }",
        "{ '$limit': ?0 }"
    })
    List<TopSupplierStats> getTopSuppliers(int limit);
    
    /**
     * Find recent purchases (last N days)
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<Purchase> findRecentPurchases(LocalDateTime since);
    
    /**
     * Find large purchases (above threshold)
     */
    @Query("{'totals.totalAmount': {'$gte': ?0}, 'status': 'COMPLETED'}")
    List<Purchase> findLargePurchases(BigDecimal threshold);
    
    /**
     * Find purchases with partial delivery
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': { '$in': ['RECEIVED', 'COMPLETED'] } } }",
        "{ '$unwind': '$items' }",
        "{ '$match': { '$expr': { '$lt': ['$items.receivedQuantity', '$items.quantity'] } } }",
        "{ '$group': { '_id': '$_id', 'purchase': { '$first': '$$ROOT' } } }",
        "{ '$replaceRoot': { 'newRoot': '$purchase' } }"
    })
    List<Purchase> findPurchasesWithPartialDelivery();
    
    /**
     * Calculate total purchase amount for period
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'dates.purchaseDate': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': null, 'totalAmount': { '$sum': '$totals.totalAmount' } } }"
    })
    Optional<BigDecimal> calculateTotalPurchaseAmount(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get purchase performance by supplier
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED' } }",
        "{ '$group': { " +
        "  '_id': '$supplier.supplierId', " +
        "  'supplierName': { '$first': '$supplier.supplierName' }, " +
        "  'totalOrders': { '$sum': 1 }, " +
        "  'totalAmount': { '$sum': '$totals.totalAmount' }, " +
        "  'averageDeliveryTime': { '$avg': { '$subtract': ['$dates.deliveryDate', '$dates.purchaseDate'] } }, " +
        "  'onTimeDeliveries': { '$sum': { '$cond': [{ '$lte': ['$dates.deliveryDate', '$dates.expectedDate'] }, 1, 0] } } " +
        "} }",
        "{ '$addFields': { 'onTimePercentage': { '$multiply': [{ '$divide': ['$onTimeDeliveries', '$totalOrders'] }, 100] } } }",
        "{ '$sort': { 'onTimePercentage': -1 } }"
    })
    List<SupplierPerformanceStats> getSupplierPerformanceStats();
    
    /**
     * Interface for daily purchase statistics projection
     */
    interface DailyPurchaseStats {
        LocalDate getId();
        Long getTotalPurchases();
        BigDecimal getTotalAmount();
        BigDecimal getAveragePurchase();
        Long getTotalItems();
    }
    
    /**
     * Interface for monthly purchase statistics projection
     */
    interface MonthlyPurchaseStats {
        MonthYear getId();
        Long getTotalPurchases();
        BigDecimal getTotalAmount();
        BigDecimal getAveragePurchase();
        
        interface MonthYear {
            Integer getYear();
            Integer getMonth();
        }
    }
    
    /**
     * Interface for top supplier statistics projection
     */
    interface TopSupplierStats {
        String getId();
        String getSupplierName();
        Long getTotalPurchases();
        BigDecimal getTotalAmount();
        BigDecimal getAverageAmount();
        LocalDate getLastPurchaseDate();
    }
    
    /**
     * Interface for supplier performance statistics projection
     */
    interface SupplierPerformanceStats {
        String getId();
        String getSupplierName();
        Long getTotalOrders();
        BigDecimal getTotalAmount();
        Double getAverageDeliveryTime();
        Long getOnTimeDeliveries();
        Double getOnTimePercentage();
    }
}
