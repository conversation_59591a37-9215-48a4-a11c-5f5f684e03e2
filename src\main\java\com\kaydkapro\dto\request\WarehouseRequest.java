package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Warehouse;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * Warehouse Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class WarehouseRequest {
    
    @NotBlank(message = "Warehouse name is required")
    @Size(max = 200, message = "Warehouse name must not exceed 200 characters")
    private String name;
    
    @NotBlank(message = "Warehouse code is required")
    @Size(max = 20, message = "Warehouse code must not exceed 20 characters")
    @Pattern(regexp = "^[A-Z0-9_-]+$", message = "Warehouse code can only contain uppercase letters, numbers, underscores, and hyphens")
    private String code;
    
    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;
    
    @NotBlank(message = "Warehouse type is required")
    @Size(max = 50, message = "Type must not exceed 50 characters")
    private String type;
    
    @NotNull(message = "Location information is required")
    private LocationInfo location;
    
    private ContactInfo contact;
    
    private CapacityInfo capacity;
    
    private Boolean isActive = true;
    
    private Boolean isDefault = false;
    
    @Data
    public static class LocationInfo {
        @NotBlank(message = "Address is required")
        @Size(max = 500, message = "Address must not exceed 500 characters")
        private String address;
        
        @NotBlank(message = "City is required")
        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;
        
        @Size(max = 100, message = "State must not exceed 100 characters")
        private String state;
        
        @NotBlank(message = "Country is required")
        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;
        
        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;
        
        @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
        @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
        private BigDecimal latitude;
        
        @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
        @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
        private BigDecimal longitude;
    }
    
    @Data
    public static class ContactInfo {
        @Size(max = 100, message = "Manager name must not exceed 100 characters")
        private String managerName;
        
        @Size(max = 20, message = "Phone number must not exceed 20 characters")
        @Pattern(regexp = "^[+]?[0-9\\s\\-\\(\\)]*$", message = "Invalid phone number format")
        private String phone;
        
        @Email(message = "Email should be valid")
        @Size(max = 100, message = "Email must not exceed 100 characters")
        private String email;
        
        @Size(max = 20, message = "Fax number must not exceed 20 characters")
        private String fax;
    }
    
    @Data
    public static class CapacityInfo {
        @DecimalMin(value = "0", message = "Total area must be non-negative")
        private BigDecimal totalArea;
        
        @DecimalMin(value = "0", message = "Storage area must be non-negative")
        private BigDecimal storageArea;
        
        @DecimalMin(value = "0", message = "Maximum weight must be non-negative")
        private BigDecimal maxWeight;
        
        @DecimalMin(value = "0", message = "Maximum volume must be non-negative")
        private BigDecimal maxVolume;
        
        @Size(max = 20, message = "Area unit must not exceed 20 characters")
        private String areaUnit = "sqm";
        
        @Size(max = "20", message = "Weight unit must not exceed 20 characters")
        private String weightUnit = "kg";
        
        @Size(max = "20", message = "Volume unit must not exceed 20 characters")
        private String volumeUnit = "cbm";
    }
    
    /**
     * Convert DTO to Entity
     */
    public Warehouse toEntity() {
        Warehouse warehouse = Warehouse.builder()
                .name(this.name)
                .code(this.code)
                .description(this.description)
                .type(this.type)
                .isActive(this.isActive)
                .isDefault(this.isDefault)
                .build();
        
        // Set location
        if (this.location != null) {
            warehouse.setLocation(Warehouse.WarehouseLocation.builder()
                    .address(this.location.getAddress())
                    .city(this.location.getCity())
                    .state(this.location.getState())
                    .country(this.location.getCountry())
                    .postalCode(this.location.getPostalCode())
                    .latitude(this.location.getLatitude())
                    .longitude(this.location.getLongitude())
                    .build());
        }
        
        // Set contact
        if (this.contact != null) {
            warehouse.setContact(Warehouse.WarehouseContact.builder()
                    .managerName(this.contact.getManagerName())
                    .phone(this.contact.getPhone())
                    .email(this.contact.getEmail())
                    .fax(this.contact.getFax())
                    .build());
        }
        
        // Set capacity
        if (this.capacity != null) {
            warehouse.setCapacity(Warehouse.WarehouseCapacity.builder()
                    .totalArea(this.capacity.getTotalArea())
                    .storageArea(this.capacity.getStorageArea())
                    .maxWeight(this.capacity.getMaxWeight())
                    .maxVolume(this.capacity.getMaxVolume())
                    .areaUnit(this.capacity.getAreaUnit())
                    .weightUnit(this.capacity.getWeightUnit())
                    .volumeUnit(this.capacity.getVolumeUnit())
                    .build());
        }
        
        return warehouse;
    }
}
