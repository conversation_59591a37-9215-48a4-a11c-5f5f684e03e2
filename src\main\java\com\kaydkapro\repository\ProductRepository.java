package com.kaydkapro.repository;

import com.kaydkapro.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Product Repository - Data access layer for Product entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface ProductRepository extends MongoRepository<Product, String> {
    
    /**
     * Find product by SKU
     */
    Optional<Product> findBySku(String sku);
    
    /**
     * Find product by barcode
     */
    Optional<Product> findByBarcode(String barcode);
    
    /**
     * Find all active products
     */
    List<Product> findByIsActiveTrue();
    
    /**
     * Find products by category
     */
    @Query("{'category.categoryId': ?0, 'isActive': true}")
    List<Product> findByCategoryId(String categoryId);
    
    /**
     * Find products by category with pagination
     */
    @Query("{'category.categoryId': ?0, 'isActive': true}")
    Page<Product> findByCategoryId(String categoryId, Pageable pageable);
    
    /**
     * Search products by text (name, description, SKU, tags)
     */
    @Query("{'$and': [{'isActive': true}, {'$or': [" +
           "{'name': {'$regex': ?0, '$options': 'i'}}, " +
           "{'description': {'$regex': ?0, '$options': 'i'}}, " +
           "{'sku': {'$regex': ?0, '$options': 'i'}}, " +
           "{'tags': {'$regex': ?0, '$options': 'i'}}" +
           "]}]}")
    Page<Product> searchProducts(String searchTerm, Pageable pageable);
    
    /**
     * Find products by price range
     */
    @Query("{'pricing.sellingPrice': {'$gte': ?0, '$lte': ?1}, 'isActive': true}")
    List<Product> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * Find products with variants
     */
    @Query("{'hasVariants': true, 'isActive': true}")
    List<Product> findProductsWithVariants();
    
    /**
     * Find products with expiry tracking
     */
    @Query("{'expiryTracking': true, 'isActive': true}")
    List<Product> findProductsWithExpiryTracking();
    
    /**
     * Find products with batch tracking
     */
    @Query("{'batchTracking': true, 'isActive': true}")
    List<Product> findProductsWithBatchTracking();
    
    /**
     * Find products by tags
     */
    @Query("{'tags': {'$in': ?0}, 'isActive': true}")
    List<Product> findByTags(List<String> tags);
    
    /**
     * Find products created by user
     */
    List<Product> findByCreatedBy(String createdBy);
    
    /**
     * Find products created between dates
     */
    List<Product> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Check if SKU exists
     */
    boolean existsBySku(String sku);
    
    /**
     * Check if barcode exists
     */
    boolean existsByBarcode(String barcode);
    
    /**
     * Check if SKU exists excluding current product
     */
    @Query("{'sku': ?0, '_id': {'$ne': ?1}}")
    boolean existsBySkuAndIdNot(String sku, String id);
    
    /**
     * Check if barcode exists excluding current product
     */
    @Query("{'barcode': ?0, '_id': {'$ne': ?1}}")
    boolean existsByBarcodeAndIdNot(String barcode, String id);
    
    /**
     * Count active products
     */
    long countByIsActiveTrue();
    
    /**
     * Count products by category
     */
    @Query(value = "{'category.categoryId': ?0, 'isActive': true}", count = true)
    long countByCategoryId(String categoryId);
    
    /**
     * Find low stock products using aggregation
     */
    @Aggregation(pipeline = {
        "{ '$lookup': { 'from': 'inventory', 'localField': '_id', 'foreignField': 'product.productId', 'as': 'inventory' } }",
        "{ '$unwind': { 'path': '$inventory', 'preserveNullAndEmptyArrays': true } }",
        "{ '$group': { '_id': '$_id', 'product': { '$first': '$$ROOT' }, 'totalStock': { '$sum': '$inventory.quantities.available' } } }",
        "{ '$match': { '$expr': { '$and': [ { '$ne': ['$product.stockLevels.minStockLevel', null] }, { '$lte': ['$totalStock', '$product.stockLevels.minStockLevel'] } ] } } }",
        "{ '$replaceRoot': { 'newRoot': '$product' } }"
    })
    List<Product> findLowStockProducts();
    
    /**
     * Find products expiring soon (requires expiry tracking)
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'expiryTracking': true, 'isActive': true } }",
        "{ '$lookup': { 'from': 'stockMovements', 'localField': '_id', 'foreignField': 'product.productId', 'as': 'movements' } }",
        "{ '$unwind': { 'path': '$movements', 'preserveNullAndEmptyArrays': true } }",
        "{ '$match': { 'movements.batchInfo.expiryDate': { '$gte': new Date(), '$lte': new Date(Date.now() + 30*24*60*60*1000) } } }",
        "{ '$group': { '_id': '$_id', 'product': { '$first': '$$ROOT' } } }",
        "{ '$replaceRoot': { 'newRoot': '$product' } }"
    })
    List<Product> findProductsExpiringSoon();
    
    /**
     * Find top selling products
     */
    @Aggregation(pipeline = {
        "{ '$lookup': { 'from': 'sales', 'localField': '_id', 'foreignField': 'items.product.productId', 'as': 'sales' } }",
        "{ '$unwind': { 'path': '$sales', 'preserveNullAndEmptyArrays': true } }",
        "{ '$unwind': { 'path': '$sales.items', 'preserveNullAndEmptyArrays': true } }",
        "{ '$match': { 'sales.items.product.productId': { '$exists': true }, 'sales.status': 'COMPLETED' } }",
        "{ '$group': { '_id': '$_id', 'product': { '$first': '$$ROOT' }, 'totalSold': { '$sum': '$sales.items.quantity' }, 'totalRevenue': { '$sum': '$sales.items.totalPrice' } } }",
        "{ '$sort': { 'totalSold': -1 } }",
        "{ '$limit': ?0 }",
        "{ '$replaceRoot': { 'newRoot': '$product' } }"
    })
    List<Product> findTopSellingProducts(int limit);
    
    /**
     * Find products by profit margin range
     */
    @Query("{'$expr': { '$and': [ " +
           "{ '$gte': [ { '$divide': [ { '$subtract': ['$pricing.sellingPrice', '$pricing.costPrice'] }, '$pricing.costPrice' ] }, ?0 ] }, " +
           "{ '$lte': [ { '$divide': [ { '$subtract': ['$pricing.sellingPrice', '$pricing.costPrice'] }, '$pricing.costPrice' ] }, ?1 ] } " +
           "] }, 'isActive': true}")
    List<Product> findByProfitMarginRange(BigDecimal minMargin, BigDecimal maxMargin);
    
    /**
     * Find recently added products
     */
    @Query("{'createdAt': {'$gte': ?0}, 'isActive': true}")
    List<Product> findRecentlyAddedProducts(LocalDateTime since);
    
    /**
     * Find products without images
     */
    @Query("{'$or': [{'images': {'$size': 0}}, {'images': null}], 'isActive': true}")
    List<Product> findProductsWithoutImages();
}
