package com.kaydkapro.controller;

import com.kaydkapro.dto.request.CustomerRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.Customer;
import com.kaydkapro.service.CustomerService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * Customer Controller - REST API endpoints for customer management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/customers")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class CustomerController {
    
    private final CustomerService customerService;
    
    /**
     * Create a new customer
     */
    @PostMapping
    @PreAuthorize("hasRole('CASHIER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Customer>> createCustomer(@Valid @RequestBody CustomerRequest customerRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Customer customer = customerRequest.toEntity();
            Customer createdCustomer = customerService.createCustomer(customer, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Customer created successfully", createdCustomer));
            
        } catch (Exception e) {
            log.error("Failed to create customer: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update an existing customer
     */
    @PutMapping("/{customerId}")
    @PreAuthorize("hasRole('CASHIER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Customer>> updateCustomer(@PathVariable String customerId,
                                                               @Valid @RequestBody CustomerRequest customerRequest,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Customer customerUpdates = customerRequest.toEntity();
            Customer updatedCustomer = customerService.updateCustomer(customerId, customerUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Customer updated successfully", updatedCustomer));
            
        } catch (Exception e) {
            log.error("Failed to update customer {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get customer by ID
     */
    @GetMapping("/{customerId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Customer>> getCustomer(@PathVariable String customerId) {
        try {
            Customer customer = customerService.getCustomerById(customerId);
            return ResponseEntity.ok(ApiResponse.success("Customer retrieved successfully", customer));
            
        } catch (Exception e) {
            log.error("Failed to get customer {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Customer not found"));
        }
    }
    
    /**
     * Get customer by email
     */
    @GetMapping("/email/{email}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Customer>> getCustomerByEmail(@PathVariable String email) {
        try {
            Customer customer = customerService.getCustomerByEmail(email);
            return ResponseEntity.ok(ApiResponse.success("Customer retrieved successfully", customer));
            
        } catch (Exception e) {
            log.error("Failed to get customer by email {}: {}", email, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Customer not found"));
        }
    }
    
    /**
     * Get customer by phone
     */
    @GetMapping("/phone/{phone}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Customer>> getCustomerByPhone(@PathVariable String phone) {
        try {
            Customer customer = customerService.getCustomerByPhone(phone);
            return ResponseEntity.ok(ApiResponse.success("Customer retrieved successfully", customer));
            
        } catch (Exception e) {
            log.error("Failed to get customer by phone {}: {}", phone, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Customer not found"));
        }
    }
    
    /**
     * Get all customers with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Customer>>> getCustomers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "firstName") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Customer> customerPage = customerService.getCustomers(pageable);
            PageResponse<Customer> pageResponse = PageResponse.of(customerPage);
            
            return ResponseEntity.ok(ApiResponse.success("Customers retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get customers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search customers
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Customer>>> searchCustomers(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Customer> customerPage = customerService.searchCustomers(query, pageable);
            PageResponse<Customer> pageResponse = PageResponse.of(customerPage);
            
            return ResponseEntity.ok(ApiResponse.success("Customer search completed", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to search customers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get customers by type
     */
    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Customer>>> getCustomersByType(@PathVariable String type) {
        try {
            List<Customer> customers = customerService.getCustomersByType(type);
            return ResponseEntity.ok(ApiResponse.success("Customers retrieved successfully", customers));
            
        } catch (Exception e) {
            log.error("Failed to get customers by type {}: {}", type, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get active customers
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Customer>>> getActiveCustomers() {
        try {
            List<Customer> customers = customerService.getActiveCustomers();
            return ResponseEntity.ok(ApiResponse.success("Active customers retrieved successfully", customers));
            
        } catch (Exception e) {
            log.error("Failed to get active customers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get VIP customers
     */
    @GetMapping("/vip")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Customer>>> getVipCustomers() {
        try {
            List<Customer> customers = customerService.getVipCustomers();
            return ResponseEntity.ok(ApiResponse.success("VIP customers retrieved successfully", customers));
            
        } catch (Exception e) {
            log.error("Failed to get VIP customers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get top customers by purchase amount
     */
    @GetMapping("/top-customers")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<Customer>>> getTopCustomers(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Customer> customers = customerService.getTopCustomers(limit);
            return ResponseEntity.ok(ApiResponse.success("Top customers retrieved successfully", customers));
            
        } catch (Exception e) {
            log.error("Failed to get top customers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update customer credit limit
     */
    @PatchMapping("/{customerId}/credit-limit")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Customer>> updateCreditLimit(@PathVariable String customerId,
                                                                  @RequestParam BigDecimal creditLimit,
                                                                  Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Customer customer = customerService.updateCreditLimit(customerId, creditLimit, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Credit limit updated successfully", customer));
            
        } catch (Exception e) {
            log.error("Failed to update credit limit for customer {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update customer loyalty points
     */
    @PatchMapping("/{customerId}/loyalty-points")
    @PreAuthorize("hasRole('CASHIER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Customer>> updateLoyaltyPoints(@PathVariable String customerId,
                                                                    @RequestParam Integer points,
                                                                    @RequestParam String operation, // ADD or SUBTRACT
                                                                    Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Customer customer = customerService.updateLoyaltyPoints(customerId, points, operation, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Loyalty points updated successfully", customer));
            
        } catch (Exception e) {
            log.error("Failed to update loyalty points for customer {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Toggle customer status (activate/deactivate)
     */
    @PatchMapping("/{customerId}/status")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Customer>> toggleCustomerStatus(@PathVariable String customerId,
                                                                     @RequestParam boolean isActive,
                                                                     Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Customer customer = customerService.toggleCustomerStatus(customerId, isActive, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Customer status updated", customer));
            
        } catch (Exception e) {
            log.error("Failed to toggle customer status {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Set customer as VIP
     */
    @PatchMapping("/{customerId}/vip")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Customer>> setCustomerVip(@PathVariable String customerId,
                                                               @RequestParam boolean isVip,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Customer customer = customerService.setCustomerVip(customerId, isVip, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Customer VIP status updated", customer));
            
        } catch (Exception e) {
            log.error("Failed to set customer VIP status {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get customer purchase history
     */
    @GetMapping("/{customerId}/purchase-history")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<CustomerService.CustomerPurchaseHistory>> getCustomerPurchaseHistory(
            @PathVariable String customerId) {
        try {
            CustomerService.CustomerPurchaseHistory history = customerService.getCustomerPurchaseHistory(customerId);
            return ResponseEntity.ok(ApiResponse.success("Customer purchase history retrieved", history));
            
        } catch (Exception e) {
            log.error("Failed to get customer purchase history {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get customer statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<CustomerService.CustomerStats>> getCustomerStatistics() {
        try {
            CustomerService.CustomerStats stats = customerService.getCustomerStats();
            return ResponseEntity.ok(ApiResponse.success("Customer statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get customer statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete customer (soft delete)
     */
    @DeleteMapping("/{customerId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteCustomer(@PathVariable String customerId,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            customerService.deleteCustomer(customerId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Customer deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete customer {}: {}", customerId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
