package com.kaydkapro.dto.response;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * Login Response DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
public class LoginResponse {
    
    private String accessToken;
    private String tokenType;
    private String userId;
    private String username;
    private String email;
    private String firstName;
    private String lastName;
    private List<String> roles;
    private String profileImage;
    private Long expiresIn; // Token expiration time in seconds
}
