package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Sales Entity - Represents sales transactions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "sales")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Sales {
    
    @Id
    private String id;
    
    @NotBlank(message = "Sale number is required")
    @Size(max = 50, message = "Sale number must not exceed 50 characters")
    @Indexed(unique = true)
    private String saleNumber;
    
    private SaleCustomer customer;
    
    @NotNull(message = "Warehouse information is required")
    private SaleWarehouse warehouse;
    
    @NotNull(message = "Sale date time is required")
    private SaleDateTime saleDateTime;
    
    @NotBlank(message = "Status is required")
    private String status; // PENDING, COMPLETED, RETURNED, CANCELLED
    
    @Builder.Default
    private List<SaleItem> items = new ArrayList<>();
    
    @NotNull(message = "Totals information is required")
    private SaleTotals totals;
    
    @NotNull(message = "Payment information is required")
    private SalePayment payment;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    private String notes;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    @NotNull(message = "Created by information is required")
    private SaleUser createdBy;
    
    /**
     * Nested class for customer information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleCustomer {
        private String customerId;
        private String customerName;
        private String phone;
        private String email;
    }
    
    /**
     * Nested class for warehouse information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleWarehouse {
        private String warehouseId;
        private String warehouseName;
        private String warehouseCode;
    }
    
    /**
     * Nested class for sale date and time
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleDateTime {
        private LocalDate date;
        private String time;
    }
    
    /**
     * Nested class for sale items
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleItem {
        private String id;
        private SaleProduct product;
        
        @NotNull(message = "Quantity is required")
        @DecimalMin(value = "0.0", inclusive = false, message = "Quantity must be greater than 0")
        private BigDecimal quantity;
        
        @NotNull(message = "Unit price is required")
        @DecimalMin(value = "0.0", inclusive = false, message = "Unit price must be greater than 0")
        private BigDecimal unitPrice;
        
        @DecimalMin(value = "0.0", message = "Discount amount must be non-negative")
        @Builder.Default
        private BigDecimal discountAmount = BigDecimal.ZERO;
        
        private BigDecimal totalPrice;
        private BigDecimal costPrice;
        private BigDecimal profit;
        private String batchNumber;
        
        public BigDecimal calculateTotalPrice() {
            if (quantity != null && unitPrice != null) {
                BigDecimal total = quantity.multiply(unitPrice);
                if (discountAmount != null) {
                    total = total.subtract(discountAmount);
                }
                return total;
            }
            return BigDecimal.ZERO;
        }
        
        public BigDecimal calculateProfit() {
            if (costPrice != null && totalPrice != null) {
                BigDecimal totalCost = costPrice.multiply(quantity);
                return totalPrice.subtract(totalCost);
            }
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * Nested class for product information in sale item
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleProduct {
        private String productId;
        private String productName;
        private String sku;
    }
    
    /**
     * Nested class for sale totals
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleTotals {
        @NotNull(message = "Subtotal is required")
        @DecimalMin(value = "0.0", message = "Subtotal must be non-negative")
        private BigDecimal subtotal;
        
        @DecimalMin(value = "0.0", message = "Tax amount must be non-negative")
        @Builder.Default
        private BigDecimal taxAmount = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Discount amount must be non-negative")
        @Builder.Default
        private BigDecimal discountAmount = BigDecimal.ZERO;
        
        @NotNull(message = "Total amount is required")
        @DecimalMin(value = "0.0", message = "Total amount must be non-negative")
        private BigDecimal totalAmount;
        
        public BigDecimal calculateTotalAmount() {
            if (subtotal != null) {
                BigDecimal total = subtotal;
                if (taxAmount != null) {
                    total = total.add(taxAmount);
                }
                if (discountAmount != null) {
                    total = total.subtract(discountAmount);
                }
                return total;
            }
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * Nested class for payment information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SalePayment {
        @NotNull(message = "Paid amount is required")
        @DecimalMin(value = "0.0", message = "Paid amount must be non-negative")
        private BigDecimal paidAmount;
        
        @DecimalMin(value = "0.0", message = "Change amount must be non-negative")
        @Builder.Default
        private BigDecimal changeAmount = BigDecimal.ZERO;
        
        @NotBlank(message = "Payment method is required")
        private String paymentMethod; // CASH, CARD, MOBILE, CREDIT, MIXED
        
        @NotBlank(message = "Payment status is required")
        private String paymentStatus; // UNPAID, PAID, PARTIAL
    }
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleUser {
        private String userId;
        private String userName;
    }
    
    // Status constants
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_RETURNED = "RETURNED";
    public static final String STATUS_CANCELLED = "CANCELLED";
    
    // Payment method constants
    public static final String PAYMENT_CASH = "CASH";
    public static final String PAYMENT_CARD = "CARD";
    public static final String PAYMENT_MOBILE = "MOBILE";
    public static final String PAYMENT_CREDIT = "CREDIT";
    public static final String PAYMENT_MIXED = "MIXED";
    
    // Payment status constants
    public static final String PAYMENT_UNPAID = "UNPAID";
    public static final String PAYMENT_PAID = "PAID";
    public static final String PAYMENT_PARTIAL = "PARTIAL";
    
    // Helper methods
    public BigDecimal getTotalProfit() {
        return items.stream()
                .map(SaleItem::calculateProfit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    public int getTotalItemCount() {
        return items.size();
    }
    
    public BigDecimal getTotalQuantity() {
        return items.stream()
                .map(SaleItem::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }
    
    public boolean isPaid() {
        return payment != null && PAYMENT_PAID.equals(payment.getPaymentStatus());
    }
}
