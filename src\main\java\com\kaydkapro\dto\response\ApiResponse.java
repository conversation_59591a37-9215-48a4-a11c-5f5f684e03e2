package com.kaydkapro.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * Generic API Response DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    
    private boolean success;
    private String message;
    private T data;
    private String error;
    private LocalDateTime timestamp;
    private String path;
    private Integer status;
    
    // Static factory methods for success responses
    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .status(200)
                .build();
    }
    
    public static <T> ApiResponse<T> success(T data) {
        return success("Operation successful", data);
    }
    
    public static ApiResponse<Void> success(String message) {
        return success(message, null);
    }
    
    public static ApiResponse<Void> success() {
        return success("Operation successful", null);
    }
    
    // Static factory methods for error responses
    public static <T> ApiResponse<T> error(String error) {
        return ApiResponse.<T>builder()
                .success(false)
                .error(error)
                .timestamp(LocalDateTime.now())
                .status(400)
                .build();
    }
    
    public static <T> ApiResponse<T> error(String error, Integer status) {
        return ApiResponse.<T>builder()
                .success(false)
                .error(error)
                .timestamp(LocalDateTime.now())
                .status(status)
                .build();
    }
    
    public static <T> ApiResponse<T> error(String error, String path, Integer status) {
        return ApiResponse.<T>builder()
                .success(false)
                .error(error)
                .path(path)
                .timestamp(LocalDateTime.now())
                .status(status)
                .build();
    }
    
    // Validation error response
    public static <T> ApiResponse<T> validationError(String error) {
        return error(error, 422);
    }
    
    // Not found error response
    public static <T> ApiResponse<T> notFound(String error) {
        return error(error, 404);
    }
    
    // Unauthorized error response
    public static <T> ApiResponse<T> unauthorized(String error) {
        return error(error, 401);
    }
    
    // Forbidden error response
    public static <T> ApiResponse<T> forbidden(String error) {
        return error(error, 403);
    }
    
    // Internal server error response
    public static <T> ApiResponse<T> internalError(String error) {
        return error(error, 500);
    }
}
