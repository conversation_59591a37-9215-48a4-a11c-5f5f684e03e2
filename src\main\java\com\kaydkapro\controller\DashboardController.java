package com.kaydkapro.controller;

import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.service.*;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * Dashboard Controller - REST API endpoints for dashboard data and analytics
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class DashboardController {
    
    private final UserService userService;
    private final ProductService productService;
    private final InventoryService inventoryService;
    private final SalesService salesService;
    private final FinancialService financialService;
    private final CategoryService categoryService;
    private final NotificationService notificationService;
    private final QRCodeService qrCodeService;
    
    /**
     * Get dashboard overview
     */
    @GetMapping("/overview")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<DashboardOverview>> getDashboardOverview(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            // Get basic statistics
            UserService.UserStats userStats = userService.getUserStats();
            ProductService.ProductStats productStats = productService.getProductStats();
            SalesService.SalesStats salesStats = salesService.getSalesStats();
            FinancialService.FinancialStats financialStats = financialService.getFinancialStats();
            CategoryService.CategoryStats categoryStats = categoryService.getCategoryStats();
            NotificationService.NotificationStats notificationStats = notificationService.getNotificationStats(userPrincipal.getId());
            QRCodeService.QRCodeStats qrCodeStats = qrCodeService.getQRCodeStats();
            
            // Calculate additional metrics
            BigDecimal totalInventoryValue = inventoryService.calculateTotalInventoryValue();
            long unreadNotifications = notificationService.getUnreadNotificationCount(userPrincipal.getId());
            
            DashboardOverview overview = DashboardOverview.builder()
                    .userStats(userStats)
                    .productStats(productStats)
                    .salesStats(salesStats)
                    .financialStats(financialStats)
                    .categoryStats(categoryStats)
                    .notificationStats(notificationStats)
                    .qrCodeStats(qrCodeStats)
                    .totalInventoryValue(totalInventoryValue)
                    .unreadNotifications(unreadNotifications)
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Dashboard overview retrieved", overview));
            
        } catch (Exception e) {
            log.error("Failed to get dashboard overview: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get sales analytics
     */
    @GetMapping("/analytics/sales")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SalesAnalytics>> getSalesAnalytics(
            @RequestParam(defaultValue = "30") int days) {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days);
            
            // Get sales data
            var dailySalesStats = salesService.getDailySalesStats(startDate, endDate);
            var topCustomers = salesService.getTopCustomers(10);
            BigDecimal totalRevenue = salesService.calculateTotalRevenue(startDate, endDate);
            SalesService.SalesStats salesStats = salesService.getSalesStats();
            
            SalesAnalytics analytics = SalesAnalytics.builder()
                    .dailySalesStats(dailySalesStats)
                    .topCustomers(topCustomers)
                    .totalRevenue(totalRevenue)
                    .salesStats(salesStats)
                    .periodDays(days)
                    .startDate(startDate)
                    .endDate(endDate)
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Sales analytics retrieved", analytics));
            
        } catch (Exception e) {
            log.error("Failed to get sales analytics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get financial analytics
     */
    @GetMapping("/analytics/financial")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<FinancialAnalytics>> getFinancialAnalytics(
            @RequestParam(defaultValue = "30") int days) {
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days);
            
            // Get financial data
            var financialSummary = financialService.getFinancialSummary(startDate, endDate);
            var incomeCategoryStats = financialService.getIncomeByCategoryStats(startDate, endDate);
            var expenseCategoryStats = financialService.getExpensesByCategoryStats(startDate, endDate);
            var dailyIncomeStats = financialService.getDailyIncomeStats(startDate, endDate);
            var dailyExpenseStats = financialService.getDailyExpenseStats(startDate, endDate);
            
            FinancialAnalytics analytics = FinancialAnalytics.builder()
                    .financialSummary(financialSummary)
                    .incomeCategoryStats(incomeCategoryStats)
                    .expenseCategoryStats(expenseCategoryStats)
                    .dailyIncomeStats(dailyIncomeStats)
                    .dailyExpenseStats(dailyExpenseStats)
                    .periodDays(days)
                    .startDate(startDate)
                    .endDate(endDate)
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Financial analytics retrieved", analytics));
            
        } catch (Exception e) {
            log.error("Failed to get financial analytics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get inventory analytics
     */
    @GetMapping("/analytics/inventory")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<InventoryAnalytics>> getInventoryAnalytics() {
        try {
            // Get inventory data
            var lowStockItems = inventoryService.getLowStockInventory();
            var outOfStockItems = inventoryService.getOutOfStockInventory();
            var inventorySummary = inventoryService.getInventorySummaryByWarehouse();
            BigDecimal totalInventoryValue = inventoryService.calculateTotalInventoryValue();
            
            InventoryAnalytics analytics = InventoryAnalytics.builder()
                    .lowStockItems(lowStockItems)
                    .outOfStockItems(outOfStockItems)
                    .inventorySummary(inventorySummary)
                    .totalInventoryValue(totalInventoryValue)
                    .lowStockCount(lowStockItems.size())
                    .outOfStockCount(outOfStockItems.size())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Inventory analytics retrieved", analytics));
            
        } catch (Exception e) {
            log.error("Failed to get inventory analytics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get product analytics
     */
    @GetMapping("/analytics/products")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<ProductAnalytics>> getProductAnalytics() {
        try {
            // Get product data
            var topSellingProducts = productService.getTopSellingProducts(10);
            var lowStockProducts = productService.getLowStockProducts();
            ProductService.ProductStats productStats = productService.getProductStats();
            
            ProductAnalytics analytics = ProductAnalytics.builder()
                    .topSellingProducts(topSellingProducts)
                    .lowStockProducts(lowStockProducts)
                    .productStats(productStats)
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Product analytics retrieved", analytics));
            
        } catch (Exception e) {
            log.error("Failed to get product analytics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get quick stats for widgets
     */
    @GetMapping("/quick-stats")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getQuickStats(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Map<String, Object> quickStats = new HashMap<>();
            
            // Basic counts
            quickStats.put("totalProducts", productService.getProductStats().getTotalProducts());
            quickStats.put("totalSales", salesService.getSalesStats().getTotalSales());
            quickStats.put("todaySales", salesService.getSalesStats().getTodaySales());
            quickStats.put("unreadNotifications", notificationService.getUnreadNotificationCount(userPrincipal.getId()));
            quickStats.put("lowStockItems", inventoryService.getLowStockInventory().size());
            quickStats.put("outOfStockItems", inventoryService.getOutOfStockInventory().size());
            
            // Financial data
            LocalDate today = LocalDate.now();
            LocalDate monthStart = today.withDayOfMonth(1);
            quickStats.put("monthlyRevenue", salesService.calculateTotalRevenue(monthStart, today));
            quickStats.put("monthlyProfit", financialService.calculateNetProfit(monthStart, today));
            quickStats.put("totalInventoryValue", inventoryService.calculateTotalInventoryValue());
            
            return ResponseEntity.ok(ApiResponse.success("Quick stats retrieved", quickStats));
            
        } catch (Exception e) {
            log.error("Failed to get quick stats: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get recent activities
     */
    @GetMapping("/recent-activities")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRecentActivities(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Map<String, Object> recentActivities = new HashMap<>();
            
            // Recent notifications
            var recentNotifications = notificationService.getRecentNotifications(userPrincipal.getId(), 24);
            recentActivities.put("recentNotifications", recentNotifications);
            
            // Recent sales (if user has permission)
            if (userPrincipal.getAuthorities().stream()
                    .anyMatch(auth -> auth.getAuthority().contains("MANAGER") || auth.getAuthority().contains("ADMIN"))) {
                // This would need to be implemented in SalesService
                // var recentSales = salesService.getRecentSales(10);
                // recentActivities.put("recentSales", recentSales);
            }
            
            return ResponseEntity.ok(ApiResponse.success("Recent activities retrieved", recentActivities));
            
        } catch (Exception e) {
            log.error("Failed to get recent activities: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // Dashboard DTOs
    @lombok.Data
    @lombok.Builder
    public static class DashboardOverview {
        private UserService.UserStats userStats;
        private ProductService.ProductStats productStats;
        private SalesService.SalesStats salesStats;
        private FinancialService.FinancialStats financialStats;
        private CategoryService.CategoryStats categoryStats;
        private NotificationService.NotificationStats notificationStats;
        private QRCodeService.QRCodeStats qrCodeStats;
        private BigDecimal totalInventoryValue;
        private long unreadNotifications;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class SalesAnalytics {
        private java.util.List<com.kaydkapro.repository.SalesRepository.DailySalesStats> dailySalesStats;
        private java.util.List<com.kaydkapro.repository.SalesRepository.TopCustomerStats> topCustomers;
        private BigDecimal totalRevenue;
        private SalesService.SalesStats salesStats;
        private int periodDays;
        private LocalDate startDate;
        private LocalDate endDate;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class FinancialAnalytics {
        private FinancialService.FinancialSummary financialSummary;
        private java.util.List<com.kaydkapro.repository.IncomeRepository.IncomeCategoryStats> incomeCategoryStats;
        private java.util.List<com.kaydkapro.repository.ExpenseRepository.ExpenseCategoryStats> expenseCategoryStats;
        private java.util.List<com.kaydkapro.repository.IncomeRepository.DailyIncomeStats> dailyIncomeStats;
        private java.util.List<com.kaydkapro.repository.ExpenseRepository.DailyExpenseStats> dailyExpenseStats;
        private int periodDays;
        private LocalDate startDate;
        private LocalDate endDate;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class InventoryAnalytics {
        private java.util.List<com.kaydkapro.entity.Inventory> lowStockItems;
        private java.util.List<com.kaydkapro.entity.Inventory> outOfStockItems;
        private java.util.List<com.kaydkapro.repository.InventoryRepository.InventorySummary> inventorySummary;
        private BigDecimal totalInventoryValue;
        private int lowStockCount;
        private int outOfStockCount;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class ProductAnalytics {
        private java.util.List<com.kaydkapro.entity.Product> topSellingProducts;
        private java.util.List<com.kaydkapro.entity.Product> lowStockProducts;
        private ProductService.ProductStats productStats;
    }
}
