package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Category;
import lombok.Data;
import jakarta.validation.constraints.*;

/**
 * Category Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class CategoryRequest {
    
    @NotBlank(message = "Category name is required")
    @Size(max = 100, message = "Category name must not exceed 100 characters")
    private String name;
    
    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;
    
    private String parentId;
    
    @Size(max = 500, message = "Image URL must not exceed 500 characters")
    private String imageUrl;
    
    private Integer sortOrder;
    
    /**
     * Convert DTO to Entity
     */
    public Category toEntity() {
        return Category.builder()
                .name(this.name)
                .description(this.description)
                .parentId(this.parentId)
                .imageUrl(this.imageUrl)
                .sortOrder(this.sortOrder)
                .build();
    }
}
