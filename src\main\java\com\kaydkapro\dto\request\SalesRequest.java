package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Sales;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * Sales Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class SalesRequest {
    
    @NotBlank(message = "Sale number is required")
    @Size(max = 50, message = "Sale number must not exceed 50 characters")
    private String saleNumber;
    
    @NotNull(message = "Sale date is required")
    private LocalDate saleDate;
    
    @NotNull(message = "Sale time is required")
    private LocalTime saleTime;
    
    private CustomerInfo customer;
    
    @NotNull(message = "Warehouse information is required")
    private WarehouseInfo warehouse;
    
    @NotEmpty(message = "Sale must have at least one item")
    private List<SaleItemInfo> items;
    
    @NotNull(message = "Totals information is required")
    private TotalsInfo totals;
    
    @NotNull(message = "Payment information is required")
    private PaymentInfo payment;
    
    private String notes;
    
    @Data
    public static class CustomerInfo {
        private String customerId;
        private String customerName;
        private String customerEmail;
        private String customerPhone;
    }
    
    @Data
    public static class WarehouseInfo {
        @NotBlank(message = "Warehouse ID is required")
        private String warehouseId;
        
        @NotBlank(message = "Warehouse name is required")
        private String warehouseName;
        
        private String warehouseCode;
    }
    
    @Data
    public static class SaleItemInfo {
        @NotNull(message = "Product information is required")
        private ProductInfo product;
        
        @NotNull(message = "Quantity is required")
        @DecimalMin(value = "0.01", message = "Quantity must be greater than 0")
        private BigDecimal quantity;
        
        @NotNull(message = "Unit price is required")
        @DecimalMin(value = "0.0", message = "Unit price must be non-negative")
        private BigDecimal unitPrice;
        
        @DecimalMin(value = "0.0", message = "Discount must be non-negative")
        @DecimalMax(value = "100.0", message = "Discount cannot exceed 100%")
        private BigDecimal discount = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Tax rate must be non-negative")
        private BigDecimal taxRate = BigDecimal.ZERO;
        
        private String notes;
        
        @Data
        public static class ProductInfo {
            @NotBlank(message = "Product ID is required")
            private String productId;
            
            @NotBlank(message = "Product name is required")
            private String productName;
            
            @NotBlank(message = "SKU is required")
            private String sku;
        }
    }
    
    @Data
    public static class TotalsInfo {
        @NotNull(message = "Subtotal is required")
        @DecimalMin(value = "0.0", message = "Subtotal must be non-negative")
        private BigDecimal subtotal;
        
        @DecimalMin(value = "0.0", message = "Tax amount must be non-negative")
        private BigDecimal taxAmount = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Discount amount must be non-negative")
        private BigDecimal discountAmount = BigDecimal.ZERO;
        
        @NotNull(message = "Total amount is required")
        @DecimalMin(value = "0.01", message = "Total amount must be greater than 0")
        private BigDecimal totalAmount;
    }
    
    @Data
    public static class PaymentInfo {
        @NotBlank(message = "Payment method is required")
        private String paymentMethod;
        
        @NotBlank(message = "Payment status is required")
        private String paymentStatus;
        
        @NotNull(message = "Paid amount is required")
        @DecimalMin(value = "0.0", message = "Paid amount must be non-negative")
        private BigDecimal paidAmount;
        
        @DecimalMin(value = "0.0", message = "Change amount must be non-negative")
        private BigDecimal changeAmount = BigDecimal.ZERO;
        
        private String paymentReference;
    }
    
    /**
     * Convert DTO to Entity
     */
    public Sales toEntity() {
        Sales sales = Sales.builder()
                .saleNumber(this.saleNumber)
                .notes(this.notes)
                .build();
        
        // Set sale date and time
        sales.setSaleDateTime(Sales.SaleDateTime.builder()
                .date(this.saleDate)
                .time(this.saleTime)
                .build());
        
        // Set customer if provided
        if (this.customer != null) {
            sales.setCustomer(Sales.SaleCustomer.builder()
                    .customerId(this.customer.getCustomerId())
                    .customerName(this.customer.getCustomerName())
                    .customerEmail(this.customer.getCustomerEmail())
                    .customerPhone(this.customer.getCustomerPhone())
                    .build());
        }
        
        // Set warehouse
        sales.setWarehouse(Sales.SaleWarehouse.builder()
                .warehouseId(this.warehouse.getWarehouseId())
                .warehouseName(this.warehouse.getWarehouseName())
                .warehouseCode(this.warehouse.getWarehouseCode())
                .build());
        
        // Set items
        List<Sales.SaleItem> saleItems = this.items.stream()
                .map(item -> Sales.SaleItem.builder()
                        .product(Sales.SaleProduct.builder()
                                .productId(item.getProduct().getProductId())
                                .productName(item.getProduct().getProductName())
                                .sku(item.getProduct().getSku())
                                .build())
                        .quantity(item.getQuantity())
                        .unitPrice(item.getUnitPrice())
                        .discount(item.getDiscount())
                        .taxRate(item.getTaxRate())
                        .notes(item.getNotes())
                        .build())
                .toList();
        sales.setItems(saleItems);
        
        // Set totals
        sales.setTotals(Sales.SaleTotals.builder()
                .subtotal(this.totals.getSubtotal())
                .taxAmount(this.totals.getTaxAmount())
                .discountAmount(this.totals.getDiscountAmount())
                .totalAmount(this.totals.getTotalAmount())
                .build());
        
        // Set payment
        sales.setPayment(Sales.SalePayment.builder()
                .paymentMethod(this.payment.getPaymentMethod())
                .paymentStatus(this.payment.getPaymentStatus())
                .paidAmount(this.payment.getPaidAmount())
                .changeAmount(this.payment.getChangeAmount())
                .paymentReference(this.payment.getPaymentReference())
                .build());
        
        return sales;
    }
}
