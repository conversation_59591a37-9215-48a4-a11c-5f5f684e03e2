package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Purchase;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Purchase Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class PurchaseRequest {
    
    @NotBlank(message = "Order number is required")
    @Size(max = 50, message = "Order number must not exceed 50 characters")
    private String orderNumber;
    
    @NotNull(message = "Order date is required")
    private LocalDate orderDate;
    
    private LocalDate expectedDeliveryDate;
    
    @NotNull(message = "Supplier information is required")
    private SupplierInfo supplier;
    
    @NotNull(message = "Warehouse information is required")
    private WarehouseInfo warehouse;
    
    @NotEmpty(message = "Purchase must have at least one item")
    private List<PurchaseItemInfo> items;
    
    @NotNull(message = "Totals information is required")
    private TotalsInfo totals;
    
    private PaymentInfo payment;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;
    
    @Data
    public static class SupplierInfo {
        @NotBlank(message = "Supplier ID is required")
        private String supplierId;
        
        @NotBlank(message = "Supplier name is required")
        private String supplierName;
        
        private String supplierCode;
        
        private String contactPerson;
        
        private String contactPhone;
        
        private String contactEmail;
    }
    
    @Data
    public static class WarehouseInfo {
        @NotBlank(message = "Warehouse ID is required")
        private String warehouseId;
        
        @NotBlank(message = "Warehouse name is required")
        private String warehouseName;
        
        private String warehouseCode;
    }
    
    @Data
    public static class PurchaseItemInfo {
        @NotNull(message = "Product information is required")
        private ProductInfo product;
        
        @NotNull(message = "Quantity is required")
        @DecimalMin(value = "0.01", message = "Quantity must be greater than 0")
        private BigDecimal orderedQuantity;
        
        @NotNull(message = "Unit cost is required")
        @DecimalMin(value = "0.0", message = "Unit cost must be non-negative")
        private BigDecimal unitCost;
        
        @DecimalMin(value = "0.0", message = "Discount must be non-negative")
        @DecimalMax(value = "100.0", message = "Discount cannot exceed 100%")
        private BigDecimal discount = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Tax rate must be non-negative")
        private BigDecimal taxRate = BigDecimal.ZERO;
        
        private String notes;
        
        @Data
        public static class ProductInfo {
            @NotBlank(message = "Product ID is required")
            private String productId;
            
            @NotBlank(message = "Product name is required")
            private String productName;
            
            @NotBlank(message = "SKU is required")
            private String sku;
            
            private String unit;
        }
    }
    
    @Data
    public static class TotalsInfo {
        @NotNull(message = "Subtotal is required")
        @DecimalMin(value = "0.0", message = "Subtotal must be non-negative")
        private BigDecimal subtotal;
        
        @DecimalMin(value = "0.0", message = "Tax amount must be non-negative")
        private BigDecimal taxAmount = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Discount amount must be non-negative")
        private BigDecimal discountAmount = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Shipping cost must be non-negative")
        private BigDecimal shippingCost = BigDecimal.ZERO;
        
        @NotNull(message = "Total amount is required")
        @DecimalMin(value = "0.01", message = "Total amount must be greater than 0")
        private BigDecimal totalAmount;
    }
    
    @Data
    public static class PaymentInfo {
        @NotBlank(message = "Payment terms are required")
        private String paymentTerms;
        
        @Min(value = 0, message = "Payment days must be non-negative")
        private Integer paymentDays = 30;
        
        private LocalDate dueDate;
        
        @Size(max = 20, message = "Currency must not exceed 20 characters")
        private String currency = "USD";
        
        @Size(max = 100, message = "Payment method must not exceed 100 characters")
        private String paymentMethod;
        
        private String paymentReference;
    }
    
    /**
     * Convert DTO to Entity
     */
    public Purchase toEntity() {
        Purchase purchase = Purchase.builder()
                .orderNumber(this.orderNumber)
                .orderDate(this.orderDate)
                .expectedDeliveryDate(this.expectedDeliveryDate)
                .notes(this.notes)
                .build();
        
        // Set supplier
        purchase.setSupplier(Purchase.PurchaseSupplier.builder()
                .supplierId(this.supplier.getSupplierId())
                .supplierName(this.supplier.getSupplierName())
                .supplierCode(this.supplier.getSupplierCode())
                .contactPerson(this.supplier.getContactPerson())
                .contactPhone(this.supplier.getContactPhone())
                .contactEmail(this.supplier.getContactEmail())
                .build());
        
        // Set warehouse
        purchase.setWarehouse(Purchase.PurchaseWarehouse.builder()
                .warehouseId(this.warehouse.getWarehouseId())
                .warehouseName(this.warehouse.getWarehouseName())
                .warehouseCode(this.warehouse.getWarehouseCode())
                .build());
        
        // Set items
        List<Purchase.PurchaseItem> purchaseItems = this.items.stream()
                .map(item -> Purchase.PurchaseItem.builder()
                        .product(Purchase.PurchaseProduct.builder()
                                .productId(item.getProduct().getProductId())
                                .productName(item.getProduct().getProductName())
                                .sku(item.getProduct().getSku())
                                .unit(item.getProduct().getUnit())
                                .build())
                        .orderedQuantity(item.getOrderedQuantity())
                        .unitCost(item.getUnitCost())
                        .discount(item.getDiscount())
                        .taxRate(item.getTaxRate())
                        .notes(item.getNotes())
                        .build())
                .toList();
        purchase.setItems(purchaseItems);
        
        // Set totals
        purchase.setTotals(Purchase.PurchaseTotals.builder()
                .subtotal(this.totals.getSubtotal())
                .taxAmount(this.totals.getTaxAmount())
                .discountAmount(this.totals.getDiscountAmount())
                .shippingCost(this.totals.getShippingCost())
                .totalAmount(this.totals.getTotalAmount())
                .build());
        
        // Set payment
        if (this.payment != null) {
            purchase.setPayment(Purchase.PurchasePayment.builder()
                    .paymentTerms(this.payment.getPaymentTerms())
                    .paymentDays(this.payment.getPaymentDays())
                    .dueDate(this.payment.getDueDate())
                    .currency(this.payment.getCurrency())
                    .paymentMethod(this.payment.getPaymentMethod())
                    .paymentReference(this.payment.getPaymentReference())
                    .build());
        }
        
        return purchase;
    }
}
