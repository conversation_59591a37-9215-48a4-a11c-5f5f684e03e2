package com.kaydkapro.controller;

import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.service.*;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Report Controller - REST API endpoints for generating various business reports
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/reports")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class ReportController {
    
    private final SalesService salesService;
    private final FinancialService financialService;
    private final InventoryService inventoryService;
    private final ProductService productService;
    private final CustomerService customerService;
    private final SupplierService supplierService;
    private final PurchaseService purchaseService;
    
    /**
     * Generate sales report
     */
    @GetMapping("/sales")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SalesReport>> generateSalesReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "false") boolean includeDetails) {
        try {
            // Get sales data
            var dailySalesStats = salesService.getDailySalesStats(startDate, endDate);
            var topCustomers = salesService.getTopCustomers(10);
            BigDecimal totalRevenue = salesService.calculateTotalRevenue(startDate, endDate);
            var salesStats = salesService.getSalesStats();
            
            // Get top selling products if details requested
            var topProducts = includeDetails ? productService.getTopSellingProducts(10) : null;
            
            SalesReport report = SalesReport.builder()
                    .reportPeriod(new ReportPeriod(startDate, endDate))
                    .totalRevenue(totalRevenue)
                    .dailySalesStats(dailySalesStats)
                    .topCustomers(topCustomers)
                    .topProducts(topProducts)
                    .salesStats(salesStats)
                    .generatedAt(java.time.LocalDateTime.now())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Sales report generated successfully", report));
            
        } catch (Exception e) {
            log.error("Failed to generate sales report: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate financial report
     */
    @GetMapping("/financial")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<FinancialReport>> generateFinancialReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            // Get financial data
            var financialSummary = financialService.getFinancialSummary(startDate, endDate);
            var incomeCategoryStats = financialService.getIncomeByCategoryStats(startDate, endDate);
            var expenseCategoryStats = financialService.getExpensesByCategoryStats(startDate, endDate);
            BigDecimal totalIncome = financialService.calculateTotalIncome(startDate, endDate);
            BigDecimal totalExpenses = financialService.calculateTotalExpenses(startDate, endDate);
            BigDecimal netProfit = financialService.calculateNetProfit(startDate, endDate);
            
            FinancialReport report = FinancialReport.builder()
                    .reportPeriod(new ReportPeriod(startDate, endDate))
                    .financialSummary(financialSummary)
                    .totalIncome(totalIncome)
                    .totalExpenses(totalExpenses)
                    .netProfit(netProfit)
                    .incomeCategoryStats(incomeCategoryStats)
                    .expenseCategoryStats(expenseCategoryStats)
                    .generatedAt(java.time.LocalDateTime.now())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Financial report generated successfully", report));
            
        } catch (Exception e) {
            log.error("Failed to generate financial report: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate inventory report
     */
    @GetMapping("/inventory")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<InventoryReport>> generateInventoryReport() {
        try {
            // Get inventory data
            var lowStockItems = inventoryService.getLowStockInventory();
            var outOfStockItems = inventoryService.getOutOfStockInventory();
            var inventorySummary = inventoryService.getInventorySummaryByWarehouse();
            BigDecimal totalInventoryValue = inventoryService.calculateTotalInventoryValue();
            var productStats = productService.getProductStats();
            
            InventoryReport report = InventoryReport.builder()
                    .totalInventoryValue(totalInventoryValue)
                    .lowStockItems(lowStockItems)
                    .outOfStockItems(outOfStockItems)
                    .inventorySummary(inventorySummary)
                    .productStats(productStats)
                    .lowStockCount(lowStockItems.size())
                    .outOfStockCount(outOfStockItems.size())
                    .generatedAt(java.time.LocalDateTime.now())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Inventory report generated successfully", report));
            
        } catch (Exception e) {
            log.error("Failed to generate inventory report: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate customer report
     */
    @GetMapping("/customers")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<CustomerReport>> generateCustomerReport() {
        try {
            // Get customer data
            var topCustomers = customerService.getTopCustomers(20);
            var vipCustomers = customerService.getVipCustomers();
            var customerStats = customerService.getCustomerStats();
            
            CustomerReport report = CustomerReport.builder()
                    .topCustomers(topCustomers)
                    .vipCustomers(vipCustomers)
                    .customerStats(customerStats)
                    .generatedAt(java.time.LocalDateTime.now())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Customer report generated successfully", report));
            
        } catch (Exception e) {
            log.error("Failed to generate customer report: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate supplier report
     */
    @GetMapping("/suppliers")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SupplierReport>> generateSupplierReport() {
        try {
            // Get supplier data
            var topSuppliers = supplierService.getTopPerformingSuppliers(20);
            var preferredSuppliers = supplierService.getPreferredSuppliers();
            var supplierStats = supplierService.getSupplierStats();
            
            SupplierReport report = SupplierReport.builder()
                    .topSuppliers(topSuppliers)
                    .preferredSuppliers(preferredSuppliers)
                    .supplierStats(supplierStats)
                    .generatedAt(java.time.LocalDateTime.now())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Supplier report generated successfully", report));
            
        } catch (Exception e) {
            log.error("Failed to generate supplier report: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate purchase report
     */
    @GetMapping("/purchases")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<PurchaseReport>> generatePurchaseReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            // Get purchase data
            BigDecimal totalPurchaseAmount = purchaseService.calculateTotalPurchaseAmount(startDate, endDate);
            var purchaseStats = purchaseService.getPurchaseStats();
            var pendingPurchases = purchaseService.getPendingPurchases();
            var overduePurchases = purchaseService.getOverduePurchases();
            
            PurchaseReport report = PurchaseReport.builder()
                    .reportPeriod(new ReportPeriod(startDate, endDate))
                    .totalPurchaseAmount(totalPurchaseAmount)
                    .purchaseStats(purchaseStats)
                    .pendingPurchases(pendingPurchases)
                    .overduePurchases(overduePurchases)
                    .generatedAt(java.time.LocalDateTime.now())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Purchase report generated successfully", report));
            
        } catch (Exception e) {
            log.error("Failed to generate purchase report: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate comprehensive business report
     */
    @GetMapping("/business-overview")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BusinessOverviewReport>> generateBusinessOverviewReport(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            // Get comprehensive business data
            BigDecimal totalRevenue = salesService.calculateTotalRevenue(startDate, endDate);
            BigDecimal totalExpenses = financialService.calculateTotalExpenses(startDate, endDate);
            BigDecimal netProfit = financialService.calculateNetProfit(startDate, endDate);
            BigDecimal totalInventoryValue = inventoryService.calculateTotalInventoryValue();
            BigDecimal totalPurchases = purchaseService.calculateTotalPurchaseAmount(startDate, endDate);
            
            var salesStats = salesService.getSalesStats();
            var customerStats = customerService.getCustomerStats();
            var productStats = productService.getProductStats();
            var supplierStats = supplierService.getSupplierStats();
            
            BusinessOverviewReport report = BusinessOverviewReport.builder()
                    .reportPeriod(new ReportPeriod(startDate, endDate))
                    .totalRevenue(totalRevenue)
                    .totalExpenses(totalExpenses)
                    .netProfit(netProfit)
                    .totalInventoryValue(totalInventoryValue)
                    .totalPurchases(totalPurchases)
                    .salesStats(salesStats)
                    .customerStats(customerStats)
                    .productStats(productStats)
                    .supplierStats(supplierStats)
                    .generatedAt(java.time.LocalDateTime.now())
                    .build();
            
            return ResponseEntity.ok(ApiResponse.success("Business overview report generated successfully", report));
            
        } catch (Exception e) {
            log.error("Failed to generate business overview report: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get available report types
     */
    @GetMapping("/types")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<ReportType>>> getReportTypes() {
        try {
            List<ReportType> reportTypes = List.of(
                new ReportType("sales", "Sales Report", "Comprehensive sales analysis and statistics"),
                new ReportType("financial", "Financial Report", "Income, expenses, and profit analysis"),
                new ReportType("inventory", "Inventory Report", "Stock levels, valuation, and alerts"),
                new ReportType("customers", "Customer Report", "Customer analysis and statistics"),
                new ReportType("suppliers", "Supplier Report", "Supplier performance and statistics"),
                new ReportType("purchases", "Purchase Report", "Purchase orders and supplier analysis"),
                new ReportType("business-overview", "Business Overview", "Comprehensive business performance report")
            );
            
            return ResponseEntity.ok(ApiResponse.success("Report types retrieved successfully", reportTypes));
            
        } catch (Exception e) {
            log.error("Failed to get report types: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // Report DTOs
    @lombok.Data
    @lombok.Builder
    public static class SalesReport {
        private ReportPeriod reportPeriod;
        private BigDecimal totalRevenue;
        private List<com.kaydkapro.repository.SalesRepository.DailySalesStats> dailySalesStats;
        private List<com.kaydkapro.repository.SalesRepository.TopCustomerStats> topCustomers;
        private List<com.kaydkapro.entity.Product> topProducts;
        private SalesService.SalesStats salesStats;
        private java.time.LocalDateTime generatedAt;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class FinancialReport {
        private ReportPeriod reportPeriod;
        private FinancialService.FinancialSummary financialSummary;
        private BigDecimal totalIncome;
        private BigDecimal totalExpenses;
        private BigDecimal netProfit;
        private List<com.kaydkapro.repository.IncomeRepository.IncomeCategoryStats> incomeCategoryStats;
        private List<com.kaydkapro.repository.ExpenseRepository.ExpenseCategoryStats> expenseCategoryStats;
        private java.time.LocalDateTime generatedAt;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class InventoryReport {
        private BigDecimal totalInventoryValue;
        private List<com.kaydkapro.entity.Inventory> lowStockItems;
        private List<com.kaydkapro.entity.Inventory> outOfStockItems;
        private List<com.kaydkapro.repository.InventoryRepository.InventorySummary> inventorySummary;
        private ProductService.ProductStats productStats;
        private int lowStockCount;
        private int outOfStockCount;
        private java.time.LocalDateTime generatedAt;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class CustomerReport {
        private List<com.kaydkapro.entity.Customer> topCustomers;
        private List<com.kaydkapro.entity.Customer> vipCustomers;
        private CustomerService.CustomerStats customerStats;
        private java.time.LocalDateTime generatedAt;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class SupplierReport {
        private List<com.kaydkapro.entity.Supplier> topSuppliers;
        private List<com.kaydkapro.entity.Supplier> preferredSuppliers;
        private SupplierService.SupplierStats supplierStats;
        private java.time.LocalDateTime generatedAt;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class PurchaseReport {
        private ReportPeriod reportPeriod;
        private BigDecimal totalPurchaseAmount;
        private PurchaseService.PurchaseStats purchaseStats;
        private List<com.kaydkapro.entity.Purchase> pendingPurchases;
        private List<com.kaydkapro.entity.Purchase> overduePurchases;
        private java.time.LocalDateTime generatedAt;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class BusinessOverviewReport {
        private ReportPeriod reportPeriod;
        private BigDecimal totalRevenue;
        private BigDecimal totalExpenses;
        private BigDecimal netProfit;
        private BigDecimal totalInventoryValue;
        private BigDecimal totalPurchases;
        private SalesService.SalesStats salesStats;
        private CustomerService.CustomerStats customerStats;
        private ProductService.ProductStats productStats;
        private SupplierService.SupplierStats supplierStats;
        private java.time.LocalDateTime generatedAt;
    }
    
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class ReportPeriod {
        private LocalDate startDate;
        private LocalDate endDate;
    }
    
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class ReportType {
        private String code;
        private String name;
        private String description;
    }
}
