package com.kaydkapro.dto.request;

import lombok.Data;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * Stock Transfer Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class StockTransferRequest {
    
    @NotBlank(message = "Product ID is required")
    private String productId;
    
    @NotBlank(message = "From warehouse ID is required")
    private String fromWarehouseId;
    
    @NotBlank(message = "To warehouse ID is required")
    private String toWarehouseId;
    
    @NotNull(message = "Quantity is required")
    @DecimalMin(value = "0.01", message = "Quantity must be greater than 0")
    private BigDecimal quantity;
    
    private String notes;
}
