package com.kaydkapro.dto.request;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * QR Code Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class QRCodeRequest {
    
    @NotBlank(message = "QR code type is required")
    @Size(max = 50, message = "Type must not exceed 50 characters")
    private String type;
    
    @NotBlank(message = "QR code data is required")
    @Size(max = 1000, message = "Data must not exceed 1000 characters")
    private String data;
    
    @Size(max = 200, message = "Title must not exceed 200 characters")
    private String title;
    
    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;
}
