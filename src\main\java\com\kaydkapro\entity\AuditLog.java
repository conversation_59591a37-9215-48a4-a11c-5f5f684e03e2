package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * AuditLog Entity - Tracks all system operations for auditing purposes
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "auditLogs")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuditLog {
    
    @Id
    private String id;
    
    @NotNull(message = "User information is required")
    private AuditUser user;
    
    @NotBlank(message = "Action is required")
    @Size(max = 100, message = "Action must not exceed 100 characters")
    private String action;
    
    @NotNull(message = "Entity information is required")
    private AuditEntity entity;
    
    private AuditChanges changes;
    
    private AuditMetadata metadata;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditUser {
        private String userId;
        private String userName;
        private String userRole;
    }
    
    /**
     * Nested class for entity information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditEntity {
        @NotBlank(message = "Entity type is required")
        private String type;
        
        private String entityId;
        private String entityName;
    }
    
    /**
     * Nested class for tracking changes
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditChanges {
        private Map<String, Object> oldValues;
        private Map<String, Object> newValues;
        
        public void addOldValue(String field, Object value) {
            if (oldValues == null) {
                oldValues = new HashMap<>();
            }
            oldValues.put(field, value);
        }
        
        public void addNewValue(String field, Object value) {
            if (newValues == null) {
                newValues = new HashMap<>();
            }
            newValues.put(field, value);
        }
        
        public boolean hasChanges() {
            return (oldValues != null && !oldValues.isEmpty()) || 
                   (newValues != null && !newValues.isEmpty());
        }
    }
    
    /**
     * Nested class for additional metadata
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditMetadata {
        private String ipAddress;
        private String userAgent;
        private String sessionId;
        private String requestId;
        
        @Builder.Default
        private Map<String, Object> additionalData = new HashMap<>();
        
        public void addData(String key, Object value) {
            additionalData.put(key, value);
        }
        
        public Object getData(String key) {
            return additionalData.get(key);
        }
    }
    
    // Action constants
    public static final String ACTION_CREATE = "CREATE";
    public static final String ACTION_UPDATE = "UPDATE";
    public static final String ACTION_DELETE = "DELETE";
    public static final String ACTION_LOGIN = "LOGIN";
    public static final String ACTION_LOGOUT = "LOGOUT";
    public static final String ACTION_VIEW = "VIEW";
    public static final String ACTION_EXPORT = "EXPORT";
    public static final String ACTION_IMPORT = "IMPORT";
    public static final String ACTION_APPROVE = "APPROVE";
    public static final String ACTION_REJECT = "REJECT";
    public static final String ACTION_CANCEL = "CANCEL";
    
    // Entity type constants
    public static final String ENTITY_USER = "USER";
    public static final String ENTITY_PRODUCT = "PRODUCT";
    public static final String ENTITY_CATEGORY = "CATEGORY";
    public static final String ENTITY_INVENTORY = "INVENTORY";
    public static final String ENTITY_SALE = "SALE";
    public static final String ENTITY_PURCHASE = "PURCHASE";
    public static final String ENTITY_SUPPLIER = "SUPPLIER";
    public static final String ENTITY_CUSTOMER = "CUSTOMER";
    public static final String ENTITY_WAREHOUSE = "WAREHOUSE";
    public static final String ENTITY_INCOME = "INCOME";
    public static final String ENTITY_EXPENSE = "EXPENSE";
    public static final String ENTITY_QRCODE = "QRCODE";
    public static final String ENTITY_NOTIFICATION = "NOTIFICATION";
    
    // Helper methods
    public boolean isCreateAction() {
        return ACTION_CREATE.equals(action);
    }
    
    public boolean isUpdateAction() {
        return ACTION_UPDATE.equals(action);
    }
    
    public boolean isDeleteAction() {
        return ACTION_DELETE.equals(action);
    }
    
    public boolean isSecurityAction() {
        return ACTION_LOGIN.equals(action) || ACTION_LOGOUT.equals(action);
    }
    
    public String getFormattedAction() {
        return String.format("%s %s", action, entity.getType());
    }
    
    public String getDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(user.getUserName()).append(" ");
        sb.append(action.toLowerCase()).append("d ");
        sb.append(entity.getType().toLowerCase());
        
        if (entity.getEntityName() != null) {
            sb.append(" '").append(entity.getEntityName()).append("'");
        }
        
        return sb.toString();
    }
    
    // Factory methods for common audit logs
    public static AuditLog createUserAction(String userId, String userName, String userRole,
                                          String action, String entityType, String entityId, 
                                          String entityName) {
        return AuditLog.builder()
                .user(AuditUser.builder()
                        .userId(userId)
                        .userName(userName)
                        .userRole(userRole)
                        .build())
                .action(action)
                .entity(AuditEntity.builder()
                        .type(entityType)
                        .entityId(entityId)
                        .entityName(entityName)
                        .build())
                .build();
    }
    
    public static AuditLog createLoginLog(String userId, String userName, String ipAddress, String userAgent) {
        return AuditLog.builder()
                .user(AuditUser.builder()
                        .userId(userId)
                        .userName(userName)
                        .build())
                .action(ACTION_LOGIN)
                .entity(AuditEntity.builder()
                        .type(ENTITY_USER)
                        .entityId(userId)
                        .entityName(userName)
                        .build())
                .metadata(AuditMetadata.builder()
                        .ipAddress(ipAddress)
                        .userAgent(userAgent)
                        .build())
                .build();
    }
    
    public static AuditLog createDataChangeLog(String userId, String userName, String userRole,
                                             String action, String entityType, String entityId, 
                                             String entityName, Map<String, Object> oldValues, 
                                             Map<String, Object> newValues) {
        return AuditLog.builder()
                .user(AuditUser.builder()
                        .userId(userId)
                        .userName(userName)
                        .userRole(userRole)
                        .build())
                .action(action)
                .entity(AuditEntity.builder()
                        .type(entityType)
                        .entityId(entityId)
                        .entityName(entityName)
                        .build())
                .changes(AuditChanges.builder()
                        .oldValues(oldValues)
                        .newValues(newValues)
                        .build())
                .build();
    }
}
