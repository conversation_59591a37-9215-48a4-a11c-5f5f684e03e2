package com.kaydkapro.dto.request;

import com.kaydkapro.entity.User;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.util.List;

/**
 * User Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class UserRequest {
    
    @NotBlank(message = "Username is required")
    @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "Username can only contain letters, numbers, and underscores")
    private String username;
    
    @NotBlank(message = "Email is required")
    @Email(message = "Email should be valid")
    @Size(max = 100, message = "Email must not exceed 100 characters")
    private String email;
    
    @NotBlank(message = "Password is required")
    @Size(min = 6, max = 100, message = "Password must be between 6 and 100 characters")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*$", 
             message = "Password must contain at least one lowercase letter, one uppercase letter, and one digit")
    private String password;
    
    @NotBlank(message = "First name is required")
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    private String firstName;
    
    @NotBlank(message = "Last name is required")
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    private String lastName;
    
    @Size(max = 20, message = "Phone number must not exceed 20 characters")
    @Pattern(regexp = "^[+]?[0-9\\s\\-\\(\\)]*$", message = "Invalid phone number format")
    private String phone;
    
    @Size(max = 500, message = "Profile image URL must not exceed 500 characters")
    private String profileImage;
    
    @NotEmpty(message = "At least one role must be assigned")
    private List<String> roleIds;
    
    private Boolean isActive = true;
    
    /**
     * Convert DTO to Entity
     */
    public User toEntity() {
        return User.builder()
                .username(this.username)
                .email(this.email)
                .password(this.password) // Will be encoded in service
                .firstName(this.firstName)
                .lastName(this.lastName)
                .phone(this.phone)
                .profileImage(this.profileImage)
                .isActive(this.isActive)
                .build();
    }
}
