package com.kaydkapro.service;

import com.kaydkapro.entity.AuditLog;
import com.kaydkapro.repository.AuditLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * AuditLog Service - Business logic for audit logging
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuditLogService {
    
    private final AuditLogRepository auditLogRepository;
    
    /**
     * Log user action
     */
    public void logUserAction(String userId, String action, String entityType, 
                             String entityId, String entityName) {
        try {
            AuditLog auditLog = AuditLog.createUserAction(
                userId, 
                getUserName(userId), 
                getUserRole(userId),
                action, 
                entityType, 
                entityId, 
                entityName
            );
            
            auditLogRepository.save(auditLog);
            log.debug("Audit log created: {} - {} - {}", userId, action, entityType);
        } catch (Exception e) {
            log.error("Failed to create audit log: {}", e.getMessage());
            // Don't throw exception to avoid breaking main operation
        }
    }
    
    /**
     * Log data changes
     */
    public void logDataChange(String userId, String action, String entityType, 
                             String entityId, String entityName,
                             Map<String, Object> oldValues, Map<String, Object> newValues) {
        try {
            AuditLog auditLog = AuditLog.createDataChangeLog(
                userId,
                getUserName(userId),
                getUserRole(userId),
                action,
                entityType,
                entityId,
                entityName,
                oldValues,
                newValues
            );
            
            auditLogRepository.save(auditLog);
            log.debug("Data change audit log created: {} - {} - {}", userId, action, entityType);
        } catch (Exception e) {
            log.error("Failed to create data change audit log: {}", e.getMessage());
        }
    }
    
    /**
     * Log login activity
     */
    public void logLogin(String userId, String ipAddress, String userAgent) {
        try {
            AuditLog auditLog = AuditLog.createLoginLog(
                userId,
                getUserName(userId),
                ipAddress,
                userAgent
            );
            
            auditLogRepository.save(auditLog);
            log.debug("Login audit log created: {}", userId);
        } catch (Exception e) {
            log.error("Failed to create login audit log: {}", e.getMessage());
        }
    }
    
    /**
     * Get audit logs by user ID
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> getAuditLogsByUserId(String userId, Pageable pageable) {
        return auditLogRepository.findByUserId(userId, pageable);
    }
    
    /**
     * Get audit logs by entity type
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> getAuditLogsByEntityType(String entityType, Pageable pageable) {
        return auditLogRepository.findByEntityType(entityType, pageable);
    }
    
    /**
     * Get audit logs by date range
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> getAuditLogsByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return auditLogRepository.findByDateRange(startDate, endDate, pageable);
    }
    
    /**
     * Get security logs (login/logout)
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> getSecurityLogs(Pageable pageable) {
        return auditLogRepository.findSecurityLogs(pageable);
    }
    
    /**
     * Get data modification logs
     */
    @Transactional(readOnly = true)
    public Page<AuditLog> getDataModificationLogs(Pageable pageable) {
        return auditLogRepository.findDataModificationLogs(pageable);
    }
    
    /**
     * Get audit logs for specific entity
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getEntityAuditLogs(String entityType, String entityId) {
        return auditLogRepository.findByEntityTypeAndEntityId(entityType, entityId);
    }
    
    /**
     * Get recent audit logs
     */
    @Transactional(readOnly = true)
    public List<AuditLog> getRecentAuditLogs(int hours) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        return auditLogRepository.findRecentLogs(since);
    }
    
    /**
     * Get audit statistics
     */
    @Transactional(readOnly = true)
    public AuditStats getAuditStats() {
        long totalLogs = auditLogRepository.count();
        long todayLogs = auditLogRepository.countByDateRange(
            LocalDateTime.now().toLocalDate().atStartOfDay(),
            LocalDateTime.now()
        );
        long securityLogs = auditLogRepository.countByAction(AuditLog.ACTION_LOGIN) +
                           auditLogRepository.countByAction(AuditLog.ACTION_LOGOUT);
        long dataModificationLogs = auditLogRepository.countByAction(AuditLog.ACTION_CREATE) +
                                   auditLogRepository.countByAction(AuditLog.ACTION_UPDATE) +
                                   auditLogRepository.countByAction(AuditLog.ACTION_DELETE);
        
        return AuditStats.builder()
                .totalLogs(totalLogs)
                .todayLogs(todayLogs)
                .securityLogs(securityLogs)
                .dataModificationLogs(dataModificationLogs)
                .build();
    }
    
    /**
     * Clean up old audit logs
     */
    public void cleanupOldLogs(int daysToKeep) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
        auditLogRepository.deleteByCreatedAtBefore(cutoffDate);
        log.info("Cleaned up audit logs older than {} days", daysToKeep);
    }
    
    // Helper methods
    private String getUserName(String userId) {
        // This would typically fetch from UserService or cache
        // For now, return a placeholder
        return "User-" + userId;
    }
    
    private String getUserRole(String userId) {
        // This would typically fetch from UserService or cache
        // For now, return a placeholder
        return "USER";
    }
    
    /**
     * Audit statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class AuditStats {
        private long totalLogs;
        private long todayLogs;
        private long securityLogs;
        private long dataModificationLogs;
    }
}
