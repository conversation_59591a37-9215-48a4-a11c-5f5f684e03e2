package com.kaydkapro.repository;

import com.kaydkapro.entity.Customer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Customer Repository - Data access layer for Customer entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface CustomerRepository extends MongoRepository<Customer, String> {
    
    /**
     * Find customer by name
     */
    Optional<Customer> findByName(String name);
    
    /**
     * Find all active customers
     */
    List<Customer> findByIsActiveTrue();
    
    /**
     * Find all active customers with pagination
     */
    Page<Customer> findByIsActiveTrue(Pageable pageable);
    
    /**
     * Find customers by type
     */
    List<Customer> findByCustomerType(String customerType);
    
    /**
     * Find customers by type with pagination
     */
    Page<Customer> findByCustomerType(String customerType, Pageable pageable);
    
    /**
     * Find customers by email
     */
    @Query("{'contact.email': ?0}")
    Optional<Customer> findByEmail(String email);
    
    /**
     * Find customers by phone
     */
    @Query("{'contact.phone': ?0}")
    Optional<Customer> findByPhone(String phone);
    
    /**
     * Find customers by mobile
     */
    @Query("{'contact.mobile': ?0}")
    Optional<Customer> findByMobile(String mobile);
    
    /**
     * Find customers by city
     */
    @Query("{'address.city': ?0, 'isActive': true}")
    List<Customer> findByCity(String city);
    
    /**
     * Find customers by state
     */
    @Query("{'address.state': ?0, 'isActive': true}")
    List<Customer> findByState(String state);
    
    /**
     * Find customers by country
     */
    @Query("{'address.country': ?0, 'isActive': true}")
    List<Customer> findByCountry(String country);
    
    /**
     * Find customers by loyalty tier
     */
    @Query("{'loyaltyProgram.tier': ?0, 'isActive': true}")
    List<Customer> findByLoyaltyTier(String tier);
    
    /**
     * Find VIP customers (Gold and Platinum tiers)
     */
    @Query("{'loyaltyProgram.tier': {'$in': ['GOLD', 'PLATINUM']}, 'isActive': true}")
    List<Customer> findVipCustomers();
    
    /**
     * Find customers with loyalty points above threshold
     */
    @Query("{'loyaltyProgram.points': {'$gte': ?0}, 'isActive': true}")
    List<Customer> findCustomersWithPointsAbove(BigDecimal points);
    
    /**
     * Find business customers
     */
    @Query("{'customerType': 'BUSINESS', 'isActive': true}")
    List<Customer> findBusinessCustomers();
    
    /**
     * Find individual customers
     */
    @Query("{'customerType': 'INDIVIDUAL', 'isActive': true}")
    List<Customer> findIndividualCustomers();
    
    /**
     * Find customers by tax number
     */
    @Query("{'businessInfo.taxNumber': ?0}")
    Optional<Customer> findByTaxNumber(String taxNumber);
    
    /**
     * Search customers by name, email, or phone
     */
    @Query("{'$and': [{'isActive': true}, {'$or': [" +
           "{'name': {'$regex': ?0, '$options': 'i'}}, " +
           "{'contact.email': {'$regex': ?0, '$options': 'i'}}, " +
           "{'contact.phone': {'$regex': ?0, '$options': 'i'}}, " +
           "{'contact.mobile': {'$regex': ?0, '$options': 'i'}}" +
           "]}]}")
    Page<Customer> searchCustomers(String searchTerm, Pageable pageable);
    
    /**
     * Find customers with credit limit
     */
    @Query("{'businessInfo.creditLimit': {'$ne': null}, 'isActive': true}")
    List<Customer> findCustomersWithCreditLimit();
    
    /**
     * Find customers without credit limit
     */
    @Query("{'businessInfo.creditLimit': null, 'isActive': true}")
    List<Customer> findCustomersWithoutCreditLimit();
    
    /**
     * Find customers created by user
     */
    List<Customer> findByCreatedBy(String createdBy);
    
    /**
     * Check if customer name exists
     */
    boolean existsByName(String name);
    
    /**
     * Check if customer name exists excluding current customer
     */
    @Query("{'name': ?0, '_id': {'$ne': ?1}}")
    boolean existsByNameAndIdNot(String name, String id);
    
    /**
     * Check if email exists
     */
    @Query("{'contact.email': ?0}")
    boolean existsByEmail(String email);
    
    /**
     * Check if email exists excluding current customer
     */
    @Query("{'contact.email': ?0, '_id': {'$ne': ?1}}")
    boolean existsByEmailAndIdNot(String email, String id);
    
    /**
     * Check if phone exists
     */
    @Query("{'$or': [{'contact.phone': ?0}, {'contact.mobile': ?0}]}")
    boolean existsByPhone(String phone);
    
    /**
     * Check if tax number exists
     */
    @Query("{'businessInfo.taxNumber': ?0}")
    boolean existsByTaxNumber(String taxNumber);
    
    /**
     * Check if tax number exists excluding current customer
     */
    @Query("{'businessInfo.taxNumber': ?0, '_id': {'$ne': ?1}}")
    boolean existsByTaxNumberAndIdNot(String taxNumber, String id);
    
    /**
     * Count active customers
     */
    long countByIsActiveTrue();
    
    /**
     * Count customers by type
     */
    long countByCustomerType(String customerType);
    
    /**
     * Count customers by loyalty tier
     */
    @Query(value = "{'loyaltyProgram.tier': ?0, 'isActive': true}", count = true)
    long countByLoyaltyTier(String tier);
    
    /**
     * Count VIP customers
     */
    @Query(value = "{'loyaltyProgram.tier': {'$in': ['GOLD', 'PLATINUM']}, 'isActive': true}", count = true)
    long countVipCustomers();
    
    /**
     * Find customers without contact information
     */
    @Query("{'$and': [" +
           "{'isActive': true}, " +
           "{'$or': [" +
           "{'contact.email': {'$in': [null, '']}}, " +
           "{'contact.phone': {'$in': [null, '']}}, " +
           "{'contact.mobile': {'$in': [null, '']}}" +
           "]}" +
           "]}")
    List<Customer> findCustomersWithIncompleteContact();
    
    /**
     * Find customers by industry (business customers only)
     */
    @Query("{'customerType': 'BUSINESS', 'businessInfo.industry': ?0, 'isActive': true}")
    List<Customer> findByIndustry(String industry);
    
    /**
     * Find top customers by loyalty points
     */
    @Query("{'loyaltyProgram.points': {'$ne': null}, 'isActive': true}")
    List<Customer> findTopCustomersByPoints(Pageable pageable);
    
    /**
     * Find customers with recent loyalty activity
     */
    @Query("{'$or': [" +
           "{'loyaltyProgram.lastPointsEarned': {'$gte': ?0}}, " +
           "{'loyaltyProgram.lastPointsRedeemed': {'$gte': ?0}}" +
           "], 'isActive': true}")
    List<Customer> findCustomersWithRecentLoyaltyActivity(java.time.LocalDateTime since);
}
