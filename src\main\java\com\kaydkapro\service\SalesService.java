package com.kaydkapro.service;

import com.kaydkapro.entity.Sales;
import com.kaydkapro.entity.Product;
import com.kaydkapro.repository.SalesRepository;
import com.kaydkapro.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Sales Service - Business logic for sales management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SalesService {
    
    private final SalesRepository salesRepository;
    private final ProductRepository productRepository;
    private final InventoryService inventoryService;
    private final AuditLogService auditLogService;
    private final NotificationService notificationService;
    
    /**
     * Create a new sale
     */
    public Sales createSale(Sales sale, String createdByUserId) {
        log.info("Creating new sale: {}", sale.getSaleNumber());
        
        // Validate sale number uniqueness
        if (salesRepository.existsBySaleNumber(sale.getSaleNumber())) {
            throw new RuntimeException("Sale number already exists: " + sale.getSaleNumber());
        }
        
        // Validate and process sale items
        validateAndProcessSaleItems(sale);
        
        // Calculate totals
        calculateSaleTotals(sale);
        
        // Set audit fields
        sale.setCreatedBy(Sales.SaleUser.builder()
                .userId(createdByUserId)
                .userName(getUserName(createdByUserId))
                .build());
        sale.setCreatedAt(LocalDateTime.now());
        sale.setStatus(Sales.STATUS_PENDING);
        
        Sales savedSale = salesRepository.save(sale);
        
        // Reserve stock for sale items
        if (Sales.STATUS_PENDING.equals(savedSale.getStatus())) {
            reserveStockForSale(savedSale, createdByUserId);
        }
        
        // Log audit
        auditLogService.logUserAction(
            createdByUserId,
            "CREATE",
            "SALE",
            savedSale.getId(),
            savedSale.getSaleNumber()
        );
        
        log.info("Sale created successfully: {}", savedSale.getId());
        return savedSale;
    }
    
    /**
     * Complete a sale
     */
    public Sales completeSale(String saleId, String completedByUserId) {
        log.info("Completing sale: {}", saleId);
        
        Sales sale = getSaleById(saleId);
        
        if (!Sales.STATUS_PENDING.equals(sale.getStatus())) {
            throw new RuntimeException("Only pending sales can be completed");
        }
        
        // Validate payment
        if (!Sales.PAYMENT_PAID.equals(sale.getPayment().getPaymentStatus())) {
            throw new RuntimeException("Sale must be paid before completion");
        }
        
        // Process inventory movements
        processInventoryForCompletedSale(sale, completedByUserId);
        
        // Update sale status
        sale.setStatus(Sales.STATUS_COMPLETED);
        sale.setUpdatedAt(LocalDateTime.now());
        
        Sales savedSale = salesRepository.save(sale);
        
        // Send completion notification
        notificationService.sendSaleCompletedNotification(
            completedByUserId,
            savedSale.getSaleNumber(),
            savedSale.getTotals().getTotalAmount().toString()
        );
        
        // Log audit
        auditLogService.logUserAction(
            completedByUserId,
            "COMPLETE",
            "SALE",
            savedSale.getId(),
            savedSale.getSaleNumber()
        );
        
        log.info("Sale completed successfully: {}", savedSale.getId());
        return savedSale;
    }
    
    /**
     * Cancel a sale
     */
    public Sales cancelSale(String saleId, String reason, String cancelledByUserId) {
        log.info("Cancelling sale: {}", saleId);
        
        Sales sale = getSaleById(saleId);
        
        if (Sales.STATUS_COMPLETED.equals(sale.getStatus())) {
            throw new RuntimeException("Completed sales cannot be cancelled");
        }
        
        // Release reserved stock
        if (Sales.STATUS_PENDING.equals(sale.getStatus())) {
            releaseReservedStockForSale(sale, cancelledByUserId);
        }
        
        // Update sale status
        sale.setStatus(Sales.STATUS_CANCELLED);
        sale.setNotes(sale.getNotes() + "\nCancellation reason: " + reason);
        sale.setUpdatedAt(LocalDateTime.now());
        
        Sales savedSale = salesRepository.save(sale);
        
        // Log audit
        auditLogService.logUserAction(
            cancelledByUserId,
            "CANCEL",
            "SALE",
            savedSale.getId(),
            savedSale.getSaleNumber() + " - Reason: " + reason
        );
        
        log.info("Sale cancelled successfully: {}", savedSale.getId());
        return savedSale;
    }
    
    /**
     * Process return for a sale
     */
    public Sales processSaleReturn(String saleId, List<ReturnItem> returnItems, String returnedByUserId) {
        log.info("Processing return for sale: {}", saleId);
        
        Sales sale = getSaleById(saleId);
        
        if (!Sales.STATUS_COMPLETED.equals(sale.getStatus())) {
            throw new RuntimeException("Only completed sales can be returned");
        }
        
        // Validate return items
        validateReturnItems(sale, returnItems);
        
        // Process inventory returns
        processInventoryForReturn(sale, returnItems, returnedByUserId);
        
        // Update sale status
        sale.setStatus(Sales.STATUS_RETURNED);
        sale.setUpdatedAt(LocalDateTime.now());
        
        Sales savedSale = salesRepository.save(sale);
        
        // Log audit
        auditLogService.logUserAction(
            returnedByUserId,
            "RETURN",
            "SALE",
            savedSale.getId(),
            savedSale.getSaleNumber()
        );
        
        log.info("Sale return processed successfully: {}", savedSale.getId());
        return savedSale;
    }
    
    /**
     * Update payment status
     */
    public Sales updatePaymentStatus(String saleId, String paymentStatus, BigDecimal paidAmount, 
                                   String paymentMethod, String updatedByUserId) {
        log.info("Updating payment status for sale: {}", saleId);
        
        Sales sale = getSaleById(saleId);
        
        // Update payment information
        sale.getPayment().setPaymentStatus(paymentStatus);
        sale.getPayment().setPaidAmount(paidAmount);
        sale.getPayment().setPaymentMethod(paymentMethod);
        
        // Calculate change if overpaid
        if (paidAmount.compareTo(sale.getTotals().getTotalAmount()) > 0) {
            BigDecimal change = paidAmount.subtract(sale.getTotals().getTotalAmount());
            sale.getPayment().setChangeAmount(change);
        }
        
        sale.setUpdatedAt(LocalDateTime.now());
        
        Sales savedSale = salesRepository.save(sale);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            "UPDATE_PAYMENT",
            "SALE",
            savedSale.getId(),
            savedSale.getSaleNumber() + " - Status: " + paymentStatus
        );
        
        log.info("Payment status updated successfully: {}", savedSale.getId());
        return savedSale;
    }
    
    /**
     * Get sale by ID
     */
    @Transactional(readOnly = true)
    public Sales getSaleById(String saleId) {
        return salesRepository.findById(saleId)
                .orElseThrow(() -> new RuntimeException("Sale not found with id: " + saleId));
    }
    
    /**
     * Get sale by sale number
     */
    @Transactional(readOnly = true)
    public Sales getSaleBySaleNumber(String saleNumber) {
        return salesRepository.findBySaleNumber(saleNumber)
                .orElseThrow(() -> new RuntimeException("Sale not found with number: " + saleNumber));
    }
    
    /**
     * Get sales with pagination
     */
    @Transactional(readOnly = true)
    public Page<Sales> getSales(Pageable pageable) {
        return salesRepository.findAll(pageable);
    }
    
    /**
     * Get sales by status
     */
    @Transactional(readOnly = true)
    public Page<Sales> getSalesByStatus(String status, Pageable pageable) {
        return salesRepository.findByStatus(status, pageable);
    }
    
    /**
     * Get sales by customer
     */
    @Transactional(readOnly = true)
    public Page<Sales> getSalesByCustomer(String customerId, Pageable pageable) {
        return salesRepository.findByCustomerId(customerId, pageable);
    }
    
    /**
     * Get sales by date range
     */
    @Transactional(readOnly = true)
    public Page<Sales> getSalesByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        return salesRepository.findByDateRange(startDate, endDate, pageable);
    }
    
    /**
     * Search sales
     */
    @Transactional(readOnly = true)
    public Page<Sales> searchSales(String searchTerm, Pageable pageable) {
        return salesRepository.searchSales(searchTerm, pageable);
    }
    
    /**
     * Get daily sales statistics
     */
    @Transactional(readOnly = true)
    public List<SalesRepository.DailySalesStats> getDailySalesStats(LocalDate startDate, LocalDate endDate) {
        return salesRepository.getDailySalesStats(startDate, endDate);
    }
    
    /**
     * Get top customers
     */
    @Transactional(readOnly = true)
    public List<SalesRepository.TopCustomerStats> getTopCustomers(int limit) {
        return salesRepository.getTopCustomers(limit);
    }
    
    /**
     * Calculate total revenue for period
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateTotalRevenue(LocalDate startDate, LocalDate endDate) {
        return salesRepository.calculateTotalRevenue(startDate, endDate).orElse(BigDecimal.ZERO);
    }
    
    /**
     * Get sales statistics
     */
    @Transactional(readOnly = true)
    public SalesStats getSalesStats() {
        long totalSales = salesRepository.count();
        long completedSales = salesRepository.countByStatus(Sales.STATUS_COMPLETED);
        long pendingSales = salesRepository.countByStatus(Sales.STATUS_PENDING);
        long todaySales = salesRepository.countCompletedSalesToday(LocalDate.now());
        
        return SalesStats.builder()
                .totalSales(totalSales)
                .completedSales(completedSales)
                .pendingSales(pendingSales)
                .cancelledSales(salesRepository.countByStatus(Sales.STATUS_CANCELLED))
                .todaySales(todaySales)
                .build();
    }
    
    // Helper methods
    private void validateAndProcessSaleItems(Sales sale) {
        if (sale.getItems() == null || sale.getItems().isEmpty()) {
            throw new RuntimeException("Sale must have at least one item");
        }
        
        for (Sales.SaleItem item : sale.getItems()) {
            // Validate product exists
            Product product = productRepository.findById(item.getProduct().getProductId())
                    .orElseThrow(() -> new RuntimeException("Product not found: " + item.getProduct().getProductId()));
            
            // Set product information
            item.getProduct().setProductName(product.getName());
            item.getProduct().setSku(product.getSku());
            
            // Set cost price for profit calculation
            item.setCostPrice(product.getPricing().getCostPrice());
            
            // Calculate total price
            item.setTotalPrice(item.calculateTotalPrice());
            
            // Calculate profit
            item.setProfit(item.calculateProfit());
        }
    }
    
    private void calculateSaleTotals(Sales sale) {
        BigDecimal subtotal = sale.getItems().stream()
                .map(Sales.SaleItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        sale.getTotals().setSubtotal(subtotal);
        sale.getTotals().setTotalAmount(sale.getTotals().calculateTotalAmount());
    }
    
    private void reserveStockForSale(Sales sale, String userId) {
        for (Sales.SaleItem item : sale.getItems()) {
            try {
                inventoryService.reserveStock(
                    item.getProduct().getProductId(),
                    sale.getWarehouse().getWarehouseId(),
                    item.getQuantity(),
                    userId
                );
            } catch (Exception e) {
                log.warn("Failed to reserve stock for item {}: {}", item.getProduct().getProductId(), e.getMessage());
                // Could implement partial reservation logic here
            }
        }
    }
    
    private void releaseReservedStockForSale(Sales sale, String userId) {
        for (Sales.SaleItem item : sale.getItems()) {
            try {
                inventoryService.releaseReservedStock(
                    item.getProduct().getProductId(),
                    sale.getWarehouse().getWarehouseId(),
                    item.getQuantity(),
                    userId
                );
            } catch (Exception e) {
                log.warn("Failed to release reserved stock for item {}: {}", item.getProduct().getProductId(), e.getMessage());
            }
        }
    }
    
    private void processInventoryForCompletedSale(Sales sale, String userId) {
        for (Sales.SaleItem item : sale.getItems()) {
            // Remove stock from inventory
            inventoryService.removeStock(
                item.getProduct().getProductId(),
                sale.getWarehouse().getWarehouseId(),
                item.getQuantity(),
                "SALE",
                sale.getId(),
                userId
            );
        }
    }
    
    private void validateReturnItems(Sales sale, List<ReturnItem> returnItems) {
        // Validate return items against original sale items
        for (ReturnItem returnItem : returnItems) {
            boolean found = sale.getItems().stream()
                    .anyMatch(saleItem -> saleItem.getProduct().getProductId().equals(returnItem.getProductId()) &&
                                        saleItem.getQuantity().compareTo(returnItem.getQuantity()) >= 0);
            
            if (!found) {
                throw new RuntimeException("Invalid return item: " + returnItem.getProductId());
            }
        }
    }
    
    private void processInventoryForReturn(Sales sale, List<ReturnItem> returnItems, String userId) {
        for (ReturnItem returnItem : returnItems) {
            // Add stock back to inventory
            Sales.SaleItem originalItem = sale.getItems().stream()
                    .filter(item -> item.getProduct().getProductId().equals(returnItem.getProductId()))
                    .findFirst()
                    .orElseThrow();
            
            inventoryService.addStock(
                returnItem.getProductId(),
                sale.getWarehouse().getWarehouseId(),
                returnItem.getQuantity(),
                originalItem.getCostPrice(),
                "RETURN",
                sale.getId(),
                userId
            );
        }
    }
    
    private String getUserName(String userId) {
        return "User-" + userId;
    }
    
    /**
     * Return item DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class ReturnItem {
        private String productId;
        private BigDecimal quantity;
        private String reason;
    }
    
    /**
     * Sales statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class SalesStats {
        private long totalSales;
        private long completedSales;
        private long pendingSales;
        private long cancelledSales;
        private long todaySales;
    }
}
