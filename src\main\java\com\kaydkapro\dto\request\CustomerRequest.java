package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Customer;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Customer Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class CustomerRequest {
    
    @NotBlank(message = "First name is required")
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    private String firstName;
    
    @NotBlank(message = "Last name is required")
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    private String lastName;
    
    @Email(message = "Email should be valid")
    @Size(max = 100, message = "Email must not exceed 100 characters")
    private String email;
    
    @Size(max = 20, message = "Phone number must not exceed 20 characters")
    @Pattern(regexp = "^[+]?[0-9\\s\\-\\(\\)]*$", message = "Invalid phone number format")
    private String phone;
    
    private LocalDate dateOfBirth;
    
    @NotBlank(message = "Customer type is required")
    @Size(max = 50, message = "Customer type must not exceed 50 characters")
    private String type;
    
    private AddressInfo address;
    
    private BusinessInfo businessInfo;
    
    private PreferencesInfo preferences;
    
    @DecimalMin(value = "0.0", message = "Credit limit must be non-negative")
    private BigDecimal creditLimit = BigDecimal.ZERO;
    
    @Min(value = 0, message = "Loyalty points must be non-negative")
    private Integer loyaltyPoints = 0;
    
    private Boolean isVip = false;
    
    private Boolean isActive = true;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;
    
    @Data
    public static class AddressInfo {
        @Size(max = 500, message = "Street address must not exceed 500 characters")
        private String street;
        
        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;
        
        @Size(max = 100, message = "State must not exceed 100 characters")
        private String state;
        
        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;
        
        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;
    }
    
    @Data
    public static class BusinessInfo {
        @Size(max = 200, message = "Company name must not exceed 200 characters")
        private String companyName;
        
        @Size(max = 50, message = "Tax ID must not exceed 50 characters")
        private String taxId;
        
        @Size(max = 100, message = "Industry must not exceed 100 characters")
        private String industry;
        
        @Size(max = 20, message = "Business phone must not exceed 20 characters")
        private String businessPhone;
        
        @Email(message = "Business email should be valid")
        @Size(max = 100, message = "Business email must not exceed 100 characters")
        private String businessEmail;
        
        @Size(max = 500, message = "Business address must not exceed 500 characters")
        private String businessAddress;
    }
    
    @Data
    public static class PreferencesInfo {
        @Size(max = 50, message = "Preferred language must not exceed 50 characters")
        private String preferredLanguage = "en";
        
        @Size(max = 20, message = "Preferred currency must not exceed 20 characters")
        private String preferredCurrency = "USD";
        
        @Size(max = 50, message = "Communication method must not exceed 50 characters")
        private String communicationMethod = "email";
        
        private Boolean emailNotifications = true;
        
        private Boolean smsNotifications = false;
        
        private Boolean marketingEmails = true;
        
        @Size(max = 100, message = "Preferred payment method must not exceed 100 characters")
        private String preferredPaymentMethod;
    }
    
    /**
     * Convert DTO to Entity
     */
    public Customer toEntity() {
        Customer customer = Customer.builder()
                .firstName(this.firstName)
                .lastName(this.lastName)
                .email(this.email)
                .phone(this.phone)
                .dateOfBirth(this.dateOfBirth)
                .type(this.type)
                .creditLimit(this.creditLimit)
                .loyaltyPoints(this.loyaltyPoints)
                .isVip(this.isVip)
                .isActive(this.isActive)
                .notes(this.notes)
                .build();
        
        // Set address
        if (this.address != null) {
            customer.setAddress(Customer.CustomerAddress.builder()
                    .street(this.address.getStreet())
                    .city(this.address.getCity())
                    .state(this.address.getState())
                    .country(this.address.getCountry())
                    .postalCode(this.address.getPostalCode())
                    .build());
        }
        
        // Set business info
        if (this.businessInfo != null) {
            customer.setBusinessInfo(Customer.CustomerBusinessInfo.builder()
                    .companyName(this.businessInfo.getCompanyName())
                    .taxId(this.businessInfo.getTaxId())
                    .industry(this.businessInfo.getIndustry())
                    .businessPhone(this.businessInfo.getBusinessPhone())
                    .businessEmail(this.businessInfo.getBusinessEmail())
                    .businessAddress(this.businessInfo.getBusinessAddress())
                    .build());
        }
        
        // Set preferences
        if (this.preferences != null) {
            customer.setPreferences(Customer.CustomerPreferences.builder()
                    .preferredLanguage(this.preferences.getPreferredLanguage())
                    .preferredCurrency(this.preferences.getPreferredCurrency())
                    .communicationMethod(this.preferences.getCommunicationMethod())
                    .emailNotifications(this.preferences.getEmailNotifications())
                    .smsNotifications(this.preferences.getSmsNotifications())
                    .marketingEmails(this.preferences.getMarketingEmails())
                    .preferredPaymentMethod(this.preferences.getPreferredPaymentMethod())
                    .build());
        }
        
        return customer;
    }
}
