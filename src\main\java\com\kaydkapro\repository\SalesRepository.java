package com.kaydkapro.repository;

import com.kaydkapro.entity.Sales;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Sales Repository - Data access layer for Sales entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface SalesRepository extends MongoRepository<Sales, String> {
    
    /**
     * Find sale by sale number
     */
    Optional<Sales> findBySaleNumber(String saleNumber);
    
    /**
     * Find sales by status
     */
    List<Sales> findByStatus(String status);
    
    /**
     * Find sales by status with pagination
     */
    Page<Sales> findByStatus(String status, Pageable pageable);
    
    /**
     * Find sales by customer ID
     */
    @Query("{'customer.customerId': ?0}")
    List<Sales> findByCustomerId(String customerId);
    
    /**
     * Find sales by customer ID with pagination
     */
    @Query("{'customer.customerId': ?0}")
    Page<Sales> findByCustomerId(String customerId, Pageable pageable);
    
    /**
     * Find sales by warehouse ID
     */
    @Query("{'warehouse.warehouseId': ?0}")
    List<Sales> findByWarehouseId(String warehouseId);
    
    /**
     * Find sales by date range
     */
    @Query("{'saleDateTime.date': {'$gte': ?0, '$lte': ?1}}")
    List<Sales> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find sales by date range with pagination
     */
    @Query("{'saleDateTime.date': {'$gte': ?0, '$lte': ?1}}")
    Page<Sales> findByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    /**
     * Find completed sales by date range
     */
    @Query("{'status': 'COMPLETED', 'saleDateTime.date': {'$gte': ?0, '$lte': ?1}}")
    List<Sales> findCompletedSalesByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * Find sales by created user
     */
    @Query("{'createdBy.userId': ?0}")
    List<Sales> findByCreatedByUserId(String userId);
    
    /**
     * Find sales by payment method
     */
    @Query("{'payment.paymentMethod': ?0}")
    List<Sales> findByPaymentMethod(String paymentMethod);
    
    /**
     * Find sales by payment status
     */
    @Query("{'payment.paymentStatus': ?0}")
    List<Sales> findByPaymentStatus(String paymentStatus);
    
    /**
     * Find unpaid sales
     */
    @Query("{'payment.paymentStatus': {'$in': ['UNPAID', 'PARTIAL']}}")
    List<Sales> findUnpaidSales();
    
    /**
     * Find sales by amount range
     */
    @Query("{'totals.totalAmount': {'$gte': ?0, '$lte': ?1}}")
    List<Sales> findByAmountRange(BigDecimal minAmount, BigDecimal maxAmount);
    
    /**
     * Search sales by sale number or customer name
     */
    @Query("{'$or': [" +
           "{'saleNumber': {'$regex': ?0, '$options': 'i'}}, " +
           "{'customer.customerName': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    Page<Sales> searchSales(String searchTerm, Pageable pageable);
    
    /**
     * Check if sale number exists
     */
    boolean existsBySaleNumber(String saleNumber);
    
    /**
     * Count sales by status
     */
    long countByStatus(String status);
    
    /**
     * Count completed sales today
     */
    @Query(value = "{'status': 'COMPLETED', 'saleDateTime.date': ?0}", count = true)
    long countCompletedSalesToday(LocalDate today);
    
    /**
     * Get daily sales summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'saleDateTime.date': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': '$saleDateTime.date', " +
        "  'totalSales': { '$sum': 1 }, " +
        "  'totalRevenue': { '$sum': '$totals.totalAmount' }, " +
        "  'totalProfit': { '$sum': { '$sum': '$items.profit' } }, " +
        "  'averageSale': { '$avg': '$totals.totalAmount' }, " +
        "  'totalItems': { '$sum': { '$size': '$items' } } " +
        "} }",
        "{ '$sort': { '_id': 1 } }"
    })
    List<DailySalesStats> getDailySalesStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get monthly sales summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'saleDateTime.date': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': { 'year': { '$year': '$saleDateTime.date' }, 'month': { '$month': '$saleDateTime.date' } }, " +
        "  'totalSales': { '$sum': 1 }, " +
        "  'totalRevenue': { '$sum': '$totals.totalAmount' }, " +
        "  'totalProfit': { '$sum': { '$sum': '$items.profit' } }, " +
        "  'averageSale': { '$avg': '$totals.totalAmount' } " +
        "} }",
        "{ '$sort': { '_id.year': 1, '_id.month': 1 } }"
    })
    List<MonthlySalesStats> getMonthlySalesStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get sales by payment method summary
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'saleDateTime.date': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { " +
        "  '_id': '$payment.paymentMethod', " +
        "  'totalSales': { '$sum': 1 }, " +
        "  'totalAmount': { '$sum': '$totals.totalAmount' }, " +
        "  'percentage': { '$sum': 1 } " +
        "} }",
        "{ '$sort': { 'totalAmount': -1 } }"
    })
    List<PaymentMethodStats> getPaymentMethodStats(LocalDate startDate, LocalDate endDate);
    
    /**
     * Get top customers by sales volume
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'customer.customerId': { '$ne': null } } }",
        "{ '$group': { " +
        "  '_id': '$customer.customerId', " +
        "  'customerName': { '$first': '$customer.customerName' }, " +
        "  'totalSales': { '$sum': 1 }, " +
        "  'totalAmount': { '$sum': '$totals.totalAmount' }, " +
        "  'averageAmount': { '$avg': '$totals.totalAmount' }, " +
        "  'lastSaleDate': { '$max': '$saleDateTime.date' } " +
        "} }",
        "{ '$sort': { 'totalAmount': -1 } }",
        "{ '$limit': ?0 }"
    })
    List<TopCustomerStats> getTopCustomers(int limit);
    
    /**
     * Find recent sales (last N days)
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<Sales> findRecentSales(LocalDateTime since);
    
    /**
     * Find large sales (above threshold)
     */
    @Query("{'totals.totalAmount': {'$gte': ?0}, 'status': 'COMPLETED'}")
    List<Sales> findLargeSales(BigDecimal threshold);
    
    /**
     * Find sales with returns
     */
    @Query("{'status': 'RETURNED'}")
    List<Sales> findReturnedSales();
    
    /**
     * Calculate total revenue for period
     */
    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'saleDateTime.date': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': null, 'totalRevenue': { '$sum': '$totals.totalAmount' } } }"
    })
    Optional<BigDecimal> calculateTotalRevenue(LocalDate startDate, LocalDate endDate);
    
    /**
     * Interface for daily sales statistics projection
     */
    interface DailySalesStats {
        LocalDate getId();
        Long getTotalSales();
        BigDecimal getTotalRevenue();
        BigDecimal getTotalProfit();
        BigDecimal getAverageSale();
        Long getTotalItems();
    }
    
    /**
     * Interface for monthly sales statistics projection
     */
    interface MonthlySalesStats {
        MonthYear getId();
        Long getTotalSales();
        BigDecimal getTotalRevenue();
        BigDecimal getTotalProfit();
        BigDecimal getAverageSale();
        
        interface MonthYear {
            Integer getYear();
            Integer getMonth();
        }
    }
    
    /**
     * Interface for payment method statistics projection
     */
    interface PaymentMethodStats {
        String getId();
        Long getTotalSales();
        BigDecimal getTotalAmount();
        Long getPercentage();
    }
    
    /**
     * Interface for top customer statistics projection
     */
    interface TopCustomerStats {
        String getId();
        String getCustomerName();
        Long getTotalSales();
        BigDecimal getTotalAmount();
        BigDecimal getAverageAmount();
        LocalDate getLastSaleDate();
    }
}
