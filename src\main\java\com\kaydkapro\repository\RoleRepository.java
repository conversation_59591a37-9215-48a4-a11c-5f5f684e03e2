package com.kaydkapro.repository;

import com.kaydkapro.entity.Role;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Role Repository - Data access layer for Role entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface RoleRepository extends MongoRepository<Role, String> {
    
    /**
     * Find role by name
     */
    Optional<Role> findByName(String name);
    
    /**
     * Find roles by names
     */
    List<Role> findByNameIn(List<String> names);
    
    /**
     * Check if role name exists
     */
    boolean existsByName(String name);
    
    /**
     * Check if role name exists excluding current role
     */
    @Query("{'name': ?0, '_id': {'$ne': ?1}}")
    boolean existsByNameAndIdNot(String name, String id);
    
    /**
     * Find roles with specific permission
     */
    @Query("{'permissions': ?0}")
    List<Role> findByPermission(String permission);
    
    /**
     * Find roles with any of the specified permissions
     */
    @Query("{'permissions': {'$in': ?0}}")
    List<Role> findByPermissionsIn(List<String> permissions);
    
    /**
     * Find roles with all specified permissions
     */
    @Query("{'permissions': {'$all': ?0}}")
    List<Role> findByPermissionsAll(List<String> permissions);
    
    /**
     * Search roles by name or description
     */
    @Query("{'$or': [" +
           "{'name': {'$regex': ?0, '$options': 'i'}}, " +
           "{'description': {'$regex': ?0, '$options': 'i'}}" +
           "]}")
    List<Role> searchRoles(String searchTerm);
    
    /**
     * Find default system roles
     */
    @Query("{'name': {'$in': ['ADMIN', 'MANAGER', 'CASHIER', 'STOREKEEPER']}}")
    List<Role> findSystemRoles();
    
    /**
     * Count total roles
     */
    long count();
}
