package com.kaydkapro.config;

import com.kaydkapro.security.CustomUserDetailsService;
import com.kaydkapro.security.JwtAuthenticationEntryPoint;
import com.kaydkapro.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * Security Configuration - Spring Security configuration for JWT authentication
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {
    
    private final CustomUserDetailsService customUserDetailsService;
    private final JwtAuthenticationEntryPoint unauthorizedHandler;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(customUserDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }
    
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(AbstractHttpConfigurer::disable)
            .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Public endpoints
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                
                // Admin only endpoints
                .requestMatchers("/api/users/*/roles/**").hasRole("ADMIN")
                .requestMatchers("/api/users/statistics").hasRole("ADMIN")
                
                // Manager and Admin endpoints
                .requestMatchers("/api/products/**").hasAnyRole("MANAGER", "ADMIN", "STOREKEEPER")
                .requestMatchers("/api/categories/**").hasAnyRole("MANAGER", "ADMIN")
                .requestMatchers("/api/inventory/adjust-stock").hasAnyRole("MANAGER", "ADMIN")
                .requestMatchers("/api/sales/*/cancel").hasAnyRole("MANAGER", "ADMIN")
                .requestMatchers("/api/finance/**").hasAnyRole("MANAGER", "ADMIN")
                .requestMatchers("/api/notifications/broadcast").hasAnyRole("MANAGER", "ADMIN")
                .requestMatchers("/api/qrcodes/bulk/**").hasAnyRole("MANAGER", "ADMIN")
                .requestMatchers("/api/dashboard/analytics/**").hasAnyRole("MANAGER", "ADMIN")
                
                // Cashier, Manager and Admin endpoints
                .requestMatchers("/api/sales/**").hasAnyRole("CASHIER", "MANAGER", "ADMIN")
                .requestMatchers("/api/inventory/reserve-stock").hasAnyRole("CASHIER", "MANAGER", "ADMIN")
                .requestMatchers("/api/inventory/release-stock").hasAnyRole("CASHIER", "MANAGER", "ADMIN")
                
                // Storekeeper, Manager and Admin endpoints
                .requestMatchers("/api/inventory/add-stock").hasAnyRole("STOREKEEPER", "MANAGER", "ADMIN")
                .requestMatchers("/api/inventory/remove-stock").hasAnyRole("STOREKEEPER", "MANAGER", "ADMIN")
                .requestMatchers("/api/inventory/transfer-stock").hasAnyRole("STOREKEEPER", "MANAGER", "ADMIN")
                
                // All authenticated users
                .requestMatchers("/api/**").hasRole("USER")
                
                // All other requests need authentication
                .anyRequest().authenticated()
            );
        
        http.authenticationProvider(authenticationProvider());
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        
        return http.build();
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
