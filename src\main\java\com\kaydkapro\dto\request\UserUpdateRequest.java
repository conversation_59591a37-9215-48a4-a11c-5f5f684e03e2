package com.kaydkapro.dto.request;

import com.kaydkapro.entity.User;
import lombok.Data;
import jakarta.validation.constraints.*;

/**
 * User Update Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class UserUpdateRequest {
    
    @Email(message = "Email should be valid")
    @Size(max = 100, message = "Email must not exceed 100 characters")
    private String email;
    
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    private String firstName;
    
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    private String lastName;
    
    @Size(max = 20, message = "Phone number must not exceed 20 characters")
    @Pattern(regexp = "^[+]?[0-9\\s\\-\\(\\)]*$", message = "Invalid phone number format")
    private String phone;
    
    @Size(max = 500, message = "Profile image URL must not exceed 500 characters")
    private String profileImage;
    
    /**
     * Convert DTO to Entity
     */
    public User toEntity() {
        return User.builder()
                .email(this.email)
                .firstName(this.firstName)
                .lastName(this.lastName)
                .phone(this.phone)
                .profileImage(this.profileImage)
                .build();
    }
}
