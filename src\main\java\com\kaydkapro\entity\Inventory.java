package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Inventory Entity - Represents product stock levels in warehouses
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "inventory")
@CompoundIndex(def = "{'product.productId': 1, 'warehouse.warehouseId': 1}", unique = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Inventory {
    
    @Id
    private String id;
    
    @NotNull(message = "Product information is required")
    private InventoryProduct product;
    
    @NotNull(message = "Warehouse information is required")
    private InventoryWarehouse warehouse;
    
    @NotNull(message = "Quantities information is required")
    private InventoryQuantities quantities;
    
    private InventoryCostInfo costInfo;
    
    @LastModifiedDate
    private LocalDateTime lastUpdated;
    
    /**
     * Nested class for product information in inventory
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InventoryProduct {
        private String productId;
        private String productName;
        private String sku;
    }
    
    /**
     * Nested class for warehouse information in inventory
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InventoryWarehouse {
        private String warehouseId;
        private String warehouseName;
        private String warehouseCode;
    }
    
    /**
     * Nested class for inventory quantities
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InventoryQuantities {
        @NotNull(message = "Available quantity is required")
        @DecimalMin(value = "0.0", message = "Available quantity must be non-negative")
        @Builder.Default
        private BigDecimal available = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "Reserved quantity must be non-negative")
        @Builder.Default
        private BigDecimal reserved = BigDecimal.ZERO;
        
        @DecimalMin(value = "0.0", message = "On order quantity must be non-negative")
        @Builder.Default
        private BigDecimal onOrder = BigDecimal.ZERO;
        
        public BigDecimal getTotalStock() {
            return available.add(reserved);
        }
        
        public BigDecimal getAvailableForSale() {
            return available.subtract(reserved);
        }
    }
    
    /**
     * Nested class for cost information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InventoryCostInfo {
        private BigDecimal averageCost;
        private BigDecimal lastCost;
        
        public BigDecimal getTotalValue(BigDecimal quantity) {
            if (averageCost != null && quantity != null) {
                return averageCost.multiply(quantity);
            }
            return BigDecimal.ZERO;
        }
    }
    
    // Helper methods
    public BigDecimal getTotalStockValue() {
        if (costInfo != null && costInfo.getAverageCost() != null) {
            return costInfo.getAverageCost().multiply(quantities.getTotalStock());
        }
        return BigDecimal.ZERO;
    }
    
    public boolean isLowStock(Integer minStockLevel) {
        if (minStockLevel == null) return false;
        return quantities.getAvailable().compareTo(BigDecimal.valueOf(minStockLevel)) <= 0;
    }
    
    public boolean isOutOfStock() {
        return quantities.getAvailable().compareTo(BigDecimal.ZERO) <= 0;
    }
}
