package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Expense Entity - Represents expense transactions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "expenses")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Expense {
    
    @Id
    private String id;
    
    @NotBlank(message = "Expense title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    private String title;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Amount must be greater than 0")
    private BigDecimal amount;
    
    @NotBlank(message = "Category is required")
    @Size(max = 100, message = "Category must not exceed 100 characters")
    private String category;
    
    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;
    
    @NotNull(message = "Expense date is required")
    private LocalDate expenseDate;
    
    private ExpenseReference reference;
    
    private String receiptImage;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    private String notes;
    
    @Size(max = 100, message = "Payment method must not exceed 100 characters")
    private String paymentMethod; // CASH, BANK_TRANSFER, CHECK, CARD
    
    @Size(max = 100, message = "Vendor must not exceed 100 characters")
    private String vendor;
    
    @Builder.Default
    private Boolean isRecurring = false;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @NotNull(message = "Created by information is required")
    private ExpenseUser createdBy;
    
    /**
     * Nested class for expense reference
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpenseReference {
        private String type; // PURCHASE, PAYROLL, UTILITY, OTHER
        private String referenceId;
        private String referenceNumber;
    }
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpenseUser {
        private String userId;
        private String userName;
    }
    
    // Expense category constants
    public static final String CATEGORY_RENT = "RENT";
    public static final String CATEGORY_UTILITIES = "UTILITIES";
    public static final String CATEGORY_SALARIES = "SALARIES";
    public static final String CATEGORY_MARKETING = "MARKETING";
    public static final String CATEGORY_OFFICE_SUPPLIES = "OFFICE_SUPPLIES";
    public static final String CATEGORY_TRAVEL = "TRAVEL";
    public static final String CATEGORY_MAINTENANCE = "MAINTENANCE";
    public static final String CATEGORY_INSURANCE = "INSURANCE";
    public static final String CATEGORY_TAXES = "TAXES";
    public static final String CATEGORY_PROFESSIONAL_SERVICES = "PROFESSIONAL_SERVICES";
    public static final String CATEGORY_EQUIPMENT = "EQUIPMENT";
    public static final String CATEGORY_OTHER = "OTHER";
    
    // Reference type constants
    public static final String REF_PURCHASE = "PURCHASE";
    public static final String REF_PAYROLL = "PAYROLL";
    public static final String REF_UTILITY = "UTILITY";
    public static final String REF_OTHER = "OTHER";
    
    // Payment method constants
    public static final String PAYMENT_CASH = "CASH";
    public static final String PAYMENT_BANK_TRANSFER = "BANK_TRANSFER";
    public static final String PAYMENT_CHECK = "CHECK";
    public static final String PAYMENT_CARD = "CARD";
    
    // Helper methods
    public boolean isOperationalExpense() {
        return CATEGORY_RENT.equals(category) || 
               CATEGORY_UTILITIES.equals(category) || 
               CATEGORY_SALARIES.equals(category) ||
               CATEGORY_OFFICE_SUPPLIES.equals(category);
    }
    
    public boolean hasReference() {
        return reference != null && reference.getReferenceId() != null;
    }
    
    public String getFormattedAmount() {
        return String.format("$%.2f", amount);
    }
    
    public boolean isLargeExpense(BigDecimal threshold) {
        return amount.compareTo(threshold) > 0;
    }
}
