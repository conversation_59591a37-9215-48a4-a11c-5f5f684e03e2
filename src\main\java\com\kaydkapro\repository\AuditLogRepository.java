package com.kaydkapro.repository;

import com.kaydkapro.entity.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AuditLog Repository - Data access layer for AuditLog entity
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface AuditLogRepository extends MongoRepository<AuditLog, String> {
    
    /**
     * Find audit logs by user ID
     */
    @Query("{'user.userId': ?0}")
    List<AuditLog> findByUserId(String userId);
    
    /**
     * Find audit logs by user ID with pagination
     */
    @Query("{'user.userId': ?0}")
    Page<AuditLog> findByUserId(String userId, Pageable pageable);
    
    /**
     * Find audit logs by action
     */
    List<AuditLog> findByAction(String action);
    
    /**
     * Find audit logs by entity type
     */
    @Query("{'entity.type': ?0}")
    List<AuditLog> findByEntityType(String entityType);
    
    /**
     * Find audit logs by entity type with pagination
     */
    @Query("{'entity.type': ?0}")
    Page<AuditLog> findByEntityType(String entityType, Pageable pageable);
    
    /**
     * Find audit logs by entity ID
     */
    @Query("{'entity.entityId': ?0}")
    List<AuditLog> findByEntityId(String entityId);
    
    /**
     * Find audit logs by entity type and entity ID
     */
    @Query("{'entity.type': ?0, 'entity.entityId': ?1}")
    List<AuditLog> findByEntityTypeAndEntityId(String entityType, String entityId);
    
    /**
     * Find audit logs by user ID and action
     */
    @Query("{'user.userId': ?0, 'action': ?1}")
    List<AuditLog> findByUserIdAndAction(String userId, String action);
    
    /**
     * Find audit logs by user ID and entity type
     */
    @Query("{'user.userId': ?0, 'entity.type': ?1}")
    List<AuditLog> findByUserIdAndEntityType(String userId, String entityType);
    
    /**
     * Find audit logs created after specific date
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<AuditLog> findByCreatedAtAfter(LocalDateTime date);
    
    /**
     * Find audit logs by date range
     */
    @Query("{'createdAt': {'$gte': ?0, '$lte': ?1}}")
    List<AuditLog> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find audit logs by date range with pagination
     */
    @Query("{'createdAt': {'$gte': ?0, '$lte': ?1}}")
    Page<AuditLog> findByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    /**
     * Find audit logs by user ID and date range
     */
    @Query("{'user.userId': ?0, 'createdAt': {'$gte': ?1, '$lte': ?2}}")
    List<AuditLog> findByUserIdAndDateRange(String userId, LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find audit logs by IP address
     */
    @Query("{'metadata.ipAddress': ?0}")
    List<AuditLog> findByIpAddress(String ipAddress);
    
    /**
     * Find login/logout audit logs
     */
    @Query("{'action': {'$in': ['LOGIN', 'LOGOUT']}}")
    List<AuditLog> findSecurityLogs();
    
    /**
     * Find login/logout audit logs with pagination
     */
    @Query("{'action': {'$in': ['LOGIN', 'LOGOUT']}}")
    Page<AuditLog> findSecurityLogs(Pageable pageable);
    
    /**
     * Find login logs by user ID
     */
    @Query("{'user.userId': ?0, 'action': 'LOGIN'}")
    List<AuditLog> findLoginLogsByUserId(String userId);
    
    /**
     * Find data modification logs (CREATE, UPDATE, DELETE)
     */
    @Query("{'action': {'$in': ['CREATE', 'UPDATE', 'DELETE']}}")
    List<AuditLog> findDataModificationLogs();
    
    /**
     * Find data modification logs with pagination
     */
    @Query("{'action': {'$in': ['CREATE', 'UPDATE', 'DELETE']}}")
    Page<AuditLog> findDataModificationLogs(Pageable pageable);
    
    /**
     * Find audit logs with changes
     */
    @Query("{'changes': {'$ne': null}}")
    List<AuditLog> findLogsWithChanges();
    
    /**
     * Find audit logs by user role
     */
    @Query("{'user.userRole': ?0}")
    List<AuditLog> findByUserRole(String userRole);
    
    /**
     * Find recent audit logs (last N hours)
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<AuditLog> findRecentLogs(LocalDateTime since);
    
    /**
     * Count audit logs by user ID
     */
    @Query(value = "{'user.userId': ?0}", count = true)
    long countByUserId(String userId);
    
    /**
     * Count audit logs by action
     */
    long countByAction(String action);
    
    /**
     * Count audit logs by entity type
     */
    @Query(value = "{'entity.type': ?0}", count = true)
    long countByEntityType(String entityType);
    
    /**
     * Count audit logs by date range
     */
    @Query(value = "{'createdAt': {'$gte': ?0, '$lte': ?1}}", count = true)
    long countByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Find audit logs by session ID
     */
    @Query("{'metadata.sessionId': ?0}")
    List<AuditLog> findBySessionId(String sessionId);
    
    /**
     * Find audit logs by request ID
     */
    @Query("{'metadata.requestId': ?0}")
    List<AuditLog> findByRequestId(String requestId);
    
    /**
     * Search audit logs by entity name
     */
    @Query("{'entity.entityName': {'$regex': ?0, '$options': 'i'}}")
    List<AuditLog> searchByEntityName(String entityName);
    
    /**
     * Find audit logs by user name
     */
    @Query("{'user.userName': {'$regex': ?0, '$options': 'i'}}")
    List<AuditLog> findByUserName(String userName);
    
    /**
     * Find failed operations (if you track success/failure)
     */
    @Query("{'metadata.success': false}")
    List<AuditLog> findFailedOperations();
    
    /**
     * Delete old audit logs (cleanup)
     */
    void deleteByCreatedAtBefore(LocalDateTime before);
    
    /**
     * Find audit logs with specific metadata
     */
    @Query("{'metadata.additionalData.?0': ?1}")
    List<AuditLog> findByMetadataKeyValue(String key, Object value);
    
    /**
     * Find all distinct actions
     */
    @Query(value = "{}", fields = "{'action': 1}")
    List<String> findDistinctActions();
    
    /**
     * Find all distinct entity types
     */
    @Query(value = "{}", fields = "{'entity.type': 1}")
    List<String> findDistinctEntityTypes();
    
    /**
     * Find all distinct user roles
     */
    @Query(value = "{}", fields = "{'user.userRole': 1}")
    List<String> findDistinctUserRoles();
    
    /**
     * Find audit logs for sensitive operations
     */
    @Query("{'action': {'$in': ['DELETE', 'UPDATE']}, 'entity.type': {'$in': ['USER', 'ROLE', 'PRODUCT', 'FINANCIAL']}}")
    List<AuditLog> findSensitiveOperations();
    
    /**
     * Find bulk operations
     */
    @Query("{'action': {'$regex': 'BULK', '$options': 'i'}}")
    List<AuditLog> findBulkOperations();
}
