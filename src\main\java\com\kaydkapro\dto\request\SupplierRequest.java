package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Supplier;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * Supplier Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class SupplierRequest {
    
    @NotBlank(message = "Company name is required")
    @Size(max = 200, message = "Company name must not exceed 200 characters")
    private String companyName;
    
    @NotBlank(message = "Supplier code is required")
    @Size(max = 20, message = "Supplier code must not exceed 20 characters")
    @Pattern(regexp = "^[A-Z0-9_-]+$", message = "Supplier code can only contain uppercase letters, numbers, underscores, and hyphens")
    private String supplierCode;
    
    @Size(max = 50, message = "Tax ID must not exceed 50 characters")
    private String taxId;
    
    @Size(max = 100, message = "Industry must not exceed 100 characters")
    private String industry;
    
    @NotNull(message = "Contact information is required")
    private ContactInfo contact;
    
    @NotNull(message = "Address information is required")
    private AddressInfo address;
    
    private PaymentInfo paymentInfo;
    
    private List<String> categories;
    
    @DecimalMin(value = "0.0", message = "Rating must be between 0 and 5")
    @DecimalMax(value = "5.0", message = "Rating must be between 0 and 5")
    private BigDecimal rating = BigDecimal.valueOf(3.0);
    
    private Boolean isPreferred = false;
    
    private Boolean isActive = true;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    private String notes;
    
    @Data
    public static class ContactInfo {
        @NotBlank(message = "Contact person name is required")
        @Size(max = 100, message = "Contact person name must not exceed 100 characters")
        private String contactPerson;
        
        @Size(max = 100, message = "Job title must not exceed 100 characters")
        private String jobTitle;
        
        @NotBlank(message = "Phone number is required")
        @Size(max = 20, message = "Phone number must not exceed 20 characters")
        @Pattern(regexp = "^[+]?[0-9\\s\\-\\(\\)]*$", message = "Invalid phone number format")
        private String phone;
        
        @Email(message = "Email should be valid")
        @Size(max = 100, message = "Email must not exceed 100 characters")
        private String email;
        
        @Size(max = 20, message = "Mobile number must not exceed 20 characters")
        private String mobile;
        
        @Size(max = 20, message = "Fax number must not exceed 20 characters")
        private String fax;
        
        @Size(max = 200, message = "Website must not exceed 200 characters")
        private String website;
    }
    
    @Data
    public static class AddressInfo {
        @NotBlank(message = "Street address is required")
        @Size(max = 500, message = "Street address must not exceed 500 characters")
        private String street;
        
        @NotBlank(message = "City is required")
        @Size(max = 100, message = "City must not exceed 100 characters")
        private String city;
        
        @Size(max = 100, message = "State must not exceed 100 characters")
        private String state;
        
        @NotBlank(message = "Country is required")
        @Size(max = 100, message = "Country must not exceed 100 characters")
        private String country;
        
        @Size(max = 20, message = "Postal code must not exceed 20 characters")
        private String postalCode;
    }
    
    @Data
    public static class PaymentInfo {
        @Size(max = 50, message = "Payment terms must not exceed 50 characters")
        private String paymentTerms;
        
        @Min(value = 0, message = "Credit days must be non-negative")
        private Integer creditDays = 30;
        
        @DecimalMin(value = "0.0", message = "Credit limit must be non-negative")
        private BigDecimal creditLimit = BigDecimal.ZERO;
        
        @Size(max = 20, message = "Currency must not exceed 20 characters")
        private String currency = "USD";
        
        @Size(max = 100, message = "Bank name must not exceed 100 characters")
        private String bankName;
        
        @Size(max = 50, message = "Account number must not exceed 50 characters")
        private String accountNumber;
        
        @Size(max = 50, message = "SWIFT code must not exceed 50 characters")
        private String swiftCode;
        
        @Size(max = 50, message = "IBAN must not exceed 50 characters")
        private String iban;
    }
    
    /**
     * Convert DTO to Entity
     */
    public Supplier toEntity() {
        Supplier supplier = Supplier.builder()
                .companyName(this.companyName)
                .supplierCode(this.supplierCode)
                .taxId(this.taxId)
                .industry(this.industry)
                .categories(this.categories)
                .rating(this.rating)
                .isPreferred(this.isPreferred)
                .isActive(this.isActive)
                .notes(this.notes)
                .build();
        
        // Set contact
        if (this.contact != null) {
            supplier.setContact(Supplier.SupplierContact.builder()
                    .contactPerson(this.contact.getContactPerson())
                    .jobTitle(this.contact.getJobTitle())
                    .phone(this.contact.getPhone())
                    .email(this.contact.getEmail())
                    .mobile(this.contact.getMobile())
                    .fax(this.contact.getFax())
                    .website(this.contact.getWebsite())
                    .build());
        }
        
        // Set address
        if (this.address != null) {
            supplier.setAddress(Supplier.SupplierAddress.builder()
                    .street(this.address.getStreet())
                    .city(this.address.getCity())
                    .state(this.address.getState())
                    .country(this.address.getCountry())
                    .postalCode(this.address.getPostalCode())
                    .build());
        }
        
        // Set payment info
        if (this.paymentInfo != null) {
            supplier.setPaymentInfo(Supplier.SupplierPaymentInfo.builder()
                    .paymentTerms(this.paymentInfo.getPaymentTerms())
                    .creditDays(this.paymentInfo.getCreditDays())
                    .creditLimit(this.paymentInfo.getCreditLimit())
                    .currency(this.paymentInfo.getCurrency())
                    .bankName(this.paymentInfo.getBankName())
                    .accountNumber(this.paymentInfo.getAccountNumber())
                    .swiftCode(this.paymentInfo.getSwiftCode())
                    .iban(this.paymentInfo.getIban())
                    .build());
        }
        
        return supplier;
    }
}
