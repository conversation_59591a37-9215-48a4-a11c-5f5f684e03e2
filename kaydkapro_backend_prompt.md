# 🔥 KING KONG BACKEND PROMPT FOR LLM

```
You are a senior Spring Boot architect building "KaydkaPro" - a comprehensive inventory and business management system for Somali businesses. Create a COMPLETE, PRODUCTION-READY Spring Boot application with the following specifications:

## 🎯 PROJECT REQUIREMENTS

### **Tech Stack:**
- ✅ Spring Boot 3.2+ with Java 17
- ✅ Spring Security 6 + JWT Authentication
- ✅ Spring Data MongoDB + MongoDB
- ✅ Langchain4j for AI features
- ✅ WebSocket for real-time updates
- ✅ Maven build system
- ✅ Validation with Bean Validation
- ✅ Lombok for boilerplate reduction

### **Core Business Modules:**
1. ✅ Authentication & User Management
2. ✅ Product Management with Categories
3. ✅ Inventory Management with Multi-warehouse
4. Sales & POS System
5. Purchase Management with Suppliers
6. Financial Management (Income/Expenses)
7. AI-powered Features with Langchain4j
8. Real-time Notifications & Alerts
9. QR Code Generation System
10. Comprehensive Reporting

## 🏗️ DETAILED PROJECT STRUCTURE

Create this EXACT folder structure:

```

src/main/java/com/kaydkapro/
├── KaydkaproApplication.java
├── config/
│ ├── ✅ SecurityConfig.java
│ ├── ✅ MongoConfig.java
│ ├── WebSocketConfig.java
│ ├── LangchainConfig.java
│ ├── CorsConfig.java
│ └── DatabaseConfig.java
├── controller/
│ ├── ✅ AuthController.java
│ ├── ✅ UserController.java
│ ├── ✅ ProductController.java
│ ├── ✅ CategoryController.java
│ ├── ✅ InventoryController.java
│ ├── ✅ WarehouseController.java
│ ├── ✅ SalesController.java
│ ├── ✅ PurchaseController.java
│ ├── ✅ SupplierController.java
│ ├── ✅ CustomerController.java
│ ├── ✅ FinanceController.java
│ ├── ✅ ReportController.java
│ ├── ✅ QRCodeController.java
│ ├── ✅ AIController.java
│ ├── ✅ NotificationController.java
│ └── ✅ DashboardController.java
├── service/
│ ├── ✅ AuthService.java
│ ├── ✅ UserService.java
│ ├── ✅ ProductService.java
│ ├── ✅ CategoryService.java
│ ├── ✅ InventoryService.java
│ ├── WarehouseService.java
│ ├── ✅ SalesService.java
│ ├── PurchaseService.java
│ ├── SupplierService.java
│ ├── CustomerService.java
│ ├── ✅ FinancialService.java (Income & Expense)
│ ├── ReportService.java
│ ├── ✅ QRCodeService.java
│ ├── AIService.java
│ ├── EmailService.java
│ ├── ✅ NotificationService.java
│ ├── ✅ AuditLogService.java
│ └── DashboardService.java
├── repository/
│ ├── ✅ UserRepository.java
│ ├── ✅ RoleRepository.java
│ ├── ✅ ProductRepository.java
│ ├── ✅ CategoryRepository.java
│ ├── ✅ InventoryRepository.java
│ ├── ✅ WarehouseRepository.java
│ ├── ✅ SalesRepository.java
│ ├── SaleItemRepository.java
│ ├── ✅ PurchaseRepository.java
│ ├── PurchaseItemRepository.java
│ ├── ✅ SupplierRepository.java
│ ├── ✅ CustomerRepository.java
│ ├── ✅ IncomeRepository.java
│ ├── ✅ ExpenseRepository.java
│ ├── ✅ NotificationRepository.java
│ ├── ✅ AuditLogRepository.java
│ ├── ✅ QRCodeRepository.java
│ └── ✅ StockMovementRepository.java
├── entity/
│ ├── ✅ User.java
│ ├── ✅ Role.java
│ ├── ✅ Product.java
│ ├── ✅ Category.java
│ ├── ✅ Inventory.java
│ ├── ✅ Warehouse.java
│ ├── ✅ StockMovement.java
│ ├── ✅ Sales.java
│ ├── ✅ SaleItem.java
│ ├── ✅ Purchase.java
│ ├── PurchaseItem.java
│ ├── ✅ Supplier.java
│ ├── ✅ Customer.java
│ ├── ✅ Income.java
│ ├── ✅ Expense.java
│ ├── ✅ Notification.java
│ ├── ✅ AuditLog.java
│ └── ✅ QRCode.java
├── dto/
│ ├── request/
│ │ ├── ✅ LoginRequest.java
│ │ ├── ✅ RegisterRequest.java
│ │ ├── ✅ ProductRequest.java
│ │ ├── ✅ SalesRequest.java
│ │ ├── ✅ PaymentUpdateRequest.java
│ │ ├── ✅ StockTransferRequest.java
│ │ ├── ✅ StockAdjustmentRequest.java
│ │ ├── ✅ IncomeRequest.java
│ │ ├── ✅ ExpenseRequest.java
│ │ ├── ✅ CategoryRequest.java
│ │ ├── ✅ NotificationRequest.java
│ │ ├── ✅ UserRequest.java
│ │ ├── ✅ UserUpdateRequest.java
│ │ ├── ✅ QRCodeRequest.java
│ │ ├── ✅ WarehouseRequest.java
│ │ ├── ✅ CustomerRequest.java
│ │ ├── ✅ SupplierRequest.java
│ │ ├── ✅ PurchaseRequest.java
│ │ └── ✅ AIQueryRequest.java
│ └── response/
│ ├── ✅ LoginResponse.java
│ ├── ✅ ApiResponse.java
│ ├── ✅ PageResponse.java
│ ├── UserResponse.java
│ ├── ProductResponse.java
│ ├── SalesResponse.java
│ ├── DashboardResponse.java
│ ├── ReportResponse.java
│ └── AIResponse.java
├── security/
│ ├── ✅ JwtAuthenticationEntryPoint.java
│ ├── ✅ JwtAuthenticationFilter.java
│ ├── ✅ JwtTokenProvider.java
│ ├── ✅ UserPrincipal.java
│ └── ✅ CustomUserDetailsService.java
├── exception/
│ ├── ✅ GlobalExceptionHandler.java
│ ├── ✅ ResourceNotFoundException.java
│ ├── ✅ BadRequestException.java
│ └── ✅ UnauthorizedException.java
├── util/
│ ├── ResponseUtil.java
│ ├── DateUtil.java
│ ├── ValidationUtil.java
│ ├── QRCodeUtil.java
│ └── EmailTemplateUtil.java
└── websocket/
├── WebSocketHandler.java
├── NotificationWebSocketController.java
└── DashboardWebSocketController.java

```

## 🛣️ COMPLETE API ROUTES SPECIFICATION

### **Authentication Routes (AuthController)**
```

POST /api/auth/login
POST /api/auth/register
POST /api/auth/refresh-token
POST /api/auth/logout
POST /api/auth/forgot-password
POST /api/auth/reset-password
GET /api/auth/me

```

### **User Management Routes (UserController)**
```

GET /api/users
GET /api/users/{id}
POST /api/users
PUT /api/users/{id}
DELETE /api/users/{id}
POST /api/users/bulk-import
GET /api/users/search?q={query}
PUT /api/users/{id}/status
PUT /api/users/{id}/role
GET /api/users/activity-logs

```

### **Product Management Routes (ProductController)**
```

GET /api/products
GET /api/products/{id}
POST /api/products
PUT /api/products/{id}
DELETE /api/products/{id}
POST /api/products/bulk-import
GET /api/products/search?q={query}
POST /api/products/{id}/images
DELETE /api/products/{id}/images/{imageId}
GET /api/products/barcode/{barcode}
POST /api/products/{id}/generate-qr
GET /api/products/low-stock
GET /api/products/expiring-soon

```

### **Category Management Routes (CategoryController)**
```

GET /api/categories
GET /api/categories/{id}
POST /api/categories
PUT /api/categories/{id}
DELETE /api/categories/{id}
GET /api/categories/{id}/products
GET /api/categories/tree

```

### **Inventory Management Routes (InventoryController)**
```

GET /api/inventory
GET /api/inventory/product/{productId}
POST /api/inventory/adjustment
POST /api/inventory/transfer
GET /api/inventory/movements
GET /api/inventory/movements/{id}
POST /api/inventory/physical-count
GET /api/inventory/valuation
GET /api/inventory/aging-report
PUT /api/inventory/reorder-level

```

### **Warehouse Management Routes (WarehouseController)**
```

GET /api/warehouses
GET /api/warehouses/{id}
POST /api/warehouses
PUT /api/warehouses/{id}
DELETE /api/warehouses/{id}
GET /api/warehouses/{id}/inventory
GET /api/warehouses/{id}/capacity
POST /api/warehouses/{id}/transfer-stock

```

### **Sales Management Routes (SalesController)**
```

GET /api/sales
GET /api/sales/{id}
POST /api/sales
PUT /api/sales/{id}
DELETE /api/sales/{id}
POST /api/sales/{id}/return
GET /api/sales/daily-summary
GET /api/sales/pos-session
POST /api/sales/pos-session/start
POST /api/sales/pos-session/end
GET /api/sales/invoice/{id}/pdf
POST /api/sales/bulk-discount

```

### **Purchase Management Routes (PurchaseController)**
```

GET /api/purchases
GET /api/purchases/{id}
POST /api/purchases
PUT /api/purchases/{id}
DELETE /api/purchases/{id}
POST /api/purchases/order
PUT /api/purchases/order/{id}/approve
POST /api/purchases/{id}/receive
POST /api/purchases/{id}/return
GET /api/purchases/pending-orders
GET /api/purchases/invoice/{id}/pdf

```

### **Supplier Management Routes (SupplierController)**
```

GET /api/suppliers
GET /api/suppliers/{id}
POST /api/suppliers
PUT /api/suppliers/{id}
DELETE /api/suppliers/{id}
GET /api/suppliers/{id}/purchases
GET /api/suppliers/{id}/performance
GET /api/suppliers/search?q={query}
POST /api/suppliers/bulk-import

```

### **Customer Management Routes (CustomerController)**
```

GET /api/customers
GET /api/customers/{id}
POST /api/customers
PUT /api/customers/{id}
DELETE /api/customers/{id}
GET /api/customers/{id}/purchases
GET /api/customers/{id}/loyalty-points
GET /api/customers/search?q={query}
POST /api/customers/bulk-import

```

### **Financial Management Routes (FinanceController)**
```

GET /api/finance/income
POST /api/finance/income
PUT /api/finance/income/{id}
DELETE /api/finance/income/{id}
GET /api/finance/expenses
POST /api/finance/expenses
PUT /api/finance/expenses/{id}
DELETE /api/finance/expenses/{id}
GET /api/finance/profit-loss
GET /api/finance/cash-flow
GET /api/finance/budget
POST /api/finance/budget
GET /api/finance/tax-report

```

### **AI Features Routes (AIController)**
```

POST /api/ai/query
POST /api/ai/generate-email
POST /api/ai/business-insights
POST /api/ai/demand-forecast
POST /api/ai/price-optimization
POST /api/ai/categorize-product
GET /api/ai/recommendations
POST /api/ai/voice-command

```

### **QR Code Routes (QRCodeController)**
```

POST /api/qr/generate/{productId}
GET /api/qr/{qrId}/info
POST /api/qr/scan
GET /api/qr/product/{productId}
POST /api/qr/bulk-generate
GET /api/qr/{qrId}/print-label

```

### **Reporting Routes (ReportController)**
```

GET /api/reports/sales?startDate={}&endDate={}
GET /api/reports/purchases?startDate={}&endDate={}
GET /api/reports/inventory
GET /api/reports/financial?startDate={}&endDate={}
GET /api/reports/low-stock
GET /api/reports/top-products?limit={}
GET /api/reports/customer-analytics
GET /api/reports/supplier-performance
POST /api/reports/custom
GET /api/reports/{id}/export/{format}

```

### **Dashboard Routes (DashboardController)**
```

GET /api/dashboard/overview
GET /api/dashboard/sales-stats
GET /api/dashboard/inventory-stats
GET /api/dashboard/financial-stats
GET /api/dashboard/recent-activities
GET /api/dashboard/alerts
GET /api/dashboard/top-products
GET /api/dashboard/real-time-data

```

### **Notification Routes (NotificationController)**
```

GET /api/notifications
GET /api/notifications/{id}
PUT /api/notifications/{id}/read
DELETE /api/notifications/{id}
POST /api/notifications/mark-all-read
GET /api/notifications/unread-count
POST /api/notifications/subscribe
POST /api/notifications/test-email

````

## 🎯 SPECIFIC REQUIREMENTS

### **Entity Relationships:**
- User ↔ Role (Many-to-Many)
- Product ↔ Category (Many-to-One)
- Product ↔ Inventory (One-to-Many via Warehouse)
- Sales ↔ SaleItem (One-to-Many)
- Purchase ↔ PurchaseItem (One-to-Many)
- Supplier ↔ Purchase (One-to-Many)
- Customer ↔ Sales (One-to-Many)
- Warehouse ↔ Inventory (One-to-Many)

### **Security Implementation:**
- JWT-based authentication
- Role-based access control (ADMIN, MANAGER, CASHIER, STOREKEEPER)
- Method-level security annotations
- CORS configuration for React frontend
- Request/Response encryption for sensitive data

### **AI Integration (Langchain4j):**
- Natural language query processing
- Email generation for low stock alerts
- Business insights generation
- Demand forecasting algorithms
- Voice command processing

### **Real-time Features (WebSocket):**
- Live dashboard updates
- Instant notifications
- Real-time inventory updates
- Multi-user synchronization

### **Advanced Features:**
- Audit logging for all operations
- Soft delete implementation
- Pagination and sorting for all list endpoints
- Advanced search with filters
- Bulk operations support
- File upload/download capabilities
- Email integration with templates
- QR code generation and scanning
- PDF report generation
- Excel import/export functionality

### **Error Handling:**
- Global exception handler
- Custom exception classes
- Proper HTTP status codes
- Detailed error messages
- Validation error responses

### **Performance Optimization:**
- Database indexing
- Query optimization
- Caching implementation
- Connection pooling
- Lazy loading where appropriate

### **Testing Requirements:**
- Unit tests for all services
- Integration tests for controllers
- Repository tests
- Security tests
- AI service tests

## 🗄️ COMPLETE MONGODB SCHEMA

### **MongoDB Collections Structure:**

```javascript
// =============================================
// USERS & AUTHENTICATION COLLECTIONS
// =============================================

// roles collection
{
  "_id": ObjectId("..."),
  "name": "ADMIN",
  "description": "System Administrator with full access",
  "permissions": ["READ_ALL", "WRITE_ALL", "DELETE_ALL"],
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z")
}

// users collection
{
  "_id": ObjectId("..."),
  "username": "admin",
  "email": "<EMAIL>",
  "password": "$2a$10$...", // BCrypt hashed
  "firstName": "Ahmed",
  "lastName": "Hassan",
  "phone": "+252-123-456789",
  "profileImage": "/uploads/profiles/admin.jpg",
  "isActive": true,
  "roles": [
    {
      "roleId": ObjectId("..."),
      "roleName": "ADMIN",
      "assignedAt": ISODate("2024-01-01T00:00:00Z"),
      "assignedBy": ObjectId("...")
    }
  ],
  "lastLogin": ISODate("2024-01-01T00:00:00Z"),
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z"),
  "createdBy": ObjectId("..."),
  "updatedBy": ObjectId("...")
}

// =============================================
// PRODUCT MANAGEMENT COLLECTIONS
// =============================================

// categories collection
{
  "_id": ObjectId("..."),
  "name": "Food & Beverages",
  "description": "Food items and drinks",
  "parentId": null, // ObjectId for subcategories
  "imageUrl": "/uploads/categories/food.jpg",
  "isActive": true,
  "sortOrder": 1,
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z")
}

// units collection
{
  "_id": ObjectId("..."),
  "name": "Kilogram",
  "abbreviation": "kg",
  "type": "WEIGHT", // WEIGHT, VOLUME, PIECE, LENGTH
  "isActive": true,
  "createdAt": ISODate("2024-01-01T00:00:00Z")
}

// products collection
{
  "_id": ObjectId("..."),
  "name": "Premium Rice Basmati",
  "description": "High quality basmati rice imported from India",
  "sku": "RICE001",
  "barcode": "1234567890123",
  "category": {
    "categoryId": ObjectId("..."),
    "categoryName": "Food & Beverages"
  },
  "unit": {
    "unitId": ObjectId("..."),
    "unitName": "Kilogram",
    "abbreviation": "kg"
  },
  "pricing": {
    "costPrice": 25.50,
    "sellingPrice": 35.00,
    "taxRate": 15.00
  },
  "stockLevels": {
    "minStockLevel": 10,
    "maxStockLevel": 500,
    "reorderLevel": 25
  },
  "images": [
    {
      "imageUrl": "/uploads/products/rice001_1.jpg",
      "isPrimary": true,
      "sortOrder": 1
    },
    {
      "imageUrl": "/uploads/products/rice001_2.jpg",
      "isPrimary": false,
      "sortOrder": 2
    }
  ],
  "variants": [
    {
      "_id": ObjectId("..."),
      "variantName": "5kg Pack",
      "sku": "RICE001-5KG",
      "barcode": "1234567890124",
      "costPrice": 120.00,
      "sellingPrice": 165.00,
      "isActive": true
    }
  ],
  "isActive": true,
  "hasVariants": true,
  "expiryTracking": false,
  "batchTracking": false,
  "tags": ["premium", "imported", "basmati"],
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z"),
  "createdBy": ObjectId("..."),
  "updatedBy": ObjectId("...")
}

// =============================================
// WAREHOUSE & INVENTORY COLLECTIONS
// =============================================

// warehouses collection
{
  "_id": ObjectId("..."),
  "name": "Main Warehouse",
  "code": "MAIN001",
  "address": {
    "street": "Main Street",
    "city": "Mogadishu",
    "state": "Banaadir",
    "postalCode": "00252",
    "country": "Somalia"
  },
  "manager": {
    "managerId": ObjectId("..."),
    "managerName": "Omar Ahmed"
  },
  "capacity": 10000.00,
  "isActive": true,
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z")
}

// inventory collection
{
  "_id": ObjectId("..."),
  "product": {
    "productId": ObjectId("..."),
    "productName": "Premium Rice Basmati",
    "sku": "RICE001"
  },
  "warehouse": {
    "warehouseId": ObjectId("..."),
    "warehouseName": "Main Warehouse",
    "warehouseCode": "MAIN001"
  },
  "quantities": {
    "available": 150.0,
    "reserved": 25.0,
    "onOrder": 100.0
  },
  "costInfo": {
    "averageCost": 26.75,
    "lastCost": 25.50
  },
  "lastUpdated": ISODate("2024-01-01T00:00:00Z")
}

// stockMovements collection
{
  "_id": ObjectId("..."),
  "product": {
    "productId": ObjectId("..."),
    "productName": "Premium Rice Basmati",
    "sku": "RICE001"
  },
  "warehouse": {
    "warehouseId": ObjectId("..."),
    "warehouseName": "Main Warehouse"
  },
  "movementType": "IN", // IN, OUT, TRANSFER, ADJUSTMENT
  "quantity": 100.0,
  "unitCost": 25.50,
  "reference": {
    "type": "PURCHASE", // PURCHASE, SALE, TRANSFER, ADJUSTMENT, RETURN
    "referenceId": ObjectId("..."),
    "referenceNumber": "PO001"
  },
  "batchInfo": {
    "batchNumber": "BATCH001",
    "expiryDate": ISODate("2025-12-31T00:00:00Z")
  },
  "notes": "Initial stock received from supplier",
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "createdBy": {
    "userId": ObjectId("..."),
    "userName": "Ahmed Hassan"
  }
}

// =============================================
// SUPPLIER & CUSTOMER COLLECTIONS
// =============================================

// suppliers collection
{
  "_id": ObjectId("..."),
  "name": "Al-Baraka Trading Company",
  "contactPerson": "Mohamed Ali",
  "contact": {
    "email": "<EMAIL>",
    "phone": "+252-1-234567",
    "mobile": "+252-61-234567"
  },
  "address": {
    "street": "Industrial Road",
    "city": "Mogadishu",
    "state": "Banaadir",
    "postalCode": "00252",
    "country": "Somalia"
  },
  "businessInfo": {
    "taxNumber": "TAX123456",
    "paymentTerms": 30,
    "creditLimit": 50000.00,
    "rating": 4.5
  },
  "isActive": true,
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z")
}

// customers collection
{
  "_id": ObjectId("..."),
  "name": "Amina Mohamed",
  "contact": {
    "email": "<EMAIL>",
    "phone": "+252-61-123456"
  },
  "address": {
    "street": "Hamar Weyne District",
    "city": "Mogadishu",
    "state": "Banaadir",
    "postalCode": "00252",
    "country": "Somalia"
  },
  "customerType": "INDIVIDUAL", // INDIVIDUAL, BUSINESS
  "businessInfo": {
    "taxNumber": null,
    "creditLimit": 1000.00
  },
  "loyaltyProgram": {
    "points": 250,
    "tier": "SILVER" // BRONZE, SILVER, GOLD, PLATINUM
  },
  "isActive": true,
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z")
}

// =============================================
// PURCHASE MANAGEMENT COLLECTIONS
// =============================================

// purchases collection
{
  "_id": ObjectId("..."),
  "purchaseNumber": "PO-2024-001",
  "supplier": {
    "supplierId": ObjectId("..."),
    "supplierName": "Al-Baraka Trading Company",
    "contactPerson": "Mohamed Ali"
  },
  "warehouse": {
    "warehouseId": ObjectId("..."),
    "warehouseName": "Main Warehouse"
  },
  "dates": {
    "purchaseDate": ISODate("2024-01-01T00:00:00Z"),
    "deliveryDate": ISODate("2024-01-05T00:00:00Z"),
    "expectedDate": ISODate("2024-01-03T00:00:00Z")
  },
  "status": "COMPLETED", // DRAFT, PENDING, ORDERED, RECEIVED, COMPLETED, CANCELLED
  "items": [
    {
      "_id": ObjectId("..."),
      "product": {
        "productId": ObjectId("..."),
        "productName": "Premium Rice Basmati",
        "sku": "RICE001"
      },
      "quantity": 100.0,
      "unitCost": 25.50,
      "totalCost": 2550.00,
      "receivedQuantity": 100.0,
      "batchInfo": {
        "batchNumber": "BATCH001",
        "expiryDate": ISODate("2025-12-31T00:00:00Z")
      }
    }
  ],
  "totals": {
    "subtotal": 2550.00,
    "taxAmount": 382.50,
    "discountAmount": 0.00,
    "totalAmount": 2932.50
  },
  "payment": {
    "paidAmount": 2932.50,
    "paymentStatus": "PAID", // UNPAID, PARTIAL, PAID
    "paymentMethod": "BANK_TRANSFER"
  },
  "notes": "First purchase order of the year",
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedAt": ISODate("2024-01-01T00:00:00Z"),
  "createdBy": {
    "userId": ObjectId("..."),
    "userName": "Ahmed Hassan"
  }
}

// =============================================
// SALES MANAGEMENT COLLECTIONS
// =============================================

// sales collection
{
  "_id": ObjectId("..."),
  "saleNumber": "SAL-2024-001",
  "customer": {
    "customerId": ObjectId("..."),
    "customerName": "Amina Mohamed",
    "phone": "+252-61-123456"
  },
  "warehouse": {
    "warehouseId": ObjectId("..."),
    "warehouseName": "Main Warehouse"
  },
  "saleDateTime": {
    "date": ISODate("2024-01-02T00:00:00Z"),
    "time": "14:30:00"
  },
  "status": "COMPLETED", // PENDING, COMPLETED, RETURNED, CANCELLED
  "items": [
    {
      "_id": ObjectId("..."),
      "product": {
        "productId": ObjectId("..."),
        "productName": "Premium Rice Basmati",
        "sku": "RICE001"
      },
      "quantity": 2.0,
      "unitPrice": 35.00,
      "discountAmount": 0.00,
      "totalPrice": 70.00,
      "costPrice": 25.50,
      "profit": 19.00,
      "batchNumber": "BATCH001"
    }
  ],
  "totals": {
    "subtotal": 70.00,
    "taxAmount": 10.50,
    "discountAmount": 0.00,
    "totalAmount": 80.50
  },
  "payment": {
    "paidAmount": 80.50,
    "changeAmount": 0.00,
    "paymentMethod": "CASH", // CASH, CARD, MOBILE, CREDIT, MIXED
    "paymentStatus": "PAID" // UNPAID, PAID, PARTIAL
  },
  "notes": "Regular customer purchase",
  "createdAt": ISODate("2024-01-02T14:30:00Z"),
  "updatedAt": ISODate("2024-01-02T14:30:00Z"),
  "createdBy": {
    "userId": ObjectId("..."),
    "userName": "Fatima Ali"
  }
}

// =============================================
// FINANCIAL MANAGEMENT COLLECTIONS
// =============================================

// income collection
{
  "_id": ObjectId("..."),
  "title": "Rice Sales Revenue",
  "amount": 80.50,
  "category": "SALES_REVENUE",
  "description": "Revenue from rice sales",
  "incomeDate": ISODate("2024-01-02T00:00:00Z"),
  "reference": {
    "type": "SALE",
    "referenceId": ObjectId("..."),
    "referenceNumber": "SAL-2024-001"
  },
  "createdAt": ISODate("2024-01-02T00:00:00Z"),
  "createdBy": {
    "userId": ObjectId("..."),
    "userName": "Fatima Ali"
  }
}

// expenses collection
{
  "_id": ObjectId("..."),
  "title": "Office Rent Payment",
  "amount": 1500.00,
  "category": "RENT",
  "description": "Monthly office rent for January 2024",
  "expenseDate": ISODate("2024-01-01T00:00:00Z"),
  "reference": {
    "type": "OTHER",
    "referenceId": null,
    "referenceNumber": "RENT-JAN-2024"
  },
  "receiptImage": "/uploads/receipts/rent_jan_2024.jpg",
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "createdBy": {
    "userId": ObjectId("..."),
    "userName": "Ahmed Hassan"
  }
}

// =============================================
// QR CODE SYSTEM COLLECTIONS
// =============================================

// qrCodes collection
{
  "_id": ObjectId("..."),
  "product": {
    "productId": ObjectId("..."),
    "productName": "Premium Rice Basmati",
    "sku": "RICE001"
  },
  "qrCode": "QR-RICE001-2024-001",
  "qrType": "PRODUCT_INFO", // PRODUCT_INFO, INVENTORY, CHECKOUT
  "metadata": {
    "productUrl": "/products/rice001",
    "quickActions": ["VIEW_DETAILS", "ADD_TO_CART", "CHECK_STOCK"]
  },
  "isActive": true,
  "analytics": {
    "scanCount": 25,
    "lastScanned": ISODate("2024-01-02T10:15:00Z")
  },
  "createdAt": ISODate("2024-01-01T00:00:00Z"),
  "createdBy": {
    "userId": ObjectId("..."),
    "userName": "Ahmed Hassan"
  }
}

// =============================================
// NOTIFICATION SYSTEM COLLECTIONS
// =============================================

// notifications collection
{
  "_id": ObjectId("..."),
  "user": {
    "userId": ObjectId("..."),
    "userName": "Ahmed Hassan"
  },
  "title": "Low Stock Alert",
  "message": "Premium Rice Basmati is running low (Current Stock: 5 kg, Minimum: 10 kg)",
  "type": "WARNING", // INFO, WARNING, ERROR, SUCCESS
  "category": "STOCK", // STOCK, SALE, PURCHASE, SYSTEM, USER
  "isRead": false,
  "readAt": null,
  "data": {
    "productId": ObjectId("..."),
    "productName": "Premium Rice Basmati",
    "currentStock": 5,
    "minimumStock": 10,
    "actionUrl": "/inventory/products/rice001"
  },
  "createdAt": ISODate("2024-01-02T09:00:00Z")
}

// =============================================
// AUDIT & LOGGING COLLECTIONS
// =============================================

// auditLogs collection
{
  "_id": ObjectId("..."),
  "user": {
    "userId": ObjectId("..."),
    "userName": "Ahmed Hassan"
  },
  "action": "CREATE_PRODUCT",
  "entity": {
    "type": "PRODUCT",
    "entityId": ObjectId("..."),
    "entityName": "Premium Rice Basmati"
  },
  "changes": {
    "oldValues": null,
    "newValues": {
      "name": "Premium Rice Basmati",
      "sku": "RICE001",
      "sellingPrice": 35.00
    }
  },
  "metadata": {
    "ipAddress": "*************",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  },
  "createdAt": ISODate("2024-01-01T00:00:00Z")
}

// =============================================
// SYSTEM CONFIGURATION COLLECTIONS
// =============================================

// systemSettings collection
{
  "_id": ObjectId("..."),
  "settingKey": "COMPANY_NAME",
  "settingValue": "KaydkaPro Business Solutions",
  "settingType": "STRING",
  "description": "Company name displayed in the application",
  "isPublic": true,
  "updatedAt": ISODate("2024-01-01T00:00:00Z"),
  "updatedBy": {
    "userId": ObjectId("..."),
    "userName": "Ahmed Hassan"
  }
}
````

### **MongoDB Indexes for Performance:**

```javascript
// =============================================
// PERFORMANCE OPTIMIZATION INDEXES
// =============================================

// Users collection indexes
db.users.createIndex({ username: 1 }, { unique: true });
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ isActive: 1 });

// Products collection indexes
db.products.createIndex({ sku: 1 }, { unique: true });
db.products.createIndex({ barcode: 1 }, { unique: true, sparse: true });
db.products.createIndex({ "category.categoryId": 1 });
db.products.createIndex({ isActive: 1 });
db.products.createIndex({ name: "text", description: "text" });

// Inventory collection indexes
db.inventory.createIndex(
    { "product.productId": 1, "warehouse.warehouseId": 1 },
    { unique: true }
);
db.inventory.createIndex({ "quantities.available": 1 });

// Sales collection indexes
db.sales.createIndex({ saleNumber: 1 }, { unique: true });
db.sales.createIndex({ "saleDateTime.date": -1 });
db.sales.createIndex({ "customer.customerId": 1 });
db.sales.createIndex({ "createdBy.userId": 1 });

// Stock movements collection indexes
db.stockMovements.createIndex({ "product.productId": 1, createdAt: -1 });
db.stockMovements.createIndex({ "warehouse.warehouseId": 1, createdAt: -1 });
db.stockMovements.createIndex({
    "reference.type": 1,
    "reference.referenceId": 1,
});

// Purchases collection indexes
db.purchases.createIndex({ purchaseNumber: 1 }, { unique: true });
db.purchases.createIndex({ "supplier.supplierId": 1 });
db.purchases.createIndex({ "dates.purchaseDate": -1 });

// Notifications collection indexes
db.notifications.createIndex({ "user.userId": 1, isRead: 1 });
db.notifications.createIndex({ createdAt: -1 });

// Audit logs collection indexes
db.auditLogs.createIndex({ "user.userId": 1, createdAt: -1 });
db.auditLogs.createIndex({ "entity.type": 1, "entity.entityId": 1 });
```

### **MongoDB Aggregation Pipelines:**

````javascript
// =============================================
// USEFUL AGGREGATION PIPELINES
// =============================================

// Product inventory summary
db.inventory.aggregate([
  {
    $lookup: {
      from: "products",
      localField: "product.productId",
      foreignField: "_id",
      as: "productDetails"
    }
  },
  {
    $group: {
      _id: "$product.productId",
      productName: { $first: "$product.productName" },
      totalStock: { $sum: "$quantities.available" },
      minStockLevel: { $first: "$productDetails.stockLevels.minStockLevel" },
      stockStatus: {
        $cond: {
          if: { $lte: [{ $sum: "$quantities.available" }, "$productDetails.stockLevels.minStockLevel"] },
          then: "LOW",
          else: "OK"
        }
      }
    }
  }
])

// Sales summary by date
db.sales.aggregate([
  {
    $match: {
      "status": "COMPLETED",
      "saleDateTime.date": {
        $gte: ISODate("2024-01-01T00:00:00Z"),
        $lte: ISODate("2024-01-31T23:59:59Z")
      }
    }
  },
  {
    $group: {
      _id: { $dateToString: { format: "%Y-%m-%d", date: "$saleDateTime.date" } },
      totalSales: { $sum: 1 },
      totalRevenue: { $sum: "$totals.totalAmount" },
      totalProfit: { $sum: { $sum: "$items.profit" } },
      averageSale: { $avg: "$totals.totalAmount" }
    }
  },
  { $sort: { "_id": 1 } }
])

// Low stock products
db.inventory.aggregate([
  {
    $lookup: {
      from: "products",
      localField: "product.productId",
      foreignField: "_id",
      as: "productDetails"
    }
  },
  {
    $match: {
      $expr: {
        $lte: ["$quantities.available", "$productDetails.stockLevels.minStockLevel"]
      }
    }
  },
  {
    $project: {
      productName: "$product.productName",
      sku: "$product.sku",
      currentStock: "$quantities.available",
      minStockLevel: "$productDetails.stockLevels.minStockLevel",
      reorderLevel: "$productDetails.stockLevels.reorderLevel"
    }
  }
])


## 📋 ADDITIONAL SPECIFICATIONS

### **Spring Boot MongoDB Configuration:**

```java
// MongoDB Configuration Class
@Configuration
@EnableMongoRepositories(basePackages = "com.kaydkapro.repository")
public class MongoConfig {

    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), "kaydkapro_db");
    }

    @Bean
    public MongoClient mongoClient() {
        return MongoClients.create("mongodb://localhost:27017");
    }
}
````

### **MongoDB Entity Examples:**

```java
// User Entity
@Document(collection = "users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @Id
    private String id;

    @Indexed(unique = true)
    private String username;

    @Indexed(unique = true)
    private String email;

    private String password;
    private String firstName;
    private String lastName;
    private String phone;
    private String profileImage;
    private Boolean isActive = true;
    private LocalDateTime lastLogin;

    private List<UserRole> roles = new ArrayList<>();

    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updatedAt = LocalDateTime.now();
    private String createdBy;
    private String updatedBy;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserRole {
        private String roleId;
        private String roleName;
        private LocalDateTime assignedAt;
        private String assignedBy;
    }
}

// Product Entity
@Document(collection = "products")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Product {
    @Id
    private String id;

    private String name;
    private String description;

    @Indexed(unique = true)
    private String sku;

    @Indexed(unique = true, sparse = true)
    private String barcode;

    private ProductCategory category;
    private ProductUnit unit;
    private ProductPricing pricing;
    private StockLevels stockLevels;

    private List<ProductImage> images = new ArrayList<>();
    private List<ProductVariant> variants = new ArrayList<>();

    private Boolean isActive = true;
    private Boolean hasVariants = false;
    private Boolean expiryTracking = false;
    private Boolean batchTracking = false;

    private List<String> tags = new ArrayList<>();

    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updatedAt = LocalDateTime.now();
    private String createdBy;
    private String updatedBy;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductCategory {
        private String categoryId;
        private String categoryName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductUnit {
        private String unitId;
        private String unitName;
        private String abbreviation;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductPricing {
        private BigDecimal costPrice;
        private BigDecimal sellingPrice;
        private BigDecimal taxRate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StockLevels {
        private Integer minStockLevel;
        private Integer maxStockLevel;
        private Integer reorderLevel;
    }
}

// Sales Entity
@Document(collection = "sales")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Sales {
    @Id
    private String id;

    @Indexed(unique = true)
    private String saleNumber;

    private SaleCustomer customer;
    private SaleWarehouse warehouse;
    private SaleDateTime saleDateTime;

    private String status; // PENDING, COMPLETED, RETURNED, CANCELLED

    private List<SaleItem> items = new ArrayList<>();
    private SaleTotals totals;
    private SalePayment payment;

    private String notes;

    private LocalDateTime createdAt = LocalDateTime.now();
    private LocalDateTime updatedAt = LocalDateTime.now();
    private SaleUser createdBy;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleCustomer {
        private String customerId;
        private String customerName;
        private String phone;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleItem {
        private String id;
        private SaleProduct product;
        private BigDecimal quantity;
        private BigDecimal unitPrice;
        private BigDecimal discountAmount;
        private BigDecimal totalPrice;
        private BigDecimal costPrice;
        private BigDecimal profit;
        private String batchNumber;
    }
}
```

### **MongoDB Repository Examples:**

```java
// User Repository
@Repository
public interface UserRepository extends MongoRepository<User, String> {
    Optional<User> findByUsername(String username);
    Optional<User> findByEmail(String email);
    List<User> findByIsActiveTrue();

    @Query("{'roles.roleName': ?0}")
    List<User> findByRole(String roleName);

    @Query("{'$text': {'$search': ?0}}")
    List<User> searchUsers(String searchTerm);
}

// Product Repository
@Repository
public interface ProductRepository extends MongoRepository<Product, String> {
    Optional<Product> findBySku(String sku);
    Optional<Product> findByBarcode(String barcode);

    List<Product> findByCategoryCategoryId(String categoryId);
    List<Product> findByIsActiveTrue();

    @Query("{'$text': {'$search': ?0}}")
    List<Product> searchProducts(String searchTerm);

    @Aggregation(pipeline = {
        "{ '$lookup': { 'from': 'inventory', 'localField': '_id', 'foreignField': 'product.productId', 'as': 'inventory' } }",
        "{ '$unwind': '$inventory' }",
        "{ '$match': { '$expr': { '$lte': ['$inventory.quantities.available', '$stockLevels.minStockLevel'] } } }"
    })
    List<Product> findLowStockProducts();
}

// Sales Repository
@Repository
public interface SalesRepository extends MongoRepository<Sales, String> {
    List<Sales> findByStatus(String status);
    List<Sales> findByCustomerCustomerId(String customerId);

    @Query("{'saleDateTime.date': {'$gte': ?0, '$lte': ?1}}")
    List<Sales> findByDateRange(LocalDate startDate, LocalDate endDate);

    @Aggregation(pipeline = {
        "{ '$match': { 'status': 'COMPLETED', 'saleDateTime.date': { '$gte': ?0, '$lte': ?1 } } }",
        "{ '$group': { '_id': { '$dateToString': { 'format': '%Y-%m-%d', 'date': '$saleDateTime.date' } }, 'totalSales': { '$sum': 1 }, 'totalRevenue': { '$sum': '$totals.totalAmount' } } }",
        "{ '$sort': { '_id': 1 } }"
    })
    List<DailySalesStats> getDailySalesStats(LocalDate startDate, LocalDate endDate);
}
```

### **MongoDB Service Examples:**

```java
// Product Service with MongoDB
@Service
@Transactional
public class ProductService {

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    public List<Product> getAllProducts() {
        return productRepository.findByIsActiveTrue();
    }

    public Product getProductById(String id) {
        return productRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));
    }

    public Product createProduct(ProductRequest request) {
        // Check if SKU already exists
        if (productRepository.findBySku(request.getSku()).isPresent()) {
            throw new BadRequestException("Product with SKU " + request.getSku() + " already exists");
        }

        Product product = Product.builder()
            .name(request.getName())
            .description(request.getDescription())
            .sku(request.getSku())
            .barcode(request.getBarcode())
            .category(ProductCategory.builder()
                .categoryId(request.getCategoryId())
                .categoryName(request.getCategoryName())
                .build())
            .pricing(ProductPricing.builder()
                .costPrice(request.getCostPrice())
                .sellingPrice(request.getSellingPrice())
                .taxRate(request.getTaxRate())
                .build())
            .stockLevels(StockLevels.builder()
                .minStockLevel(request.getMinStockLevel())
                .maxStockLevel(request.getMaxStockLevel())
                .reorderLevel(request.getReorderLevel())
                .build())
            .isActive(true)
            .createdAt(LocalDateTime.now())
            .createdBy(getCurrentUserId())
            .build();

        Product savedProduct = productRepository.save(product);

        // Initialize inventory for all warehouses
        initializeInventoryForProduct(savedProduct.getId());

        return savedProduct;
    }

    public List<Product> searchProducts(String searchTerm) {
        return productRepository.searchProducts(searchTerm);
    }

    public List<Product> getLowStockProducts() {
        return productRepository.findLowStockProducts();
    }
}
```

### **MongoDB Configuration Properties:**

```properties
# MongoDB Configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=kaydkapro_db
spring.data.mongodb.auto-index-creation=true

# Optional: MongoDB Authentication
# spring.data.mongodb.username=kaydkapro_user
# spring.data.mongodb.password=secure_password
# spring.data.mongodb.authentication-database=admin

# Connection Pool Settings
spring.data.mongodb.connection-per-host=100
spring.data.mongodb.threads-allowed-to-block-for-connection-multiplier=5
spring.data.mongodb.connect-timeout=10000
spring.data.mongodb.socket-timeout=0
spring.data.mongodb.socket-keep-alive=true
```

### **Application Properties:**

Configure all necessary properties for database, security, AI, email, and other integrations.

### **Docker Configuration:**

Provide Dockerfile and docker-compose.yml for easy deployment with MongoDB.

```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/kaydkapro-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```yaml
# docker-compose.yml
version: "3.8"

services:
    mongodb:
        image: mongo:latest
        container_name: kaydkapro-mongodb
        restart: always
        ports:
            - "27017:27017"
        environment:
            MONGO_INITDB_ROOT_USERNAME: admin
            MONGO_INITDB_ROOT_PASSWORD: kaydkapro2024
            MONGO_INITDB_DATABASE: kaydkapro_db
        volumes:
            - mongodb_data:/data/db
            - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro

    app:
        build: .
        container_name: kaydkapro-app
        restart: always
        ports:
            - "8080:8080"
        environment:
            SPRING_DATA_MONGODB_HOST: mongodb
            SPRING_DATA_MONGODB_PORT: 27017
            SPRING_DATA_MONGODB_DATABASE: kaydkapro_db
            SPRING_DATA_MONGODB_USERNAME: kaydkapro_user
            SPRING_DATA_MONGODB_PASSWORD: kaydkapro2024
        depends_on:
            - mongodb

volumes:
    mongodb_data:
```

### **API Documentation:**

Generate OpenAPI/Swagger documentation for all endpoints.

### **Logging Configuration:**

Implement comprehensive logging with different levels and file rotation.

## 🚀 FINAL REQUIREMENTS

1. **Code Quality:** Clean, well-documented, production-ready code
2. **Architecture:** Follow Spring Boot best practices and design patterns
3. **Security:** Implement robust security measures
4. **Performance:** Optimize for high performance and scalability
5. **Maintainability:** Easy to understand and extend
6. **Testing:** Comprehensive test coverage
7. **Documentation:** Detailed README and API documentation

Create a COMPLETE, WORKING Spring Boot application that can handle a real business inventory management system with all the advanced features mentioned. This should be ENTERPRISE-LEVEL quality code that stands out from typical student projects.

MAKE IT LEGENDARY! 🔥👑

```

---

## 🎯 HOW TO USE THIS PROMPT

### **Step 1: Copy the Prompt**
Copy the entire prompt section above (between the triple backticks)

### **Step 2: Generate Code Modules**
Use these specific requests with your LLM:

#### **For Complete Project Setup:**
```

Use the KaydkaPro prompt above to generate the complete pom.xml file with all dependencies

```

#### **For Entity Generation:**
```

Use the KaydkaPro prompt above to generate all Entity classes with proper JPA annotations

```

#### **For Controller Generation:**
```

Use the KaydkaPro prompt above to generate AuthController.java with all endpoints

```

#### **For Service Layer:**
```

Use the KaydkaPro prompt above to generate ProductService.java with complete business logic

```

#### **For AI Integration:**
```

Use the KaydkaPro prompt above to generate AIService.java with Langchain4j integration

```

### **Step 3: Progressive Generation**
Generate code in this order:
1. **Project Structure & Dependencies** (pom.xml, application.properties)
2. **Entity Classes** (All JPA entities)
3. **Repository Layer** (All repository interfaces)
4. **Configuration Classes** (Security, WebSocket, AI config)
5. **Service Layer** (Business logic)
6. **Controller Layer** (REST endpoints)
7. **Utility Classes** (QR Code, Email templates, etc.)
8. **WebSocket Components** (Real-time features)

### **Step 4: Specific Feature Requests**
For advanced features, use these prompts:

#### **QR Code System:**
```

Generate complete QR Code implementation from the KaydkaPro prompt - QRCodeService, QRCodeController, and QRCodeUtil

```

#### **AI Email Generation:**
```

Generate AI-powered email service using Langchain4j from the KaydkaPro prompt for low stock alerts

```

#### **Real-time Dashboard:**
```

Generate WebSocket implementation for real-time dashboard updates from the KaydkaPro prompt

```

### **Step 5: Testing & Documentation**
```

Generate complete test suite for KaydkaPro project including unit tests, integration tests, and API documentation

```

## 🚀 PRO TIPS FOR MAXIMUM IMPACT

### **When Requesting Code:**
- Always reference the "KaydkaPro prompt above"
- Ask for specific files one at a time for detailed implementation
- Request complete implementations, not just skeleton code
- Ask for proper error handling and validation

### **For Best Results:**
- Generate backend first, then frontend
- Test each module as you generate it
- Ask for Docker configuration for easy deployment
- Request comprehensive documentation

### **Example Requests:**
```

"Using the KaydkaPro prompt above, generate a complete UserController.java with all CRUD operations, validation, and error handling"

"Generate the complete SecurityConfig.java from the KaydkaPro specifications with JWT authentication and role-based access control"

"Create the AIService.java implementation with Langchain4j integration for natural language queries and email generation"

```

## 🔥 READY TO BUILD SOMETHING LEGENDARY?

This prompt will generate **ENTERPRISE-LEVEL** Spring Boot code that will:
- ✅ Impress your teacher beyond belief
- ✅ Stand out from every other student project
- ✅ Demonstrate professional-grade architecture
- ✅ Include cutting-edge AI integration
- ✅ Show real-world business understanding
- ✅ Generate perfect scores

**Copy this markdown file, save it, and start generating your KING KONG backend!** 👑🚀

---

*"This isn't just a student project - this is a professional-grade business solution!"* 💪
```
