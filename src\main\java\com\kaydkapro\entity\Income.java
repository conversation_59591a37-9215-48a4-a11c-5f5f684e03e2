package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Income Entity - Represents income transactions
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "income")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Income {
    
    @Id
    private String id;
    
    @NotBlank(message = "Income title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    private String title;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Amount must be greater than 0")
    private BigDecimal amount;
    
    @NotBlank(message = "Category is required")
    @Size(max = 100, message = "Category must not exceed 100 characters")
    private String category;
    
    @Size(max = 500, message = "Description must not exceed 500 characters")
    private String description;
    
    @NotNull(message = "Income date is required")
    private LocalDate incomeDate;
    
    private IncomeReference reference;
    
    private String receiptImage;
    
    @Size(max = 500, message = "Notes must not exceed 500 characters")
    private String notes;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @NotNull(message = "Created by information is required")
    private IncomeUser createdBy;
    
    /**
     * Nested class for income reference
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IncomeReference {
        private String type; // SALE, SERVICE, INVESTMENT, OTHER
        private String referenceId;
        private String referenceNumber;
    }
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IncomeUser {
        private String userId;
        private String userName;
    }
    
    // Income category constants
    public static final String CATEGORY_SALES_REVENUE = "SALES_REVENUE";
    public static final String CATEGORY_SERVICE_REVENUE = "SERVICE_REVENUE";
    public static final String CATEGORY_INVESTMENT_INCOME = "INVESTMENT_INCOME";
    public static final String CATEGORY_RENTAL_INCOME = "RENTAL_INCOME";
    public static final String CATEGORY_INTEREST_INCOME = "INTEREST_INCOME";
    public static final String CATEGORY_OTHER_INCOME = "OTHER_INCOME";
    
    // Reference type constants
    public static final String REF_SALE = "SALE";
    public static final String REF_SERVICE = "SERVICE";
    public static final String REF_INVESTMENT = "INVESTMENT";
    public static final String REF_OTHER = "OTHER";
    
    // Helper methods
    public boolean isSalesRevenue() {
        return CATEGORY_SALES_REVENUE.equals(category);
    }
    
    public boolean hasReference() {
        return reference != null && reference.getReferenceId() != null;
    }
    
    public String getFormattedAmount() {
        return String.format("$%.2f", amount);
    }
}
