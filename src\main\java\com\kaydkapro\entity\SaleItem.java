package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * SaleItem Entity - Represents individual items in a sale
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "saleItems")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaleItem {
    
    @Id
    private String id;
    
    @NotBlank(message = "Sale ID is required")
    private String saleId;
    
    @NotNull(message = "Product information is required")
    private SaleItemProduct product;
    
    @NotNull(message = "Quantity is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Quantity must be greater than 0")
    private BigDecimal quantity;
    
    @NotNull(message = "Unit price is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Unit price must be greater than 0")
    private BigDecimal unitPrice;
    
    @DecimalMin(value = "0.0", message = "Discount amount must be non-negative")
    @Builder.Default
    private BigDecimal discountAmount = BigDecimal.ZERO;
    
    private BigDecimal totalPrice;
    private BigDecimal costPrice;
    private BigDecimal profit;
    private String batchNumber;
    
    /**
     * Nested class for product information in sale item
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaleItemProduct {
        private String productId;
        private String productName;
        private String sku;
        private String categoryName;
    }
    
    // Helper methods
    public BigDecimal calculateTotalPrice() {
        if (quantity != null && unitPrice != null) {
            BigDecimal total = quantity.multiply(unitPrice);
            if (discountAmount != null) {
                total = total.subtract(discountAmount);
            }
            this.totalPrice = total;
            return total;
        }
        return BigDecimal.ZERO;
    }
    
    public BigDecimal calculateProfit() {
        if (costPrice != null && totalPrice != null && quantity != null) {
            BigDecimal totalCost = costPrice.multiply(quantity);
            this.profit = totalPrice.subtract(totalCost);
            return this.profit;
        }
        return BigDecimal.ZERO;
    }
    
    public BigDecimal getDiscountPercentage() {
        if (unitPrice != null && discountAmount != null && unitPrice.compareTo(BigDecimal.ZERO) > 0) {
            return discountAmount.divide(unitPrice.multiply(quantity), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        return BigDecimal.ZERO;
    }
}
