package com.kaydkapro.dto.request;

import com.kaydkapro.entity.Product;
import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * Product Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class ProductRequest {
    
    @NotBlank(message = "Product name is required")
    @Size(max = 200, message = "Product name must not exceed 200 characters")
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;
    
    @NotBlank(message = "SKU is required")
    @Size(max = 50, message = "SKU must not exceed 50 characters")
    private String sku;
    
    @Size(max = 50, message = "Barcode must not exceed 50 characters")
    private String barcode;
    
    @NotNull(message = "Category is required")
    private CategoryInfo category;
    
    @NotBlank(message = "Unit is required")
    @Size(max = 20, message = "Unit must not exceed 20 characters")
    private String unit;
    
    @NotNull(message = "Pricing information is required")
    private PricingInfo pricing;
    
    private StockLevelsInfo stockLevels;
    
    private Boolean expiryTracking = false;
    
    private Boolean batchTracking = false;
    
    private Boolean hasVariants = false;
    
    private List<String> tags;
    
    private List<ImageInfo> images;
    
    @Data
    public static class CategoryInfo {
        @NotBlank(message = "Category ID is required")
        private String categoryId;
        
        @NotBlank(message = "Category name is required")
        private String categoryName;
    }
    
    @Data
    public static class PricingInfo {
        @NotNull(message = "Cost price is required")
        @DecimalMin(value = "0.0", message = "Cost price must be non-negative")
        private BigDecimal costPrice;
        
        @NotNull(message = "Selling price is required")
        @DecimalMin(value = "0.0", inclusive = false, message = "Selling price must be greater than 0")
        private BigDecimal sellingPrice;
        
        @DecimalMin(value = "0.0", message = "Discount price must be non-negative")
        private BigDecimal discountPrice;
        
        @Size(max = 10, message = "Currency must not exceed 10 characters")
        private String currency = "USD";
        
        @DecimalMin(value = "0.0", message = "Tax rate must be non-negative")
        @DecimalMax(value = "100.0", message = "Tax rate must not exceed 100%")
        private BigDecimal taxRate = BigDecimal.ZERO;
    }
    
    @Data
    public static class StockLevelsInfo {
        @DecimalMin(value = "0", message = "Minimum stock level must be non-negative")
        private Integer minStockLevel = 0;
        
        @DecimalMin(value = "0", message = "Maximum stock level must be non-negative")
        private Integer maxStockLevel = 1000;
        
        @DecimalMin(value = "0", message = "Reorder level must be non-negative")
        private Integer reorderLevel = 10;
        
        @DecimalMin(value = "0", message = "Reorder quantity must be non-negative")
        private Integer reorderQuantity = 50;
    }
    
    @Data
    public static class ImageInfo {
        @NotBlank(message = "Image URL is required")
        private String imageUrl;
        
        private String altText;
        
        private Boolean isPrimary = false;
        
        private Integer sortOrder = 1;
    }
    
    /**
     * Convert DTO to Entity
     */
    public Product toEntity() {
        Product product = Product.builder()
                .name(this.name)
                .description(this.description)
                .sku(this.sku)
                .barcode(this.barcode)
                .unit(this.unit)
                .expiryTracking(this.expiryTracking)
                .batchTracking(this.batchTracking)
                .hasVariants(this.hasVariants)
                .tags(this.tags)
                .build();
        
        // Set category
        if (this.category != null) {
            product.setCategory(Product.ProductCategory.builder()
                    .categoryId(this.category.getCategoryId())
                    .categoryName(this.category.getCategoryName())
                    .build());
        }
        
        // Set pricing
        if (this.pricing != null) {
            product.setPricing(Product.ProductPricing.builder()
                    .costPrice(this.pricing.getCostPrice())
                    .sellingPrice(this.pricing.getSellingPrice())
                    .discountPrice(this.pricing.getDiscountPrice())
                    .currency(this.pricing.getCurrency())
                    .taxRate(this.pricing.getTaxRate())
                    .build());
        }
        
        // Set stock levels
        if (this.stockLevels != null) {
            product.setStockLevels(Product.ProductStockLevels.builder()
                    .minStockLevel(this.stockLevels.getMinStockLevel())
                    .maxStockLevel(this.stockLevels.getMaxStockLevel())
                    .reorderLevel(this.stockLevels.getReorderLevel())
                    .reorderQuantity(this.stockLevels.getReorderQuantity())
                    .build());
        }
        
        // Set images
        if (this.images != null && !this.images.isEmpty()) {
            List<Product.ProductImage> productImages = this.images.stream()
                    .map(img -> Product.ProductImage.builder()
                            .imageUrl(img.getImageUrl())
                            .altText(img.getAltText())
                            .isPrimary(img.getIsPrimary())
                            .sortOrder(img.getSortOrder())
                            .build())
                    .toList();
            product.setImages(productImages);
        }
        
        return product;
    }
}
