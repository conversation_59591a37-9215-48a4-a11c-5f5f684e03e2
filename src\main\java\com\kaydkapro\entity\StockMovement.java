package com.kaydkapro.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * StockMovement Entity - Tracks all inventory movements
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Document(collection = "stockMovements")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockMovement {
    
    @Id
    private String id;
    
    @NotNull(message = "Product information is required")
    private MovementProduct product;
    
    @NotNull(message = "Warehouse information is required")
    private MovementWarehouse warehouse;
    
    @NotBlank(message = "Movement type is required")
    private String movementType; // IN, OUT, TRANSFER, ADJUSTMENT
    
    @NotNull(message = "Quantity is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Quantity must be greater than 0")
    private BigDecimal quantity;
    
    private BigDecimal unitCost;
    
    private MovementReference reference;
    
    private MovementBatchInfo batchInfo;
    
    private String notes;
    
    @CreatedDate
    private LocalDateTime createdAt;
    
    @NotNull(message = "Created by information is required")
    private MovementUser createdBy;
    
    /**
     * Nested class for product information in movement
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MovementProduct {
        private String productId;
        private String productName;
        private String sku;
    }
    
    /**
     * Nested class for warehouse information in movement
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MovementWarehouse {
        private String warehouseId;
        private String warehouseName;
        private String warehouseCode;
    }
    
    /**
     * Nested class for movement reference
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MovementReference {
        private String type; // PURCHASE, SALE, TRANSFER, ADJUSTMENT, RETURN
        private String referenceId;
        private String referenceNumber;
    }
    
    /**
     * Nested class for batch information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MovementBatchInfo {
        private String batchNumber;
        private LocalDate expiryDate;
        private LocalDate manufacturingDate;
    }
    
    /**
     * Nested class for user information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MovementUser {
        private String userId;
        private String userName;
    }
    
    // Movement type constants
    public static final String MOVEMENT_IN = "IN";
    public static final String MOVEMENT_OUT = "OUT";
    public static final String MOVEMENT_TRANSFER = "TRANSFER";
    public static final String MOVEMENT_ADJUSTMENT = "ADJUSTMENT";
    
    // Reference type constants
    public static final String REF_PURCHASE = "PURCHASE";
    public static final String REF_SALE = "SALE";
    public static final String REF_TRANSFER = "TRANSFER";
    public static final String REF_ADJUSTMENT = "ADJUSTMENT";
    public static final String REF_RETURN = "RETURN";
    
    // Helper methods
    public BigDecimal getTotalValue() {
        if (unitCost != null && quantity != null) {
            return unitCost.multiply(quantity);
        }
        return BigDecimal.ZERO;
    }
    
    public boolean isInbound() {
        return MOVEMENT_IN.equals(movementType);
    }
    
    public boolean isOutbound() {
        return MOVEMENT_OUT.equals(movementType);
    }
    
    public boolean isTransfer() {
        return MOVEMENT_TRANSFER.equals(movementType);
    }
    
    public boolean isAdjustment() {
        return MOVEMENT_ADJUSTMENT.equals(movementType);
    }
}
