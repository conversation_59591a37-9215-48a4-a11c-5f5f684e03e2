package com.kaydkapro.controller;

import com.kaydkapro.dto.request.StockAdjustmentRequest;
import com.kaydkapro.dto.request.StockTransferRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.entity.Inventory;
import com.kaydkapro.repository.InventoryRepository;
import com.kaydkapro.service.InventoryService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Inventory Controller - REST API endpoints for inventory management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/inventory")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class InventoryController {
    
    private final InventoryService inventoryService;
    
    /**
     * Get inventory by product and warehouse
     */
    @GetMapping("/product/{productId}/warehouse/{warehouseId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Inventory>> getInventory(@PathVariable String productId,
                                                              @PathVariable String warehouseId) {
        try {
            Optional<Inventory> inventory = inventoryService.getInventory(productId, warehouseId);
            
            if (inventory.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("Inventory retrieved successfully", inventory.get()));
            } else {
                return ResponseEntity.ok(ApiResponse.success("No inventory found", null));
            }
            
        } catch (Exception e) {
            log.error("Failed to get inventory for product {} in warehouse {}: {}", productId, warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get all inventory for a product across all warehouses
     */
    @GetMapping("/product/{productId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Inventory>>> getProductInventory(@PathVariable String productId) {
        try {
            List<Inventory> inventoryList = inventoryService.getProductInventory(productId);
            return ResponseEntity.ok(ApiResponse.success("Product inventory retrieved successfully", inventoryList));
            
        } catch (Exception e) {
            log.error("Failed to get product inventory for {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get all inventory in a warehouse
     */
    @GetMapping("/warehouse/{warehouseId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Inventory>>> getWarehouseInventory(@PathVariable String warehouseId) {
        try {
            List<Inventory> inventoryList = inventoryService.getWarehouseInventory(warehouseId);
            return ResponseEntity.ok(ApiResponse.success("Warehouse inventory retrieved successfully", inventoryList));
            
        } catch (Exception e) {
            log.error("Failed to get warehouse inventory for {}: {}", warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Add stock to inventory
     */
    @PostMapping("/add-stock")
    @PreAuthorize("hasRole('STOREKEEPER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Inventory>> addStock(@RequestParam String productId,
                                                          @RequestParam String warehouseId,
                                                          @RequestParam BigDecimal quantity,
                                                          @RequestParam BigDecimal unitCost,
                                                          @RequestParam(defaultValue = "MANUAL") String referenceType,
                                                          @RequestParam(required = false) String referenceId,
                                                          Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Inventory inventory = inventoryService.addStock(
                productId, warehouseId, quantity, unitCost,
                referenceType, referenceId, userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Stock added successfully", inventory));
            
        } catch (Exception e) {
            log.error("Failed to add stock: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Remove stock from inventory
     */
    @PostMapping("/remove-stock")
    @PreAuthorize("hasRole('STOREKEEPER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Inventory>> removeStock(@RequestParam String productId,
                                                             @RequestParam String warehouseId,
                                                             @RequestParam BigDecimal quantity,
                                                             @RequestParam(defaultValue = "MANUAL") String referenceType,
                                                             @RequestParam(required = false) String referenceId,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Inventory inventory = inventoryService.removeStock(
                productId, warehouseId, quantity,
                referenceType, referenceId, userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Stock removed successfully", inventory));
            
        } catch (Exception e) {
            log.error("Failed to remove stock: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Transfer stock between warehouses
     */
    @PostMapping("/transfer-stock")
    @PreAuthorize("hasRole('STOREKEEPER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<InventoryService.TransferResult>> transferStock(
            @Valid @RequestBody StockTransferRequest transferRequest,
            Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            InventoryService.TransferResult result = inventoryService.transferStock(
                transferRequest.getProductId(),
                transferRequest.getFromWarehouseId(),
                transferRequest.getToWarehouseId(),
                transferRequest.getQuantity(),
                userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Stock transferred successfully", result));
            
        } catch (Exception e) {
            log.error("Failed to transfer stock: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Adjust stock (for corrections)
     */
    @PostMapping("/adjust-stock")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Inventory>> adjustStock(@Valid @RequestBody StockAdjustmentRequest adjustmentRequest,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Inventory inventory = inventoryService.adjustStock(
                adjustmentRequest.getProductId(),
                adjustmentRequest.getWarehouseId(),
                adjustmentRequest.getNewQuantity(),
                adjustmentRequest.getReason(),
                userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Stock adjusted successfully", inventory));
            
        } catch (Exception e) {
            log.error("Failed to adjust stock: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Reserve stock for sales
     */
    @PostMapping("/reserve-stock")
    @PreAuthorize("hasRole('CASHIER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Inventory>> reserveStock(@RequestParam String productId,
                                                              @RequestParam String warehouseId,
                                                              @RequestParam BigDecimal quantity,
                                                              Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Inventory inventory = inventoryService.reserveStock(
                productId, warehouseId, quantity, userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Stock reserved successfully", inventory));
            
        } catch (Exception e) {
            log.error("Failed to reserve stock: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Release reserved stock
     */
    @PostMapping("/release-stock")
    @PreAuthorize("hasRole('CASHIER') or hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Inventory>> releaseReservedStock(@RequestParam String productId,
                                                                      @RequestParam String warehouseId,
                                                                      @RequestParam BigDecimal quantity,
                                                                      Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Inventory inventory = inventoryService.releaseReservedStock(
                productId, warehouseId, quantity, userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Reserved stock released successfully", inventory));
            
        } catch (Exception e) {
            log.error("Failed to release reserved stock: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get low stock inventory
     */
    @GetMapping("/low-stock")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Inventory>>> getLowStockInventory() {
        try {
            List<Inventory> lowStockItems = inventoryService.getLowStockInventory();
            return ResponseEntity.ok(ApiResponse.success("Low stock inventory retrieved", lowStockItems));
            
        } catch (Exception e) {
            log.error("Failed to get low stock inventory: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get out of stock inventory
     */
    @GetMapping("/out-of-stock")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Inventory>>> getOutOfStockInventory() {
        try {
            List<Inventory> outOfStockItems = inventoryService.getOutOfStockInventory();
            return ResponseEntity.ok(ApiResponse.success("Out of stock inventory retrieved", outOfStockItems));
            
        } catch (Exception e) {
            log.error("Failed to get out of stock inventory: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Calculate total inventory value
     */
    @GetMapping("/total-value")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BigDecimal>> getTotalInventoryValue() {
        try {
            BigDecimal totalValue = inventoryService.calculateTotalInventoryValue();
            return ResponseEntity.ok(ApiResponse.success("Total inventory value calculated", totalValue));
            
        } catch (Exception e) {
            log.error("Failed to calculate total inventory value: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get inventory summary by warehouse
     */
    @GetMapping("/summary/warehouse")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<InventoryRepository.InventorySummary>>> getInventorySummaryByWarehouse() {
        try {
            List<InventoryRepository.InventorySummary> summary = inventoryService.getInventorySummaryByWarehouse();
            return ResponseEntity.ok(ApiResponse.success("Inventory summary by warehouse retrieved", summary));
            
        } catch (Exception e) {
            log.error("Failed to get inventory summary by warehouse: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
