package com.kaydkapro.controller;

import com.kaydkapro.dto.request.QRCodeRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.QRCode;
import com.kaydkapro.repository.QRCodeRepository;
import com.kaydkapro.service.QRCodeService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * QR Code Controller - REST API endpoints for QR code management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/qrcodes")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class QRCodeController {
    
    private final QRCodeService qrCodeService;
    
    /**
     * Generate QR code for product
     */
    @PostMapping("/product/{productId}")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<QRCode>> generateProductQRCode(@PathVariable String productId,
                                                                    @RequestParam(required = false) String warehouseId,
                                                                    Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            QRCode qrCode = qrCodeService.generateProductQRCode(productId, warehouseId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("QR code generated successfully", qrCode));
            
        } catch (Exception e) {
            log.error("Failed to generate QR code for product {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate QR code for inventory
     */
    @PostMapping("/inventory")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<QRCode>> generateInventoryQRCode(@RequestParam String productId,
                                                                      @RequestParam String warehouseId,
                                                                      Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            QRCode qrCode = qrCodeService.generateInventoryQRCode(productId, warehouseId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Inventory QR code generated successfully", qrCode));
            
        } catch (Exception e) {
            log.error("Failed to generate inventory QR code: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Generate custom QR code
     */
    @PostMapping("/custom")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<QRCode>> generateCustomQRCode(@Valid @RequestBody QRCodeRequest qrCodeRequest,
                                                                   Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            QRCode qrCode = qrCodeService.generateCustomQRCode(
                qrCodeRequest.getType(),
                qrCodeRequest.getData(),
                qrCodeRequest.getTitle(),
                qrCodeRequest.getDescription(),
                userPrincipal.getId()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Custom QR code generated successfully", qrCode));
            
        } catch (Exception e) {
            log.error("Failed to generate custom QR code: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Scan QR code
     */
    @PostMapping("/scan")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<QRCodeService.ScanResult>> scanQRCode(@RequestParam String qrCodeData,
                                                                           Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            QRCodeService.ScanResult scanResult = qrCodeService.scanQRCode(qrCodeData, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("QR code scanned successfully", scanResult));
            
        } catch (Exception e) {
            log.error("Failed to scan QR code: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get QR code by ID
     */
    @GetMapping("/{qrCodeId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<QRCode>> getQRCode(@PathVariable String qrCodeId) {
        try {
            QRCode qrCode = qrCodeService.getQRCodeById(qrCodeId);
            return ResponseEntity.ok(ApiResponse.success("QR code retrieved successfully", qrCode));
            
        } catch (Exception e) {
            log.error("Failed to get QR code {}: {}", qrCodeId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("QR code not found"));
        }
    }
    
    /**
     * Get QR code by data
     */
    @GetMapping("/data/{qrCodeData}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<QRCode>> getQRCodeByData(@PathVariable String qrCodeData) {
        try {
            QRCode qrCode = qrCodeService.getQRCodeByData(qrCodeData);
            return ResponseEntity.ok(ApiResponse.success("QR code retrieved successfully", qrCode));
            
        } catch (Exception e) {
            log.error("Failed to get QR code by data {}: {}", qrCodeData, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("QR code not found"));
        }
    }
    
    /**
     * Get all QR codes with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<QRCode>>> getQRCodes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<QRCode> qrCodePage = qrCodeService.getQRCodes(pageable);
            PageResponse<QRCode> pageResponse = PageResponse.of(qrCodePage);
            
            return ResponseEntity.ok(ApiResponse.success("QR codes retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get QR codes: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get QR codes by type
     */
    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<QRCode>>> getQRCodesByType(@PathVariable String type) {
        try {
            List<QRCode> qrCodes = qrCodeService.getQRCodesByType(type);
            return ResponseEntity.ok(ApiResponse.success("QR codes retrieved successfully", qrCodes));
            
        } catch (Exception e) {
            log.error("Failed to get QR codes by type {}: {}", type, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get QR codes by product
     */
    @GetMapping("/product/{productId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<QRCode>>> getQRCodesByProduct(@PathVariable String productId) {
        try {
            List<QRCode> qrCodes = qrCodeService.getQRCodesByProduct(productId);
            return ResponseEntity.ok(ApiResponse.success("Product QR codes retrieved successfully", qrCodes));
            
        } catch (Exception e) {
            log.error("Failed to get QR codes for product {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get active QR codes
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<QRCode>>> getActiveQRCodes() {
        try {
            List<QRCode> qrCodes = qrCodeService.getActiveQRCodes();
            return ResponseEntity.ok(ApiResponse.success("Active QR codes retrieved successfully", qrCodes));
            
        } catch (Exception e) {
            log.error("Failed to get active QR codes: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Bulk generate QR codes for products
     */
    @PostMapping("/bulk/products")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<QRCode>>> bulkGenerateProductQRCodes(@RequestBody List<String> productIds,
                                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            List<QRCode> qrCodes = qrCodeService.bulkGenerateProductQRCodes(productIds, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Bulk QR codes generated successfully", qrCodes));
            
        } catch (Exception e) {
            log.error("Failed to bulk generate QR codes: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Toggle QR code status (activate/deactivate)
     */
    @PatchMapping("/{qrCodeId}/status")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<QRCode>> toggleQRCodeStatus(@PathVariable String qrCodeId,
                                                                 @RequestParam boolean isActive,
                                                                 Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            QRCode qrCode = qrCodeService.toggleQRCodeStatus(qrCodeId, isActive, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("QR code status updated", qrCode));
            
        } catch (Exception e) {
            log.error("Failed to toggle QR code status {}: {}", qrCodeId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete QR code
     */
    @DeleteMapping("/{qrCodeId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteQRCode(@PathVariable String qrCodeId,
                                                           Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            qrCodeService.deleteQRCode(qrCodeId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("QR code deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete QR code {}: {}", qrCodeId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get QR code scan history
     */
    @GetMapping("/{qrCodeId}/scan-history")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<QRCodeRepository.QRCodeScanHistory>>> getQRCodeScanHistory(
            @PathVariable String qrCodeId) {
        try {
            List<QRCodeRepository.QRCodeScanHistory> scanHistory = qrCodeService.getQRCodeScanHistory(qrCodeId);
            return ResponseEntity.ok(ApiResponse.success("QR code scan history retrieved", scanHistory));
            
        } catch (Exception e) {
            log.error("Failed to get QR code scan history for {}: {}", qrCodeId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get QR code statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<QRCodeService.QRCodeStats>> getQRCodeStatistics() {
        try {
            QRCodeService.QRCodeStats stats = qrCodeService.getQRCodeStats();
            return ResponseEntity.ok(ApiResponse.success("QR code statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get QR code statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get most scanned QR codes
     */
    @GetMapping("/most-scanned")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<QRCodeRepository.QRCodeScanStats>>> getMostScannedQRCodes(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<QRCodeRepository.QRCodeScanStats> mostScanned = qrCodeService.getMostScannedQRCodes(limit);
            return ResponseEntity.ok(ApiResponse.success("Most scanned QR codes retrieved", mostScanned));
            
        } catch (Exception e) {
            log.error("Failed to get most scanned QR codes: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
