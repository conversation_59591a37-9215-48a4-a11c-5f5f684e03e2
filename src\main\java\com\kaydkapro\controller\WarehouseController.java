package com.kaydkapro.controller;

import com.kaydkapro.dto.request.WarehouseRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.Warehouse;
import com.kaydkapro.service.WarehouseService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Warehouse Controller - REST API endpoints for warehouse management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/warehouses")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class WarehouseController {
    
    private final WarehouseService warehouseService;
    
    /**
     * Create a new warehouse
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Warehouse>> createWarehouse(@Valid @RequestBody WarehouseRequest warehouseRequest,
                                                                 Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Warehouse warehouse = warehouseRequest.toEntity();
            Warehouse createdWarehouse = warehouseService.createWarehouse(warehouse, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Warehouse created successfully", createdWarehouse));
            
        } catch (Exception e) {
            log.error("Failed to create warehouse: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update an existing warehouse
     */
    @PutMapping("/{warehouseId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Warehouse>> updateWarehouse(@PathVariable String warehouseId,
                                                                 @Valid @RequestBody WarehouseRequest warehouseRequest,
                                                                 Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Warehouse warehouseUpdates = warehouseRequest.toEntity();
            Warehouse updatedWarehouse = warehouseService.updateWarehouse(warehouseId, warehouseUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Warehouse updated successfully", updatedWarehouse));
            
        } catch (Exception e) {
            log.error("Failed to update warehouse {}: {}", warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get warehouse by ID
     */
    @GetMapping("/{warehouseId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Warehouse>> getWarehouse(@PathVariable String warehouseId) {
        try {
            Warehouse warehouse = warehouseService.getWarehouseById(warehouseId);
            return ResponseEntity.ok(ApiResponse.success("Warehouse retrieved successfully", warehouse));
            
        } catch (Exception e) {
            log.error("Failed to get warehouse {}: {}", warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Warehouse not found"));
        }
    }
    
    /**
     * Get warehouse by code
     */
    @GetMapping("/code/{warehouseCode}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Warehouse>> getWarehouseByCode(@PathVariable String warehouseCode) {
        try {
            Warehouse warehouse = warehouseService.getWarehouseByCode(warehouseCode);
            return ResponseEntity.ok(ApiResponse.success("Warehouse retrieved successfully", warehouse));
            
        } catch (Exception e) {
            log.error("Failed to get warehouse by code {}: {}", warehouseCode, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Warehouse not found"));
        }
    }
    
    /**
     * Get all warehouses with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Warehouse>>> getWarehouses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Warehouse> warehousePage = warehouseService.getWarehouses(pageable);
            PageResponse<Warehouse> pageResponse = PageResponse.of(warehousePage);
            
            return ResponseEntity.ok(ApiResponse.success("Warehouses retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get warehouses: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get active warehouses
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Warehouse>>> getActiveWarehouses() {
        try {
            List<Warehouse> warehouses = warehouseService.getActiveWarehouses();
            return ResponseEntity.ok(ApiResponse.success("Active warehouses retrieved successfully", warehouses));
            
        } catch (Exception e) {
            log.error("Failed to get active warehouses: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search warehouses
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Warehouse>>> searchWarehouses(@RequestParam String query) {
        try {
            List<Warehouse> warehouses = warehouseService.searchWarehouses(query);
            return ResponseEntity.ok(ApiResponse.success("Warehouse search completed", warehouses));
            
        } catch (Exception e) {
            log.error("Failed to search warehouses: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get warehouses by type
     */
    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Warehouse>>> getWarehousesByType(@PathVariable String type) {
        try {
            List<Warehouse> warehouses = warehouseService.getWarehousesByType(type);
            return ResponseEntity.ok(ApiResponse.success("Warehouses retrieved successfully", warehouses));
            
        } catch (Exception e) {
            log.error("Failed to get warehouses by type {}: {}", type, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get warehouses by location
     */
    @GetMapping("/location")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Warehouse>>> getWarehousesByLocation(@RequestParam String city,
                                                                               @RequestParam(required = false) String state,
                                                                               @RequestParam(required = false) String country) {
        try {
            List<Warehouse> warehouses = warehouseService.getWarehousesByLocation(city, state, country);
            return ResponseEntity.ok(ApiResponse.success("Warehouses retrieved successfully", warehouses));
            
        } catch (Exception e) {
            log.error("Failed to get warehouses by location: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Toggle warehouse status (activate/deactivate)
     */
    @PatchMapping("/{warehouseId}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Warehouse>> toggleWarehouseStatus(@PathVariable String warehouseId,
                                                                       @RequestParam boolean isActive,
                                                                       Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Warehouse warehouse = warehouseService.toggleWarehouseStatus(warehouseId, isActive, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Warehouse status updated", warehouse));
            
        } catch (Exception e) {
            log.error("Failed to toggle warehouse status {}: {}", warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Set warehouse as default
     */
    @PatchMapping("/{warehouseId}/default")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Warehouse>> setDefaultWarehouse(@PathVariable String warehouseId,
                                                                     Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Warehouse warehouse = warehouseService.setDefaultWarehouse(warehouseId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Default warehouse set", warehouse));
            
        } catch (Exception e) {
            log.error("Failed to set default warehouse {}: {}", warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get default warehouse
     */
    @GetMapping("/default")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Warehouse>> getDefaultWarehouse() {
        try {
            Warehouse warehouse = warehouseService.getDefaultWarehouse();
            return ResponseEntity.ok(ApiResponse.success("Default warehouse retrieved", warehouse));
            
        } catch (Exception e) {
            log.error("Failed to get default warehouse: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("No default warehouse found"));
        }
    }
    
    /**
     * Get warehouse capacity utilization
     */
    @GetMapping("/{warehouseId}/capacity")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<WarehouseService.WarehouseCapacity>> getWarehouseCapacity(@PathVariable String warehouseId) {
        try {
            WarehouseService.WarehouseCapacity capacity = warehouseService.getWarehouseCapacity(warehouseId);
            return ResponseEntity.ok(ApiResponse.success("Warehouse capacity retrieved", capacity));
            
        } catch (Exception e) {
            log.error("Failed to get warehouse capacity {}: {}", warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get warehouse statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<WarehouseService.WarehouseStats>> getWarehouseStatistics() {
        try {
            WarehouseService.WarehouseStats stats = warehouseService.getWarehouseStats();
            return ResponseEntity.ok(ApiResponse.success("Warehouse statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get warehouse statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete warehouse (soft delete)
     */
    @DeleteMapping("/{warehouseId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteWarehouse(@PathVariable String warehouseId,
                                                              Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            warehouseService.deleteWarehouse(warehouseId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Warehouse deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete warehouse {}: {}", warehouseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
