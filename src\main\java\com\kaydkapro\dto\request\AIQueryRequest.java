package com.kaydkapro.dto.request;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.Map;

/**
 * AI Query Request DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class AIQueryRequest {
    
    @NotBlank(message = "Query is required")
    @Size(max = 1000, message = "Query must not exceed 1000 characters")
    private String query;
    
    private Map<String, Object> context;
    
    @Size(max = 50, message = "Query type must not exceed 50 characters")
    private String queryType;
    
    private Boolean includeRecommendations = false;
    
    private Boolean includeInsights = false;
}
