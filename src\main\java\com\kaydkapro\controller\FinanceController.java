package com.kaydkapro.controller;

import com.kaydkapro.dto.request.IncomeRequest;
import com.kaydkapro.dto.request.ExpenseRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.Income;
import com.kaydkapro.entity.Expense;
import com.kaydkapro.repository.IncomeRepository;
import com.kaydkapro.repository.ExpenseRepository;
import com.kaydkapro.service.FinancialService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Finance Controller - REST API endpoints for financial management (Income & Expenses)
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/finance")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class FinanceController {
    
    private final FinancialService financialService;
    
    // ==================== INCOME MANAGEMENT ====================
    
    /**
     * Record new income
     */
    @PostMapping("/income")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Income>> recordIncome(@Valid @RequestBody IncomeRequest incomeRequest,
                                                           Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Income income = incomeRequest.toEntity();
            Income recordedIncome = financialService.recordIncome(income, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Income recorded successfully", recordedIncome));
            
        } catch (Exception e) {
            log.error("Failed to record income: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update income record
     */
    @PutMapping("/income/{incomeId}")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Income>> updateIncome(@PathVariable String incomeId,
                                                           @Valid @RequestBody IncomeRequest incomeRequest,
                                                           Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Income incomeUpdates = incomeRequest.toEntity();
            Income updatedIncome = financialService.updateIncome(incomeId, incomeUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Income updated successfully", updatedIncome));
            
        } catch (Exception e) {
            log.error("Failed to update income {}: {}", incomeId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get income by ID
     */
    @GetMapping("/income/{incomeId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Income>> getIncome(@PathVariable String incomeId) {
        try {
            Income income = financialService.getIncomeById(incomeId);
            return ResponseEntity.ok(ApiResponse.success("Income retrieved successfully", income));
            
        } catch (Exception e) {
            log.error("Failed to get income {}: {}", incomeId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Income not found"));
        }
    }
    
    /**
     * Get all income with pagination
     */
    @GetMapping("/income")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Income>>> getIncome(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "incomeDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Income> incomePage = financialService.getIncome(pageable);
            PageResponse<Income> pageResponse = PageResponse.of(incomePage);
            
            return ResponseEntity.ok(ApiResponse.success("Income retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get income: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get income by date range
     */
    @GetMapping("/income/date-range")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Income>>> getIncomeByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("incomeDate").descending());
            Page<Income> incomePage = financialService.getIncomeByDateRange(startDate, endDate, pageable);
            PageResponse<Income> pageResponse = PageResponse.of(incomePage);
            
            return ResponseEntity.ok(ApiResponse.success("Income retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get income by date range: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get income by category
     */
    @GetMapping("/income/category/{category}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Income>>> getIncomeByCategory(
            @PathVariable String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("incomeDate").descending());
            Page<Income> incomePage = financialService.getIncomeByCategory(category, pageable);
            PageResponse<Income> pageResponse = PageResponse.of(incomePage);
            
            return ResponseEntity.ok(ApiResponse.success("Income retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get income by category {}: {}", category, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search income
     */
    @GetMapping("/income/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Income>>> searchIncome(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("incomeDate").descending());
            Page<Income> incomePage = financialService.searchIncome(query, pageable);
            PageResponse<Income> pageResponse = PageResponse.of(incomePage);
            
            return ResponseEntity.ok(ApiResponse.success("Income search completed", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to search income: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete income
     */
    @DeleteMapping("/income/{incomeId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteIncome(@PathVariable String incomeId,
                                                           Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            financialService.deleteIncome(incomeId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Income deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete income {}: {}", incomeId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // ==================== EXPENSE MANAGEMENT ====================
    
    /**
     * Record new expense
     */
    @PostMapping("/expenses")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Expense>> recordExpense(@Valid @RequestBody ExpenseRequest expenseRequest,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Expense expense = expenseRequest.toEntity();
            Expense recordedExpense = financialService.recordExpense(expense, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Expense recorded successfully", recordedExpense));
            
        } catch (Exception e) {
            log.error("Failed to record expense: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update expense record
     */
    @PutMapping("/expenses/{expenseId}")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Expense>> updateExpense(@PathVariable String expenseId,
                                                             @Valid @RequestBody ExpenseRequest expenseRequest,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Expense expenseUpdates = expenseRequest.toEntity();
            Expense updatedExpense = financialService.updateExpense(expenseId, expenseUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Expense updated successfully", updatedExpense));
            
        } catch (Exception e) {
            log.error("Failed to update expense {}: {}", expenseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get expense by ID
     */
    @GetMapping("/expenses/{expenseId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Expense>> getExpense(@PathVariable String expenseId) {
        try {
            Expense expense = financialService.getExpenseById(expenseId);
            return ResponseEntity.ok(ApiResponse.success("Expense retrieved successfully", expense));
            
        } catch (Exception e) {
            log.error("Failed to get expense {}: {}", expenseId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Expense not found"));
        }
    }
    
    /**
     * Get all expenses with pagination
     */
    @GetMapping("/expenses")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Expense>>> getExpenses(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "expenseDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Expense> expensePage = financialService.getExpenses(pageable);
            PageResponse<Expense> pageResponse = PageResponse.of(expensePage);
            
            return ResponseEntity.ok(ApiResponse.success("Expenses retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get expenses: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // ==================== FINANCIAL ANALYTICS ====================
    
    /**
     * Get financial summary for period
     */
    @GetMapping("/summary")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<FinancialService.FinancialSummary>> getFinancialSummary(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            FinancialService.FinancialSummary summary = financialService.getFinancialSummary(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Financial summary retrieved", summary));
            
        } catch (Exception e) {
            log.error("Failed to get financial summary: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Calculate total income for period
     */
    @GetMapping("/income/total")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BigDecimal>> calculateTotalIncome(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            BigDecimal totalIncome = financialService.calculateTotalIncome(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Total income calculated", totalIncome));
            
        } catch (Exception e) {
            log.error("Failed to calculate total income: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Calculate total expenses for period
     */
    @GetMapping("/expenses/total")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BigDecimal>> calculateTotalExpenses(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            BigDecimal totalExpenses = financialService.calculateTotalExpenses(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Total expenses calculated", totalExpenses));
            
        } catch (Exception e) {
            log.error("Failed to calculate total expenses: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Calculate net profit for period
     */
    @GetMapping("/profit-loss")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<BigDecimal>> calculateNetProfit(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            BigDecimal netProfit = financialService.calculateNetProfit(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Net profit calculated", netProfit));
            
        } catch (Exception e) {
            log.error("Failed to calculate net profit: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get income by category statistics
     */
    @GetMapping("/income/statistics/category")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<IncomeRepository.IncomeCategoryStats>>> getIncomeByCategoryStats(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            List<IncomeRepository.IncomeCategoryStats> stats = financialService.getIncomeByCategoryStats(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Income category statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get income category statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get expenses by category statistics
     */
    @GetMapping("/expenses/statistics/category")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<List<ExpenseRepository.ExpenseCategoryStats>>> getExpensesByCategoryStats(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            List<ExpenseRepository.ExpenseCategoryStats> stats = financialService.getExpensesByCategoryStats(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Expense category statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get expense category statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get financial statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<FinancialService.FinancialStats>> getFinancialStatistics() {
        try {
            FinancialService.FinancialStats stats = financialService.getFinancialStats();
            return ResponseEntity.ok(ApiResponse.success("Financial statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get financial statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
