package com.kaydkapro.controller;

import com.kaydkapro.dto.request.NotificationRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.Notification;
import com.kaydkapro.service.NotificationService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Notification Controller - REST API endpoints for notification management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/notifications")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class NotificationController {
    
    private final NotificationService notificationService;
    
    /**
     * Send custom notification
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Notification>> sendNotification(@Valid @RequestBody NotificationRequest notificationRequest,
                                                                     Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Notification notification = notificationService.sendNotification(
                notificationRequest.getUserId(),
                notificationRequest.getTitle(),
                notificationRequest.getMessage(),
                notificationRequest.getType(),
                notificationRequest.getCategory()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Notification sent successfully", notification));
            
        } catch (Exception e) {
            log.error("Failed to send notification: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Broadcast notification to multiple users
     */
    @PostMapping("/broadcast")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<List<Notification>>> broadcastNotification(
            @RequestBody BroadcastNotificationRequest broadcastRequest,
            Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            List<Notification> notifications = notificationService.broadcastNotification(
                broadcastRequest.getUserIds(),
                broadcastRequest.getTitle(),
                broadcastRequest.getMessage(),
                broadcastRequest.getType(),
                broadcastRequest.getCategory()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Broadcast notification sent successfully", notifications));
            
        } catch (Exception e) {
            log.error("Failed to broadcast notification: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get user notifications with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Notification>>> getUserNotifications(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Notification> notificationPage = notificationService.getUserNotifications(userPrincipal.getId(), pageable);
            PageResponse<Notification> pageResponse = PageResponse.of(notificationPage);
            
            return ResponseEntity.ok(ApiResponse.success("Notifications retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get user notifications: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get unread notifications
     */
    @GetMapping("/unread")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Notification>>> getUnreadNotifications(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<Notification> notificationPage = notificationService.getUnreadNotifications(userPrincipal.getId(), pageable);
            PageResponse<Notification> pageResponse = PageResponse.of(notificationPage);
            
            return ResponseEntity.ok(ApiResponse.success("Unread notifications retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get unread notifications: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get unread notification count
     */
    @GetMapping("/unread-count")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Long>> getUnreadNotificationCount(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            long unreadCount = notificationService.getUnreadNotificationCount(userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Unread notification count retrieved", unreadCount));
            
        } catch (Exception e) {
            log.error("Failed to get unread notification count: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get notification by ID
     */
    @GetMapping("/{notificationId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Notification>> getNotification(@PathVariable String notificationId,
                                                                    Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            // This would need to be implemented in the service to verify ownership
            // For now, we'll just get the notification
            // Notification notification = notificationService.getNotificationById(notificationId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Notification retrieved successfully", null));
            
        } catch (Exception e) {
            log.error("Failed to get notification {}: {}", notificationId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Notification not found"));
        }
    }
    
    /**
     * Mark notification as read
     */
    @PutMapping("/{notificationId}/read")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Notification>> markAsRead(@PathVariable String notificationId,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Notification notification = notificationService.markAsRead(notificationId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Notification marked as read", notification));
            
        } catch (Exception e) {
            log.error("Failed to mark notification as read {}: {}", notificationId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Mark all notifications as read
     */
    @PostMapping("/mark-all-read")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<String>> markAllAsRead(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            notificationService.markAllAsRead(userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("All notifications marked as read"));
            
        } catch (Exception e) {
            log.error("Failed to mark all notifications as read: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete notification
     */
    @DeleteMapping("/{notificationId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<String>> deleteNotification(@PathVariable String notificationId,
                                                                 Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            notificationService.deleteNotification(notificationId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Notification deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete notification {}: {}", notificationId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get notifications by type
     */
    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Notification>>> getNotificationsByType(@PathVariable String type,
                                                                                  Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            List<Notification> notifications = notificationService.getNotificationsByType(userPrincipal.getId(), type);
            
            return ResponseEntity.ok(ApiResponse.success("Notifications retrieved successfully", notifications));
            
        } catch (Exception e) {
            log.error("Failed to get notifications by type {}: {}", type, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get notifications by category
     */
    @GetMapping("/category/{category}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Notification>>> getNotificationsByCategory(@PathVariable String category,
                                                                                      Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            List<Notification> notifications = notificationService.getNotificationsByCategory(userPrincipal.getId(), category);
            
            return ResponseEntity.ok(ApiResponse.success("Notifications retrieved successfully", notifications));
            
        } catch (Exception e) {
            log.error("Failed to get notifications by category {}: {}", category, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get recent notifications
     */
    @GetMapping("/recent")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Notification>>> getRecentNotifications(
            @RequestParam(defaultValue = "24") int hours,
            Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            List<Notification> notifications = notificationService.getRecentNotifications(userPrincipal.getId(), hours);
            
            return ResponseEntity.ok(ApiResponse.success("Recent notifications retrieved successfully", notifications));
            
        } catch (Exception e) {
            log.error("Failed to get recent notifications: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get notification statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<NotificationService.NotificationStats>> getNotificationStatistics(
            Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            NotificationService.NotificationStats stats = notificationService.getNotificationStats(userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Notification statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get notification statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    // Helper DTOs
    @lombok.Data
    public static class BroadcastNotificationRequest {
        private List<String> userIds;
        private String title;
        private String message;
        private String type;
        private String category;
    }
}
