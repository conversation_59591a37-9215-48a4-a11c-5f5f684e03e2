package com.kaydkapro.controller;

import com.kaydkapro.dto.request.UserRequest;
import com.kaydkapro.dto.request.UserUpdateRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.User;
import com.kaydkapro.service.UserService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * User Controller - REST API endpoints for user management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserController {
    
    private final UserService userService;
    
    /**
     * Create a new user
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<User>> createUser(@Valid @RequestBody UserRequest userRequest,
                                                       Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userRequest.toEntity();
            User createdUser = userService.createUser(user, userRequest.getRoleIds(), userPrincipal.getId());
            
            // Remove sensitive information
            createdUser.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("User created successfully", createdUser));
            
        } catch (Exception e) {
            log.error("Failed to create user: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update an existing user
     */
    @PutMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN') or #userId == authentication.principal.id")
    public ResponseEntity<ApiResponse<User>> updateUser(@PathVariable String userId,
                                                       @Valid @RequestBody UserUpdateRequest userUpdateRequest,
                                                       Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User userUpdates = userUpdateRequest.toEntity();
            User updatedUser = userService.updateUser(userId, userUpdates, userPrincipal.getId());
            
            // Remove sensitive information
            updatedUser.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("User updated successfully", updatedUser));
            
        } catch (Exception e) {
            log.error("Failed to update user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get user by ID
     */
    @GetMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or #userId == authentication.principal.id")
    public ResponseEntity<ApiResponse<User>> getUser(@PathVariable String userId) {
        try {
            User user = userService.getUserById(userId);
            
            // Remove sensitive information
            user.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("User retrieved successfully", user));
            
        } catch (Exception e) {
            log.error("Failed to get user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("User not found"));
        }
    }
    
    /**
     * Get all users with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<PageResponse<User>>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "firstName") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<User> userPage = userService.getUsers(pageable);
            
            // Remove sensitive information
            userPage.getContent().forEach(user -> user.setPassword(null));
            
            PageResponse<User> pageResponse = PageResponse.of(userPage);
            
            return ResponseEntity.ok(ApiResponse.success("Users retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get users: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search users
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<PageResponse<User>>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<User> userPage = userService.searchUsers(query, pageable);
            
            // Remove sensitive information
            userPage.getContent().forEach(user -> user.setPassword(null));
            
            PageResponse<User> pageResponse = PageResponse.of(userPage);
            
            return ResponseEntity.ok(ApiResponse.success("User search completed", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to search users: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get users by role
     */
    @GetMapping("/role/{roleName}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<List<User>>> getUsersByRole(@PathVariable String roleName) {
        try {
            List<User> users = userService.getUsersByRole(roleName);
            
            // Remove sensitive information
            users.forEach(user -> user.setPassword(null));
            
            return ResponseEntity.ok(ApiResponse.success("Users retrieved successfully", users));
            
        } catch (Exception e) {
            log.error("Failed to get users by role {}: {}", roleName, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get active users
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<List<User>>> getActiveUsers() {
        try {
            List<User> users = userService.getActiveUsers();
            
            // Remove sensitive information
            users.forEach(user -> user.setPassword(null));
            
            return ResponseEntity.ok(ApiResponse.success("Active users retrieved successfully", users));
            
        } catch (Exception e) {
            log.error("Failed to get active users: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Assign role to user
     */
    @PostMapping("/{userId}/roles/{roleId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<User>> assignRole(@PathVariable String userId,
                                                       @PathVariable String roleId,
                                                       Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userService.assignRole(userId, roleId, userPrincipal.getId());
            
            // Remove sensitive information
            user.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("Role assigned successfully", user));
            
        } catch (Exception e) {
            log.error("Failed to assign role {} to user {}: {}", roleId, userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Remove role from user
     */
    @DeleteMapping("/{userId}/roles/{roleId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<User>> removeRole(@PathVariable String userId,
                                                       @PathVariable String roleId,
                                                       Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userService.removeRole(userId, roleId, userPrincipal.getId());
            
            // Remove sensitive information
            user.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("Role removed successfully", user));
            
        } catch (Exception e) {
            log.error("Failed to remove role {} from user {}: {}", roleId, userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Toggle user status (activate/deactivate)
     */
    @PatchMapping("/{userId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<User>> toggleUserStatus(@PathVariable String userId,
                                                             @RequestParam boolean isActive,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userService.toggleUserStatus(userId, isActive, userPrincipal.getId());
            
            // Remove sensitive information
            user.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("User status updated", user));
            
        } catch (Exception e) {
            log.error("Failed to toggle user status {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update user profile
     */
    @PutMapping("/{userId}/profile")
    @PreAuthorize("#userId == authentication.principal.id")
    public ResponseEntity<ApiResponse<User>> updateProfile(@PathVariable String userId,
                                                          @Valid @RequestBody UserUpdateRequest profileRequest,
                                                          Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User profileUpdates = profileRequest.toEntity();
            User updatedUser = userService.updateUserProfile(userId, profileUpdates, userPrincipal.getId());
            
            // Remove sensitive information
            updatedUser.setPassword(null);
            
            return ResponseEntity.ok(ApiResponse.success("Profile updated successfully", updatedUser));
            
        } catch (Exception e) {
            log.error("Failed to update profile for user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Change password
     */
    @PostMapping("/{userId}/change-password")
    @PreAuthorize("#userId == authentication.principal.id")
    public ResponseEntity<ApiResponse<String>> changePassword(@PathVariable String userId,
                                                             @RequestParam String oldPassword,
                                                             @RequestParam String newPassword,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            userService.changePassword(userId, oldPassword, newPassword, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Password changed successfully"));
            
        } catch (Exception e) {
            log.error("Failed to change password for user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update last login
     */
    @PostMapping("/{userId}/last-login")
    @PreAuthorize("#userId == authentication.principal.id")
    public ResponseEntity<ApiResponse<String>> updateLastLogin(@PathVariable String userId) {
        try {
            userService.updateLastLogin(userId);
            return ResponseEntity.ok(ApiResponse.success("Last login updated"));
            
        } catch (Exception e) {
            log.error("Failed to update last login for user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get user statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserService.UserStats>> getUserStatistics() {
        try {
            UserService.UserStats stats = userService.getUserStats();
            return ResponseEntity.ok(ApiResponse.success("User statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get user statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete user (soft delete)
     */
    @DeleteMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteUser(@PathVariable String userId,
                                                         Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            userService.deleteUser(userId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("User deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete user {}: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
