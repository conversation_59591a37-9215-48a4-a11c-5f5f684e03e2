server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: kaydkapro-backend
  
  # MongoDB Configuration
  data:
    mongodb:
      uri: mongodb://localhost:27017/kaydkapro
      auto-index-creation: true
  
  # Jackson Configuration
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
  
  # Validation Configuration
  validation:
    enabled: true
  
  # Multipart Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  # Cache Configuration
  cache:
    type: simple
    cache-names: users,products,categories,warehouses,suppliers,customers

# Application Configuration
app:
  jwt:
    secret: kaydkapro-secret-key-for-jwt-token-generation-and-validation-2024
    expiration: 86400000 # 24 hours in milliseconds
    refresh-expiration: 604800000 # 7 days in milliseconds
  
  cors:
    allowed-origins: "*"
    allowed-methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
  
  # File Upload Configuration
  upload:
    path: ./uploads
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
  
  # Email Configuration (if needed)
  email:
    enabled: false
    smtp:
      host: smtp.gmail.com
      port: 587
      username: <EMAIL>
      password: your-app-password
      auth: true
      starttls: true
  
  # AI Configuration (if needed)
  ai:
    enabled: false
    provider: openai
    api-key: your-openai-api-key
    model: gpt-3.5-turbo
    max-tokens: 1000

# Logging Configuration
logging:
  level:
    com.kaydkapro: DEBUG
    org.springframework.security: DEBUG
    org.springframework.data.mongodb: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/kaydkapro.log
    max-size: 10MB
    max-history: 30

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# Application Information
info:
  app:
    name: KaydkaPro Backend
    description: Comprehensive Inventory Management System
    version: 1.0.0
    author: KaydkaPro Team

# Development Profile
---
spring:
  config:
    activate:
      on-profile: dev
  
  data:
    mongodb:
      uri: mongodb://localhost:27017/kaydkapro_dev
  
logging:
  level:
    root: INFO
    com.kaydkapro: DEBUG

# Production Profile
---
spring:
  config:
    activate:
      on-profile: prod
  
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/kaydkapro_prod}

app:
  jwt:
    secret: ${JWT_SECRET:kaydkapro-production-secret-key-2024}

logging:
  level:
    root: WARN
    com.kaydkapro: INFO
  file:
    name: /var/log/kaydkapro/application.log

# Test Profile
---
spring:
  config:
    activate:
      on-profile: test
  
  data:
    mongodb:
      uri: mongodb://localhost:27017/kaydkapro_test

logging:
  level:
    root: WARN
    com.kaydkapro: DEBUG
