package com.kaydkapro.service;

import com.kaydkapro.entity.Category;
import com.kaydkapro.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Category Service - Business logic for category management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CategoryService {
    
    private final CategoryRepository categoryRepository;
    private final AuditLogService auditLogService;
    
    /**
     * Create a new category
     */
    public Category createCategory(Category category, String createdByUserId) {
        log.info("Creating new category: {}", category.getName());
        
        // Validate unique name
        if (categoryRepository.existsByName(category.getName())) {
            throw new RuntimeException("Category name already exists: " + category.getName());
        }
        
        // Set sort order
        if (category.getSortOrder() == null) {
            category.setSortOrder(getNextSortOrder(category.getParentId()));
        }
        
        // Set audit fields
        category.setCreatedBy(createdByUserId);
        category.setCreatedAt(LocalDateTime.now());
        category.setIsActive(true);
        
        Category savedCategory = categoryRepository.save(category);
        
        // Log audit
        auditLogService.logUserAction(
            createdByUserId,
            "CREATE",
            "CATEGORY",
            savedCategory.getId(),
            savedCategory.getName()
        );
        
        log.info("Category created successfully: {}", savedCategory.getId());
        return savedCategory;
    }
    
    /**
     * Update existing category
     */
    public Category updateCategory(String categoryId, Category categoryUpdates, String updatedByUserId) {
        log.info("Updating category: {}", categoryId);
        
        Category existingCategory = getCategoryById(categoryId);
        
        // Validate unique name if changed
        if (!existingCategory.getName().equals(categoryUpdates.getName())) {
            if (categoryRepository.existsByNameAndIdNot(categoryUpdates.getName(), categoryId)) {
                throw new RuntimeException("Category name already exists: " + categoryUpdates.getName());
            }
        }
        
        // Store old values for audit
        String oldName = existingCategory.getName();
        
        // Update fields
        existingCategory.setName(categoryUpdates.getName());
        existingCategory.setDescription(categoryUpdates.getDescription());
        existingCategory.setImageUrl(categoryUpdates.getImageUrl());
        existingCategory.setSortOrder(categoryUpdates.getSortOrder());
        existingCategory.setUpdatedBy(updatedByUserId);
        existingCategory.setUpdatedAt(LocalDateTime.now());
        
        Category savedCategory = categoryRepository.save(existingCategory);
        
        // Log audit
        auditLogService.logDataChange(
            updatedByUserId,
            "UPDATE",
            "CATEGORY",
            savedCategory.getId(),
            savedCategory.getName(),
            java.util.Map.of("name", oldName),
            java.util.Map.of("name", savedCategory.getName())
        );
        
        log.info("Category updated successfully: {}", savedCategory.getId());
        return savedCategory;
    }
    
    /**
     * Get category by ID
     */
    @Transactional(readOnly = true)
    public Category getCategoryById(String categoryId) {
        return categoryRepository.findById(categoryId)
                .orElseThrow(() -> new RuntimeException("Category not found with id: " + categoryId));
    }
    
    /**
     * Get all active categories
     */
    @Transactional(readOnly = true)
    public List<Category> getAllActiveCategories() {
        return categoryRepository.findByIsActiveTrue(Sort.by("sortOrder").ascending());
    }
    
    /**
     * Get root categories (no parent)
     */
    @Transactional(readOnly = true)
    public List<Category> getRootCategories() {
        return categoryRepository.findRootCategories(Sort.by("sortOrder").ascending());
    }
    
    /**
     * Get subcategories by parent ID
     */
    @Transactional(readOnly = true)
    public List<Category> getSubcategories(String parentId) {
        return categoryRepository.findByParentId(parentId, Sort.by("sortOrder").ascending());
    }
    
    /**
     * Get category hierarchy (parent and all children)
     */
    @Transactional(readOnly = true)
    public List<Category> getCategoryHierarchy(String categoryId) {
        return categoryRepository.findCategoryHierarchy(categoryId);
    }
    
    /**
     * Search categories
     */
    @Transactional(readOnly = true)
    public List<Category> searchCategories(String searchTerm) {
        return categoryRepository.searchCategories(searchTerm);
    }
    
    /**
     * Move category to different parent
     */
    public Category moveCategory(String categoryId, String newParentId, String updatedByUserId) {
        log.info("Moving category {} to parent {}", categoryId, newParentId);
        
        Category category = getCategoryById(categoryId);
        
        // Validate new parent exists if not null
        if (newParentId != null && !categoryRepository.existsById(newParentId)) {
            throw new RuntimeException("Parent category not found: " + newParentId);
        }
        
        // Prevent circular reference
        if (newParentId != null && isDescendant(categoryId, newParentId)) {
            throw new RuntimeException("Cannot move category to its own descendant");
        }
        
        String oldParentId = category.getParentId();
        category.setParentId(newParentId);
        category.setSortOrder(getNextSortOrder(newParentId));
        category.setUpdatedBy(updatedByUserId);
        category.setUpdatedAt(LocalDateTime.now());
        
        Category savedCategory = categoryRepository.save(category);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            "MOVE",
            "CATEGORY",
            savedCategory.getId(),
            String.format("%s - From: %s To: %s", savedCategory.getName(), oldParentId, newParentId)
        );
        
        log.info("Category moved successfully: {}", savedCategory.getId());
        return savedCategory;
    }
    
    /**
     * Reorder categories
     */
    public void reorderCategories(List<CategoryOrder> categoryOrders, String updatedByUserId) {
        log.info("Reordering {} categories", categoryOrders.size());
        
        for (CategoryOrder order : categoryOrders) {
            Category category = getCategoryById(order.getCategoryId());
            category.setSortOrder(order.getSortOrder());
            category.setUpdatedBy(updatedByUserId);
            category.setUpdatedAt(LocalDateTime.now());
            categoryRepository.save(category);
        }
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            "REORDER",
            "CATEGORY",
            null,
            "Reordered " + categoryOrders.size() + " categories"
        );
        
        log.info("Categories reordered successfully");
    }
    
    /**
     * Activate/Deactivate category
     */
    public Category toggleCategoryStatus(String categoryId, boolean isActive, String updatedByUserId) {
        log.info("Toggling category status: {} to {}", categoryId, isActive);
        
        Category category = getCategoryById(categoryId);
        
        // If deactivating, check if it has active subcategories
        if (!isActive && categoryRepository.countByParentId(categoryId) > 0) {
            throw new RuntimeException("Cannot deactivate category with active subcategories");
        }
        
        category.setIsActive(isActive);
        category.setUpdatedBy(updatedByUserId);
        category.setUpdatedAt(LocalDateTime.now());
        
        Category savedCategory = categoryRepository.save(category);
        
        // Log audit
        auditLogService.logUserAction(
            updatedByUserId,
            isActive ? "ACTIVATE" : "DEACTIVATE",
            "CATEGORY",
            savedCategory.getId(),
            savedCategory.getName()
        );
        
        log.info("Category status updated: {} - {}", savedCategory.getId(), isActive);
        return savedCategory;
    }
    
    /**
     * Delete category (soft delete)
     */
    public void deleteCategory(String categoryId, String deletedByUserId) {
        log.info("Deleting category: {}", categoryId);
        
        Category category = getCategoryById(categoryId);
        
        // Check if category has subcategories
        if (categoryRepository.countByParentId(categoryId) > 0) {
            throw new RuntimeException("Cannot delete category with subcategories");
        }
        
        // Check if category has products (would need ProductRepository)
        // if (productRepository.countByCategoryId(categoryId) > 0) {
        //     throw new RuntimeException("Cannot delete category with products");
        // }
        
        category.setIsActive(false);
        category.setUpdatedBy(deletedByUserId);
        category.setUpdatedAt(LocalDateTime.now());
        
        categoryRepository.save(category);
        
        // Log audit
        auditLogService.logUserAction(
            deletedByUserId,
            "DELETE",
            "CATEGORY",
            category.getId(),
            category.getName()
        );
        
        log.info("Category deleted successfully: {}", categoryId);
    }
    
    /**
     * Get category statistics
     */
    @Transactional(readOnly = true)
    public CategoryStats getCategoryStats() {
        long totalCategories = categoryRepository.count();
        long activeCategories = categoryRepository.countByIsActiveTrue();
        long rootCategories = categoryRepository.countRootCategories();
        long categoriesWithImages = categoryRepository.findCategoriesWithImages().size();
        
        return CategoryStats.builder()
                .totalCategories(totalCategories)
                .activeCategories(activeCategories)
                .inactiveCategories(totalCategories - activeCategories)
                .rootCategories(rootCategories)
                .subcategories(activeCategories - rootCategories)
                .categoriesWithImages(categoriesWithImages)
                .build();
    }
    
    // Helper methods
    private Integer getNextSortOrder(String parentId) {
        if (parentId == null) {
            return categoryRepository.findTopBySortOrderDesc()
                    .map(category -> category.getSortOrder() + 1)
                    .orElse(1);
        } else {
            return categoryRepository.findTopByParentIdOrderBySortOrderDesc(parentId)
                    .map(category -> category.getSortOrder() + 1)
                    .orElse(1);
        }
    }
    
    private boolean isDescendant(String ancestorId, String descendantId) {
        Category descendant = categoryRepository.findById(descendantId).orElse(null);
        
        while (descendant != null && descendant.getParentId() != null) {
            if (descendant.getParentId().equals(ancestorId)) {
                return true;
            }
            descendant = categoryRepository.findById(descendant.getParentId()).orElse(null);
        }
        
        return false;
    }
    
    /**
     * Category order DTO for reordering
     */
    @lombok.Data
    @lombok.Builder
    public static class CategoryOrder {
        private String categoryId;
        private Integer sortOrder;
    }
    
    /**
     * Category statistics DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class CategoryStats {
        private long totalCategories;
        private long activeCategories;
        private long inactiveCategories;
        private long rootCategories;
        private long subcategories;
        private long categoriesWithImages;
    }
}
