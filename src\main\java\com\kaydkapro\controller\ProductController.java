package com.kaydkapro.controller;

import com.kaydkapro.dto.request.ProductRequest;
import com.kaydkapro.dto.response.ApiResponse;
import com.kaydkapro.dto.response.PageResponse;
import com.kaydkapro.entity.Product;
import com.kaydkapro.service.ProductService;
import com.kaydkapro.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * Product Controller - REST API endpoints for product management
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/products")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class ProductController {
    
    private final ProductService productService;
    
    /**
     * Create a new product
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Product>> createProduct(@Valid @RequestBody ProductRequest productRequest,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Product product = productRequest.toEntity();
            Product createdProduct = productService.createProduct(product, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Product created successfully", createdProduct));
            
        } catch (Exception e) {
            log.error("Failed to create product: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Update an existing product
     */
    @PutMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Product>> updateProduct(@PathVariable String productId,
                                                             @Valid @RequestBody ProductRequest productRequest,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Product productUpdates = productRequest.toEntity();
            Product updatedProduct = productService.updateProduct(productId, productUpdates, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Product updated successfully", updatedProduct));
            
        } catch (Exception e) {
            log.error("Failed to update product {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get product by ID
     */
    @GetMapping("/{productId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Product>> getProduct(@PathVariable String productId) {
        try {
            Product product = productService.getProductById(productId);
            return ResponseEntity.ok(ApiResponse.success("Product retrieved successfully", product));
            
        } catch (Exception e) {
            log.error("Failed to get product {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Product not found"));
        }
    }
    
    /**
     * Get product by SKU
     */
    @GetMapping("/sku/{sku}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Product>> getProductBySku(@PathVariable String sku) {
        try {
            Product product = productService.getProductBySku(sku);
            return ResponseEntity.ok(ApiResponse.success("Product retrieved successfully", product));
            
        } catch (Exception e) {
            log.error("Failed to get product by SKU {}: {}", sku, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Product not found"));
        }
    }
    
    /**
     * Get product by barcode
     */
    @GetMapping("/barcode/{barcode}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Product>> getProductByBarcode(@PathVariable String barcode) {
        try {
            Product product = productService.getProductByBarcode(barcode);
            return ResponseEntity.ok(ApiResponse.success("Product retrieved successfully", product));
            
        } catch (Exception e) {
            log.error("Failed to get product by barcode {}: {}", barcode, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.notFound("Product not found"));
        }
    }
    
    /**
     * Get all products with pagination
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Product>>> getProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Product> productPage = productService.getProducts(pageable);
            PageResponse<Product> pageResponse = PageResponse.of(productPage);
            
            return ResponseEntity.ok(ApiResponse.success("Products retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get products: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Search products
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Product>>> searchProducts(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Product> productPage = productService.searchProducts(query, pageable);
            PageResponse<Product> pageResponse = PageResponse.of(productPage);
            
            return ResponseEntity.ok(ApiResponse.success("Products search completed", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to search products: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get products by category
     */
    @GetMapping("/category/{categoryId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResponse<Product>>> getProductsByCategory(
            @PathVariable String categoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Product> productPage = productService.getProductsByCategory(categoryId, pageable);
            PageResponse<Product> pageResponse = PageResponse.of(productPage);
            
            return ResponseEntity.ok(ApiResponse.success("Products retrieved successfully", pageResponse));
            
        } catch (Exception e) {
            log.error("Failed to get products by category {}: {}", categoryId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get products by price range
     */
    @GetMapping("/price-range")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Product>>> getProductsByPriceRange(
            @RequestParam BigDecimal minPrice,
            @RequestParam BigDecimal maxPrice) {
        try {
            List<Product> products = productService.getProductsByPriceRange(minPrice, maxPrice);
            return ResponseEntity.ok(ApiResponse.success("Products retrieved successfully", products));
            
        } catch (Exception e) {
            log.error("Failed to get products by price range: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get low stock products
     */
    @GetMapping("/low-stock")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Product>>> getLowStockProducts() {
        try {
            List<Product> products = productService.getLowStockProducts();
            return ResponseEntity.ok(ApiResponse.success("Low stock products retrieved", products));
            
        } catch (Exception e) {
            log.error("Failed to get low stock products: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get top selling products
     */
    @GetMapping("/top-selling")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Product>>> getTopSellingProducts(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            List<Product> products = productService.getTopSellingProducts(limit);
            return ResponseEntity.ok(ApiResponse.success("Top selling products retrieved", products));
            
        } catch (Exception e) {
            log.error("Failed to get top selling products: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Add image to product
     */
    @PostMapping("/{productId}/images")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Product>> addProductImage(@PathVariable String productId,
                                                               @RequestParam String imageUrl,
                                                               @RequestParam(defaultValue = "false") boolean isPrimary,
                                                               Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Product product = productService.addProductImage(productId, imageUrl, isPrimary, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Image added successfully", product));
            
        } catch (Exception e) {
            log.error("Failed to add image to product {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Add tag to product
     */
    @PostMapping("/{productId}/tags")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Product>> addProductTag(@PathVariable String productId,
                                                             @RequestParam String tag,
                                                             Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Product product = productService.addTagToProduct(productId, tag, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Tag added successfully", product));
            
        } catch (Exception e) {
            log.error("Failed to add tag to product {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Toggle product status (activate/deactivate)
     */
    @PatchMapping("/{productId}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<ApiResponse<Product>> toggleProductStatus(@PathVariable String productId,
                                                                   @RequestParam boolean isActive,
                                                                   Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Product product = productService.toggleProductStatus(productId, isActive, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Product status updated", product));
            
        } catch (Exception e) {
            log.error("Failed to toggle product status {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Delete product (soft delete)
     */
    @DeleteMapping("/{productId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteProduct(@PathVariable String productId,
                                                            Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            productService.deleteProduct(productId, userPrincipal.getId());
            
            return ResponseEntity.ok(ApiResponse.success("Product deleted successfully"));
            
        } catch (Exception e) {
            log.error("Failed to delete product {}: {}", productId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
    
    /**
     * Get product statistics
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('MANAGER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<ProductService.ProductStats>> getProductStatistics() {
        try {
            ProductService.ProductStats stats = productService.getProductStats();
            return ResponseEntity.ok(ApiResponse.success("Product statistics retrieved", stats));
            
        } catch (Exception e) {
            log.error("Failed to get product statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(e.getMessage()));
        }
    }
}
